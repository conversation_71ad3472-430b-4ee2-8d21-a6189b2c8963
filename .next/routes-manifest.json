{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/academic", "regex": "^/academic(?:/)?$", "routeKeys": {}, "namedRegex": "^/academic(?:/)?$"}, {"page": "/academic/assessments", "regex": "^/academic/assessments(?:/)?$", "routeKeys": {}, "namedRegex": "^/academic/assessments(?:/)?$"}, {"page": "/academic/attendance", "regex": "^/academic/attendance(?:/)?$", "routeKeys": {}, "namedRegex": "^/academic/attendance(?:/)?$"}, {"page": "/academic/curriculum", "regex": "^/academic/curriculum(?:/)?$", "routeKeys": {}, "namedRegex": "^/academic/curriculum(?:/)?$"}, {"page": "/academic/grades", "regex": "^/academic/grades(?:/)?$", "routeKeys": {}, "namedRegex": "^/academic/grades(?:/)?$"}, {"page": "/academic/progress", "regex": "^/academic/progress(?:/)?$", "routeKeys": {}, "namedRegex": "^/academic/progress(?:/)?$"}, {"page": "/academic/schedule", "regex": "^/academic/schedule(?:/)?$", "routeKeys": {}, "namedRegex": "^/academic/schedule(?:/)?$"}, {"page": "/admissions", "regex": "^/admissions(?:/)?$", "routeKeys": {}, "namedRegex": "^/admissions(?:/)?$"}, {"page": "/admissions/applications", "regex": "^/admissions/applications(?:/)?$", "routeKeys": {}, "namedRegex": "^/admissions/applications(?:/)?$"}, {"page": "/admissions/applications/new", "regex": "^/admissions/applications/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admissions/applications/new(?:/)?$"}, {"page": "/admissions/classes", "regex": "^/admissions/classes(?:/)?$", "routeKeys": {}, "namedRegex": "^/admissions/classes(?:/)?$"}, {"page": "/admissions/decisions", "regex": "^/admissions/decisions(?:/)?$", "routeKeys": {}, "namedRegex": "^/admissions/decisions(?:/)?$"}, {"page": "/admissions/enrollment", "regex": "^/admissions/enrollment(?:/)?$", "routeKeys": {}, "namedRegex": "^/admissions/enrollment(?:/)?$"}, {"page": "/admissions/interviews", "regex": "^/admissions/interviews(?:/)?$", "routeKeys": {}, "namedRegex": "^/admissions/interviews(?:/)?$"}, {"page": "/admissions/reports", "regex": "^/admissions/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/admissions/reports(?:/)?$"}, {"page": "/alumni", "regex": "^/alumni(?:/)?$", "routeKeys": {}, "namedRegex": "^/alumni(?:/)?$"}, {"page": "/alumni/achievements", "regex": "^/alumni/achievements(?:/)?$", "routeKeys": {}, "namedRegex": "^/alumni/achievements(?:/)?$"}, {"page": "/alumni/directory", "regex": "^/alumni/directory(?:/)?$", "routeKeys": {}, "namedRegex": "^/alumni/directory(?:/)?$"}, {"page": "/alumni/donations", "regex": "^/alumni/donations(?:/)?$", "routeKeys": {}, "namedRegex": "^/alumni/donations(?:/)?$"}, {"page": "/alumni/events", "regex": "^/alumni/events(?:/)?$", "routeKeys": {}, "namedRegex": "^/alumni/events(?:/)?$"}, {"page": "/alumni/jobs", "regex": "^/alumni/jobs(?:/)?$", "routeKeys": {}, "namedRegex": "^/alumni/jobs(?:/)?$"}, {"page": "/alumni/networking", "regex": "^/alumni/networking(?:/)?$", "routeKeys": {}, "namedRegex": "^/alumni/networking(?:/)?$"}, {"page": "/auth/callback", "regex": "^/auth/callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/callback(?:/)?$"}, {"page": "/auth/login", "regex": "^/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/academic", "regex": "^/dashboard/academic(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/academic(?:/)?$"}, {"page": "/dashboard/academic/assessments", "regex": "^/dashboard/academic/assessments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/academic/assessments(?:/)?$"}, {"page": "/dashboard/academic/attendance", "regex": "^/dashboard/academic/attendance(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/academic/attendance(?:/)?$"}, {"page": "/dashboard/academic/curriculum", "regex": "^/dashboard/academic/curriculum(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/academic/curriculum(?:/)?$"}, {"page": "/dashboard/academic/grades", "regex": "^/dashboard/academic/grades(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/academic/grades(?:/)?$"}, {"page": "/dashboard/academic/progress", "regex": "^/dashboard/academic/progress(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/academic/progress(?:/)?$"}, {"page": "/dashboard/academic/schedule", "regex": "^/dashboard/academic/schedule(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/academic/schedule(?:/)?$"}, {"page": "/dashboard/admissions", "regex": "^/dashboard/admissions(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admissions(?:/)?$"}, {"page": "/dashboard/admissions/applications", "regex": "^/dashboard/admissions/applications(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admissions/applications(?:/)?$"}, {"page": "/dashboard/admissions/classes", "regex": "^/dashboard/admissions/classes(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admissions/classes(?:/)?$"}, {"page": "/dashboard/admissions/decisions", "regex": "^/dashboard/admissions/decisions(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admissions/decisions(?:/)?$"}, {"page": "/dashboard/admissions/enrollment", "regex": "^/dashboard/admissions/enrollment(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admissions/enrollment(?:/)?$"}, {"page": "/dashboard/admissions/interviews", "regex": "^/dashboard/admissions/interviews(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admissions/interviews(?:/)?$"}, {"page": "/dashboard/admissions/reports", "regex": "^/dashboard/admissions/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admissions/reports(?:/)?$"}, {"page": "/dashboard/alumni", "regex": "^/dashboard/alumni(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/alumni(?:/)?$"}, {"page": "/dashboard/alumni/directory", "regex": "^/dashboard/alumni/directory(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/alumni/directory(?:/)?$"}, {"page": "/dashboard/alumni/events", "regex": "^/dashboard/alumni/events(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/alumni/events(?:/)?$"}, {"page": "/dashboard/alumni/jobs", "regex": "^/dashboard/alumni/jobs(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/alumni/jobs(?:/)?$"}, {"page": "/dashboard/financials", "regex": "^/dashboard/financials(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/financials(?:/)?$"}, {"page": "/dashboard/financials/billing", "regex": "^/dashboard/financials/billing(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/financials/billing(?:/)?$"}, {"page": "/dashboard/financials/fee-structure", "regex": "^/dashboard/financials/fee\\-structure(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/financials/fee\\-structure(?:/)?$"}, {"page": "/dashboard/financials/financial-aid", "regex": "^/dashboard/financials/financial\\-aid(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/financials/financial\\-aid(?:/)?$"}, {"page": "/dashboard/financials/history", "regex": "^/dashboard/financials/history(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/financials/history(?:/)?$"}, {"page": "/dashboard/financials/payments", "regex": "^/dashboard/financials/payments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/financials/payments(?:/)?$"}, {"page": "/dashboard/financials/reports", "regex": "^/dashboard/financials/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/financials/reports(?:/)?$"}, {"page": "/dashboard/hostel", "regex": "^/dashboard/hostel(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/hostel(?:/)?$"}, {"page": "/dashboard/hostel/billing", "regex": "^/dashboard/hostel/billing(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/hostel/billing(?:/)?$"}, {"page": "/dashboard/hostel/facilities", "regex": "^/dashboard/hostel/facilities(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/hostel/facilities(?:/)?$"}, {"page": "/dashboard/hostel/maintenance", "regex": "^/dashboard/hostel/maintenance(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/hostel/maintenance(?:/)?$"}, {"page": "/dashboard/hostel/residents", "regex": "^/dashboard/hostel/residents(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/hostel/residents(?:/)?$"}, {"page": "/dashboard/hostel/rooms", "regex": "^/dashboard/hostel/rooms(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/hostel/rooms(?:/)?$"}, {"page": "/dashboard/hostel/visitors", "regex": "^/dashboard/hostel/visitors(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/hostel/visitors(?:/)?$"}, {"page": "/dashboard/library", "regex": "^/dashboard/library(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/library(?:/)?$"}, {"page": "/dashboard/library/catalog", "regex": "^/dashboard/library/catalog(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/library/catalog(?:/)?$"}, {"page": "/dashboard/library/digital", "regex": "^/dashboard/library/digital(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/library/digital(?:/)?$"}, {"page": "/dashboard/library/inventory", "regex": "^/dashboard/library/inventory(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/library/inventory(?:/)?$"}, {"page": "/dashboard/library/members", "regex": "^/dashboard/library/members(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/library/members(?:/)?$"}, {"page": "/dashboard/teachers", "regex": "^/dashboard/teachers(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teachers(?:/)?$"}, {"page": "/dashboard/teachers/evaluations", "regex": "^/dashboard/teachers/evaluations(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teachers/evaluations(?:/)?$"}, {"page": "/dashboard/teachers/profiles", "regex": "^/dashboard/teachers/profiles(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teachers/profiles(?:/)?$"}, {"page": "/dashboard/teachers/schedules", "regex": "^/dashboard/teachers/schedules(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teachers/schedules(?:/)?$"}, {"page": "/dashboard/teachers/training", "regex": "^/dashboard/teachers/training(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teachers/training(?:/)?$"}, {"page": "/dashboard/transport", "regex": "^/dashboard/transport(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/transport(?:/)?$"}, {"page": "/dashboard/transport/drivers", "regex": "^/dashboard/transport/drivers(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/transport/drivers(?:/)?$"}, {"page": "/dashboard/transport/routes", "regex": "^/dashboard/transport/routes(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/transport/routes(?:/)?$"}, {"page": "/dashboard/transport/safety", "regex": "^/dashboard/transport/safety(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/transport/safety(?:/)?$"}, {"page": "/dashboard/transport/students", "regex": "^/dashboard/transport/students(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/transport/students(?:/)?$"}, {"page": "/dashboard/transport/vehicles", "regex": "^/dashboard/transport/vehicles(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/transport/vehicles(?:/)?$"}, {"page": "/financials", "regex": "^/financials(?:/)?$", "routeKeys": {}, "namedRegex": "^/financials(?:/)?$"}, {"page": "/financials/billing", "regex": "^/financials/billing(?:/)?$", "routeKeys": {}, "namedRegex": "^/financials/billing(?:/)?$"}, {"page": "/financials/fee-structure", "regex": "^/financials/fee\\-structure(?:/)?$", "routeKeys": {}, "namedRegex": "^/financials/fee\\-structure(?:/)?$"}, {"page": "/financials/financial-aid", "regex": "^/financials/financial\\-aid(?:/)?$", "routeKeys": {}, "namedRegex": "^/financials/financial\\-aid(?:/)?$"}, {"page": "/financials/history", "regex": "^/financials/history(?:/)?$", "routeKeys": {}, "namedRegex": "^/financials/history(?:/)?$"}, {"page": "/financials/payments", "regex": "^/financials/payments(?:/)?$", "routeKeys": {}, "namedRegex": "^/financials/payments(?:/)?$"}, {"page": "/financials/reports", "regex": "^/financials/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/financials/reports(?:/)?$"}, {"page": "/hostel", "regex": "^/hostel(?:/)?$", "routeKeys": {}, "namedRegex": "^/hostel(?:/)?$"}, {"page": "/hostel/facilities", "regex": "^/hostel/facilities(?:/)?$", "routeKeys": {}, "namedRegex": "^/hostel/facilities(?:/)?$"}, {"page": "/hostel/maintenance", "regex": "^/hostel/maintenance(?:/)?$", "routeKeys": {}, "namedRegex": "^/hostel/maintenance(?:/)?$"}, {"page": "/hostel/residents", "regex": "^/hostel/residents(?:/)?$", "routeKeys": {}, "namedRegex": "^/hostel/residents(?:/)?$"}, {"page": "/hostel/rooms", "regex": "^/hostel/rooms(?:/)?$", "routeKeys": {}, "namedRegex": "^/hostel/rooms(?:/)?$"}, {"page": "/hostel/visitors", "regex": "^/hostel/visitors(?:/)?$", "routeKeys": {}, "namedRegex": "^/hostel/visitors(?:/)?$"}, {"page": "/library", "regex": "^/library(?:/)?$", "routeKeys": {}, "namedRegex": "^/library(?:/)?$"}, {"page": "/library/catalog", "regex": "^/library/catalog(?:/)?$", "routeKeys": {}, "namedRegex": "^/library/catalog(?:/)?$"}, {"page": "/library/circulation", "regex": "^/library/circulation(?:/)?$", "routeKeys": {}, "namedRegex": "^/library/circulation(?:/)?$"}, {"page": "/library/digital", "regex": "^/library/digital(?:/)?$", "routeKeys": {}, "namedRegex": "^/library/digital(?:/)?$"}, {"page": "/library/inventory", "regex": "^/library/inventory(?:/)?$", "routeKeys": {}, "namedRegex": "^/library/inventory(?:/)?$"}, {"page": "/library/members", "regex": "^/library/members(?:/)?$", "routeKeys": {}, "namedRegex": "^/library/members(?:/)?$"}, {"page": "/library/reservations", "regex": "^/library/reservations(?:/)?$", "routeKeys": {}, "namedRegex": "^/library/reservations(?:/)?$"}, {"page": "/test", "regex": "^/test(?:/)?$", "routeKeys": {}, "namedRegex": "^/test(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}