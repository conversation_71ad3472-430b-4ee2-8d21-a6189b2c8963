{"/_not-found": "app/_not-found.js", "/academic/assessments/page": "app/academic/assessments/page.js", "/academic/curriculum/page": "app/academic/curriculum/page.js", "/academic/attendance/page": "app/academic/attendance/page.js", "/academic/grades/page": "app/academic/grades/page.js", "/academic/progress/page": "app/academic/progress/page.js", "/academic/schedule/page": "app/academic/schedule/page.js", "/admissions/applications/page": "app/admissions/applications/page.js", "/admissions/classes/page": "app/admissions/classes/page.js", "/admissions/decisions/page": "app/admissions/decisions/page.js", "/academic/page": "app/academic/page.js", "/admissions/enrollment/page": "app/admissions/enrollment/page.js", "/admissions/page": "app/admissions/page.js", "/admissions/interviews/page": "app/admissions/interviews/page.js", "/admissions/reports/page": "app/admissions/reports/page.js", "/alumni/achievements/page": "app/alumni/achievements/page.js", "/alumni/directory/page": "app/alumni/directory/page.js", "/alumni/donations/page": "app/alumni/donations/page.js", "/alumni/events/page": "app/alumni/events/page.js", "/alumni/jobs/page": "app/alumni/jobs/page.js", "/alumni/networking/page": "app/alumni/networking/page.js", "/alumni/page": "app/alumni/page.js", "/api/health/route": "app/api/health/route.js", "/admissions/applications/new/page": "app/admissions/applications/new/page.js", "/auth/callback/route": "app/auth/callback/route.js", "/auth/login/page": "app/auth/login/page.js", "/financials/billing/page": "app/financials/billing/page.js", "/financials/fee-structure/page": "app/financials/fee-structure/page.js", "/financials/financial-aid/page": "app/financials/financial-aid/page.js", "/financials/page": "app/financials/page.js", "/financials/payments/page": "app/financials/payments/page.js", "/financials/reports/page": "app/financials/reports/page.js", "/hostel/facilities/page": "app/hostel/facilities/page.js", "/hostel/maintenance/page": "app/hostel/maintenance/page.js", "/hostel/page": "app/hostel/page.js", "/hostel/residents/page": "app/hostel/residents/page.js", "/hostel/rooms/page": "app/hostel/rooms/page.js", "/hostel/visitors/page": "app/hostel/visitors/page.js", "/library/catalog/page": "app/library/catalog/page.js", "/library/digital/page": "app/library/digital/page.js", "/library/circulation/page": "app/library/circulation/page.js", "/library/inventory/page": "app/library/inventory/page.js", "/library/members/page": "app/library/members/page.js", "/library/reservations/page": "app/library/reservations/page.js", "/page": "app/page.js", "/test/page": "app/test/page.js", "/library/page": "app/library/page.js", "/financials/history/page": "app/financials/history/page.js", "/dashboard/academic/assessments/page": "app/dashboard/academic/assessments/page.js", "/dashboard/academic/attendance/page": "app/dashboard/academic/attendance/page.js", "/dashboard/academic/curriculum/page": "app/dashboard/academic/curriculum/page.js", "/dashboard/academic/grades/page": "app/dashboard/academic/grades/page.js", "/dashboard/academic/page": "app/dashboard/academic/page.js", "/dashboard/academic/progress/page": "app/dashboard/academic/progress/page.js", "/dashboard/admissions/applications/page": "app/dashboard/admissions/applications/page.js", "/dashboard/academic/schedule/page": "app/dashboard/academic/schedule/page.js", "/dashboard/admissions/classes/page": "app/dashboard/admissions/classes/page.js", "/dashboard/admissions/decisions/page": "app/dashboard/admissions/decisions/page.js", "/dashboard/admissions/enrollment/page": "app/dashboard/admissions/enrollment/page.js", "/dashboard/admissions/interviews/page": "app/dashboard/admissions/interviews/page.js", "/dashboard/admissions/page": "app/dashboard/admissions/page.js", "/dashboard/admissions/reports/page": "app/dashboard/admissions/reports/page.js", "/dashboard/alumni/directory/page": "app/dashboard/alumni/directory/page.js", "/dashboard/alumni/events/page": "app/dashboard/alumni/events/page.js", "/dashboard/alumni/jobs/page": "app/dashboard/alumni/jobs/page.js", "/dashboard/alumni/page": "app/dashboard/alumni/page.js", "/dashboard/financials/billing/page": "app/dashboard/financials/billing/page.js", "/dashboard/financials/fee-structure/page": "app/dashboard/financials/fee-structure/page.js", "/dashboard/financials/payments/page": "app/dashboard/financials/payments/page.js", "/dashboard/financials/page": "app/dashboard/financials/page.js", "/dashboard/financials/history/page": "app/dashboard/financials/history/page.js", "/dashboard/hostel/billing/page": "app/dashboard/hostel/billing/page.js", "/dashboard/financials/financial-aid/page": "app/dashboard/financials/financial-aid/page.js", "/dashboard/hostel/page": "app/dashboard/hostel/page.js", "/dashboard/hostel/maintenance/page": "app/dashboard/hostel/maintenance/page.js", "/dashboard/hostel/residents/page": "app/dashboard/hostel/residents/page.js", "/dashboard/library/catalog/page": "app/dashboard/library/catalog/page.js", "/dashboard/hostel/visitors/page": "app/dashboard/hostel/visitors/page.js", "/dashboard/library/digital/page": "app/dashboard/library/digital/page.js", "/dashboard/library/inventory/page": "app/dashboard/library/inventory/page.js", "/dashboard/library/page": "app/dashboard/library/page.js", "/dashboard/library/members/page": "app/dashboard/library/members/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/teachers/page": "app/dashboard/teachers/page.js", "/dashboard/teachers/evaluations/page": "app/dashboard/teachers/evaluations/page.js", "/dashboard/teachers/profiles/page": "app/dashboard/teachers/profiles/page.js", "/dashboard/teachers/schedules/page": "app/dashboard/teachers/schedules/page.js", "/dashboard/teachers/training/page": "app/dashboard/teachers/training/page.js", "/dashboard/hostel/facilities/page": "app/dashboard/hostel/facilities/page.js", "/dashboard/transport/drivers/page": "app/dashboard/transport/drivers/page.js", "/dashboard/transport/routes/page": "app/dashboard/transport/routes/page.js", "/dashboard/transport/safety/page": "app/dashboard/transport/safety/page.js", "/dashboard/transport/vehicles/page": "app/dashboard/transport/vehicles/page.js", "/dashboard/hostel/rooms/page": "app/dashboard/hostel/rooms/page.js", "/dashboard/financials/reports/page": "app/dashboard/financials/reports/page.js", "/dashboard/transport/students/page": "app/dashboard/transport/students/page.js", "/dashboard/transport/page": "app/dashboard/transport/page.js"}