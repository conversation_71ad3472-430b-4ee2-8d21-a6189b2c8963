<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/8438-6595e6dae933290d.js" async=""></script><script src="/_next/static/chunks/app/admissions/classes/page-e316b6c951b557ba.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><button class="text-gray-600 hover:text-gray-800">← Back</button><div><h1 class="text-2xl font-bold text-gray-900">Class Allocation</h1><p class="text-gray-600">Allocate students to classes and divisions</p></div></div><div class="flex items-center space-x-2"><select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Grades</option><option value="Grade 9">Grade 9</option><option value="Grade 10">Grade 10</option><option value="Grade 11">Grade 11</option><option value="Grade 12">Grade 12</option></select></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Students Pending Allocation</h3><p class="text-sm text-muted-foreground">Students who passed interviews and need class allocation</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between p-4 border rounded-lg"><div class="flex items-center space-x-4"><div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"><span class="text-blue-600 font-medium">JS</span></div><div><h3 class="font-medium">John Smith</h3><p class="text-sm text-gray-600">APP-2024-001</p><p class="text-sm text-gray-600">Grade 9<!-- --> - <!-- -->Science<!-- --> | Score: <!-- -->85<!-- -->/100</p></div></div><div class="flex items-center space-x-3"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">INTERVIEW PASSED</span><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Allocate Class</button></div></div><div class="flex items-center justify-between p-4 border rounded-lg"><div class="flex items-center space-x-4"><div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"><span class="text-blue-600 font-medium">SJ</span></div><div><h3 class="font-medium">Sarah Johnson</h3><p class="text-sm text-gray-600">APP-2024-002</p><p class="text-sm text-gray-600">Grade 10<!-- --> - <!-- -->Arts<!-- --> | Score: <!-- -->78<!-- -->/100</p></div></div><div class="flex items-center space-x-3"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">INTERVIEW PASSED</span><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Allocate Class</button></div></div><div class="flex items-center justify-between p-4 border rounded-lg"><div class="flex items-center space-x-4"><div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"><span class="text-blue-600 font-medium">MB</span></div><div><h3 class="font-medium">Michael Brown</h3><p class="text-sm text-gray-600">APP-2024-003</p><p class="text-sm text-gray-600">Grade 11<!-- --> - <!-- -->Science<!-- --> | Score: <!-- -->92<!-- -->/100</p></div></div><div class="flex items-center space-x-3"><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">PENDING ALLOCATION</span><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Allocate Class</button></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Class Availability Overview</h3><p class="text-sm text-muted-foreground">Current capacity and availability in all classes</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><h3 class="font-medium">Grade 9<!-- --> - Division <!-- -->A</h3><span class="text-sm font-medium text-yellow-600">5<!-- --> available</span></div><div class="space-y-2 text-sm"><div class="flex justify-between"><span>Current Strength:</span><span>35<!-- -->/<!-- -->40</span></div><div class="flex justify-between"><span>Class Teacher:</span><span>Mrs. Sarah Wilson</span></div><div class="flex justify-between"><span>Stream:</span><span>Science</span></div></div><div class="mt-3"><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:87.5%"></div></div><div class="text-xs text-gray-500 mt-1">88<!-- -->% occupied</div></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><h3 class="font-medium">Grade 9<!-- --> - Division <!-- -->B</h3><span class="text-sm font-medium text-red-600">2<!-- --> available</span></div><div class="space-y-2 text-sm"><div class="flex justify-between"><span>Current Strength:</span><span>38<!-- -->/<!-- -->40</span></div><div class="flex justify-between"><span>Class Teacher:</span><span>Mr. John Davis</span></div><div class="flex justify-between"><span>Stream:</span><span>Science</span></div></div><div class="mt-3"><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:95%"></div></div><div class="text-xs text-gray-500 mt-1">95<!-- -->% occupied</div></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><h3 class="font-medium">Grade 10<!-- --> - Division <!-- -->A</h3><span class="text-sm font-medium text-red-600">3<!-- --> available</span></div><div class="space-y-2 text-sm"><div class="flex justify-between"><span>Current Strength:</span><span>32<!-- -->/<!-- -->35</span></div><div class="flex justify-between"><span>Class Teacher:</span><span>Dr. Emily Brown</span></div><div class="flex justify-between"><span>Stream:</span><span>Science</span></div></div><div class="mt-3"><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:91.42857142857143%"></div></div><div class="text-xs text-gray-500 mt-1">91<!-- -->% occupied</div></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><h3 class="font-medium">Grade 10<!-- --> - Division <!-- -->B</h3><span class="text-sm font-medium text-yellow-600">5<!-- --> available</span></div><div class="space-y-2 text-sm"><div class="flex justify-between"><span>Current Strength:</span><span>30<!-- -->/<!-- -->35</span></div><div class="flex justify-between"><span>Class Teacher:</span><span>Ms. Lisa Garcia</span></div><div class="flex justify-between"><span>Stream:</span><span>Arts</span></div></div><div class="mt-3"><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:85.71428571428571%"></div></div><div class="text-xs text-gray-500 mt-1">86<!-- -->% occupied</div></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><h3 class="font-medium">Grade 11<!-- --> - Division <!-- -->A</h3><span class="text-sm font-medium text-red-600">2<!-- --> available</span></div><div class="space-y-2 text-sm"><div class="flex justify-between"><span>Current Strength:</span><span>28<!-- -->/<!-- -->30</span></div><div class="flex justify-between"><span>Class Teacher:</span><span>Mr. David Miller</span></div><div class="flex justify-between"><span>Stream:</span><span>Science</span></div></div><div class="mt-3"><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:93.33333333333333%"></div></div><div class="text-xs text-gray-500 mt-1">93<!-- -->% occupied</div></div></div></div></div></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[5667,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"8438\",\"static/chunks/8438-6595e6dae933290d.js\",\"5512\",\"static/chunks/app/admissions/classes/page-e316b6c951b557ba.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\nb:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"Tn7xWRdXVWNLs4FpbaknK\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/admissions/classes\",\"initialTree\":[\"\",{\"children\":[\"admissions\",{\"children\":[\"classes\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"admissions\",{\"children\":[\"classes\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"admissions\",\"children\",\"classes\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"admissions\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$La\"],\"globalErrorComponent\":\"$b\"}]]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>