(()=>{var e={};e.id=8331,e.ids=[8331],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},85477:e=>{"use strict";e.exports=require("punycode")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},57310:e=>{"use strict";e.exports=require("url")},59796:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},97529:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var n=t(50482),o=t(69108),s=t(62563),i=t.n(s),a=t(68300),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d=["",{children:["admissions",{children:["applications",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,22106)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/admissions/applications/new/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/admissions/applications/new/page.tsx"],u="/admissions/applications/new/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/admissions/applications/new/page",pathname:"/admissions/applications/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89226:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},74117:()=>{},35303:()=>{},34778:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"bailoutToClientRendering",{enumerable:!0,get:function(){return s}});let n=t(4910),o=t(45869);function s(){let e=o.staticGenerationAsyncStorage.getStore();(null==e||!e.forceStatic)&&(null==e?void 0:e.isStaticGeneration)&&(0,n.throwWithNoSSR)()}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},84552:(e,r,t)=>{"use strict";function n(e){}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"clientHookInServerComponentError",{enumerable:!0,get:function(){return n}}),t(46783),t(40002),("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},19738:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{ReadonlyURLSearchParams:function(){return p},useSearchParams:function(){return f},usePathname:function(){return x},ServerInsertedHTMLContext:function(){return l.ServerInsertedHTMLContext},useServerInsertedHTML:function(){return l.useServerInsertedHTML},useRouter:function(){return y},useParams:function(){return g},useSelectedLayoutSegments:function(){return b},useSelectedLayoutSegment:function(){return h},redirect:function(){return d.redirect},permanentRedirect:function(){return d.permanentRedirect},RedirectType:function(){return d.RedirectType},notFound:function(){return c.notFound}});let n=t(40002),o=t(78726),s=t(57210),i=t(84552),a=t(83092),l=t(80545),d=t(8010),c=t(1988),u=Symbol("internal for urlsearchparams readonly");function m(){return Error("ReadonlyURLSearchParams cannot be modified")}class p{[Symbol.iterator](){return this[u][Symbol.iterator]()}append(){throw m()}delete(){throw m()}set(){throw m()}sort(){throw m()}constructor(e){this[u]=e,this.entries=e.entries.bind(e),this.forEach=e.forEach.bind(e),this.get=e.get.bind(e),this.getAll=e.getAll.bind(e),this.has=e.has.bind(e),this.keys=e.keys.bind(e),this.values=e.values.bind(e),this.toString=e.toString.bind(e),this.size=e.size}}function f(){(0,i.clientHookInServerComponentError)("useSearchParams");let e=(0,n.useContext)(s.SearchParamsContext),r=(0,n.useMemo)(()=>e?new p(e):null,[e]);{let{bailoutToClientRendering:e}=t(34778);e()}return r}function x(){return(0,i.clientHookInServerComponentError)("usePathname"),(0,n.useContext)(s.PathnameContext)}function y(){(0,i.clientHookInServerComponentError)("useRouter");let e=(0,n.useContext)(o.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function g(){(0,i.clientHookInServerComponentError)("useParams");let e=(0,n.useContext)(o.GlobalLayoutRouterContext),r=(0,n.useContext)(s.PathParamsContext);return(0,n.useMemo)(()=>(null==e?void 0:e.tree)?function e(r,t){for(let n of(void 0===t&&(t={}),Object.values(r[1]))){let r=n[0],o=Array.isArray(r),s=o?r[1]:r;!s||s.startsWith("__PAGE__")||(o&&("c"===r[2]||"oc"===r[2])?t[r[0]]=r[1].split("/"):o&&(t[r[0]]=r[1]),t=e(n,t))}return t}(e.tree):r,[null==e?void 0:e.tree,r])}function b(e){void 0===e&&(e="children"),(0,i.clientHookInServerComponentError)("useSelectedLayoutSegments");let{tree:r}=(0,n.useContext)(o.LayoutRouterContext);return function e(r,t,n,o){let s;if(void 0===n&&(n=!0),void 0===o&&(o=[]),n)s=r[1][t];else{var i;let e=r[1];s=null!=(i=e.children)?i:Object.values(e)[0]}if(!s)return o;let l=s[0],d=(0,a.getSegmentValue)(l);return!d||d.startsWith("__PAGE__")?o:(o.push(d),e(s,t,!1,o))}(r,e)}function h(e){void 0===e&&(e="children"),(0,i.clientHookInServerComponentError)("useSelectedLayoutSegment");let r=b(e);return 0===r.length?null:r[0]}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},1988:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{notFound:function(){return n},isNotFoundError:function(){return o}});let t="NEXT_NOT_FOUND";function n(){let e=Error(t);throw e.digest=t,e}function o(e){return(null==e?void 0:e.digest)===t}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},45858:(e,r)=>{"use strict";var t;Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"RedirectStatusCode",{enumerable:!0,get:function(){return t}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(t||(t={})),("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},8010:(e,r,t)=>{"use strict";var n;Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{RedirectType:function(){return n},getRedirectError:function(){return l},redirect:function(){return d},permanentRedirect:function(){return c},isRedirectError:function(){return u},getURLFromRedirectError:function(){return m},getRedirectTypeFromError:function(){return p},getRedirectStatusCodeFromError:function(){return f}});let o=t(54580),s=t(72934),i=t(45858),a="NEXT_REDIRECT";function l(e,r,t){void 0===t&&(t=i.RedirectStatusCode.TemporaryRedirect);let n=Error(a);n.digest=a+";"+r+";"+e+";"+t+";";let s=o.requestAsyncStorage.getStore();return s&&(n.mutableCookies=s.mutableCookies),n}function d(e,r){void 0===r&&(r="replace");let t=s.actionAsyncStorage.getStore();throw l(e,r,(null==t?void 0:t.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.TemporaryRedirect)}function c(e,r){void 0===r&&(r="replace");let t=s.actionAsyncStorage.getStore();throw l(e,r,(null==t?void 0:t.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.PermanentRedirect)}function u(e){if("string"!=typeof(null==e?void 0:e.digest))return!1;let[r,t,n,o]=e.digest.split(";",4),s=Number(o);return r===a&&("replace"===t||"push"===t)&&"string"==typeof n&&!isNaN(s)&&s in i.RedirectStatusCode}function m(e){return u(e)?e.digest.split(";",3)[2]:null}function p(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function f(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},83092:(e,r)=>{"use strict";function t(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getSegmentValue",{enumerable:!0,get:function(){return t}}),("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},78726:(e,r,t)=>{"use strict";e.exports=t(50482).vendored.contexts.AppRouterContext},57210:(e,r,t)=>{"use strict";e.exports=t(50482).vendored.contexts.HooksClientContext},80545:(e,r,t)=>{"use strict";e.exports=t(50482).vendored.contexts.ServerInsertedHtml},4910:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{NEXT_DYNAMIC_NO_SSR_CODE:function(){return t},throwWithNoSSR:function(){return n}});let t="NEXT_DYNAMIC_NO_SSR_CODE";function n(){let e=Error(t);throw e.digest=t,e}},44551:(e,r,t)=>{e.exports=t(19738)},22106:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var n=t(25036),o=t(57699),s=t(32455),i=t(44551);async function a(){let e=(0,o.createServerComponentClient)({cookies:s.cookies});try{let{data:{session:r}}=await e.auth.getSession();return r}catch(e){return console.error("Error getting session:",e),null}}async function l(){let e=await a();return e?.user?e.user:null}async function d(){let e=await l();if(!e)return null;let r=(0,o.createServerComponentClient)({cookies:s.cookies});try{let{data:t,error:n}=await r.from("tenant_users").select(`
        *,
        tenant:tenants(*)
      `).eq("user_id",e.id).single();if(n)return console.error("Error getting tenant user:",n),null;return t}catch(e){return console.error("Error getting tenant user:",e),null}}async function c(){let e=await d();return e||(0,i.redirect)("/auth/login"),e}async function u(e){let r=await d();return!!r&&("super_admin"===r.role||r.permissions.includes(e))}async function m(e){return await u(e)||(0,i.redirect)("/unauthorized"),!0}var p=t(10594);async function f(){return await c(),await m("admissions.create"),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"New Application"}),n.jsx("p",{className:"text-muted-foreground",children:"Create a new student admission application"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx("button",{className:"bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90",children:"Save as Draft"}),n.jsx("button",{className:"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90",children:"Submit Application"})]})]}),(0,n.jsxs)("form",{className:"space-y-6",children:[(0,n.jsxs)(p.Zb,{children:[(0,n.jsxs)(p.Ol,{children:[n.jsx(p.ll,{children:"Student Information"}),n.jsx(p.SZ,{children:"Basic information about the student"})]}),(0,n.jsxs)(p.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name *"}),n.jsx("input",{type:"text",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter first name"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name *"}),n.jsx("input",{type:"text",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter last name"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date of Birth *"}),n.jsx("input",{type:"date",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Select date of birth",title:"Date of Birth"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Gender *"}),(0,n.jsxs)("select",{required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",title:"Gender",children:[n.jsx("option",{value:"",children:"Select gender"}),n.jsx("option",{value:"male",children:"Male"}),n.jsx("option",{value:"female",children:"Female"}),n.jsx("option",{value:"other",children:"Other"})]})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nationality *"}),n.jsx("input",{type:"text",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter nationality"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Religion"}),n.jsx("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter religion (optional)"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Blood Group"}),(0,n.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",title:"Blood Group",children:[n.jsx("option",{value:"",children:"Select blood group"}),n.jsx("option",{value:"A+",children:"A+"}),n.jsx("option",{value:"A-",children:"A-"}),n.jsx("option",{value:"B+",children:"B+"}),n.jsx("option",{value:"B-",children:"B-"}),n.jsx("option",{value:"AB+",children:"AB+"}),n.jsx("option",{value:"AB-",children:"AB-"}),n.jsx("option",{value:"O+",children:"O+"}),n.jsx("option",{value:"O-",children:"O-"})]})]})]})]})]}),(0,n.jsxs)(p.Zb,{children:[(0,n.jsxs)(p.Ol,{children:[n.jsx(p.ll,{children:"Academic Information"}),n.jsx(p.SZ,{children:"Academic details and grade applying for"})]}),(0,n.jsxs)(p.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Grade Applying For *"}),(0,n.jsxs)("select",{required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",title:"Grade Applying For",children:[n.jsx("option",{value:"",children:"Select grade"}),n.jsx("option",{value:"grade-8",children:"Grade 8"}),n.jsx("option",{value:"grade-9",children:"Grade 9"}),n.jsx("option",{value:"grade-10",children:"Grade 10"}),n.jsx("option",{value:"grade-11",children:"Grade 11"}),n.jsx("option",{value:"grade-12",children:"Grade 12"})]})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Academic Year *"}),(0,n.jsxs)("select",{required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",title:"Academic year",children:[n.jsx("option",{value:"",children:"Select academic year"}),n.jsx("option",{value:"2024-25",children:"2024-25"}),n.jsx("option",{value:"2025-26",children:"2025-26"})]})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Curriculum *"}),(0,n.jsxs)("select",{required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",title:"Curriculum",children:[n.jsx("option",{value:"",children:"Select curriculum"}),n.jsx("option",{value:"national",children:"National"}),n.jsx("option",{value:"igcse",children:"IGCSE"}),n.jsx("option",{value:"ib",children:"International Baccalaureate"})]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Previous School"}),n.jsx("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter previous school name"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Previous Grade"}),n.jsx("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter previous grade"})]})]})]})]}),(0,n.jsxs)(p.Zb,{children:[(0,n.jsxs)(p.Ol,{children:[n.jsx(p.ll,{children:"Parent/Guardian Information"}),n.jsx(p.SZ,{children:"Contact details of parent or guardian"})]}),n.jsx(p.aY,{className:"space-y-6",children:(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-3",children:"Father's Information"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name *"}),n.jsx("input",{type:"text",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter father's first name"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name *"}),n.jsx("input",{type:"text",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter father's last name"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email *"}),n.jsx("input",{type:"email",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter email address"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone *"}),n.jsx("input",{type:"tel",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter phone number"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Occupation"}),n.jsx("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter occupation"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Employer"}),n.jsx("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter employer name"})]})]})]})})]}),(0,n.jsxs)(p.Zb,{children:[(0,n.jsxs)(p.Ol,{children:[n.jsx(p.ll,{children:"Address Information"}),n.jsx(p.SZ,{children:"Permanent and current address details"})]}),(0,n.jsxs)(p.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Street Address *"}),n.jsx("input",{type:"text",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter street address"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"City *"}),n.jsx("input",{type:"text",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter city"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"State/Province *"}),n.jsx("input",{type:"text",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter state or province"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Country *"}),n.jsx("input",{type:"text",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter country"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Postal Code *"}),n.jsx("input",{type:"text",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter postal code"})]})]})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-end space-x-4",children:[n.jsx("button",{type:"button",className:"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),n.jsx("button",{type:"button",className:"px-6 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90",children:"Save as Draft"}),n.jsx("button",{type:"submit",className:"px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90",children:"Submit Application"})]})]})]})}},21342:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,metadata:()=>i});var n=t(25036),o=t(42195),s=t.n(o);t(5023);let i={title:"EMS - Employee Management System",description:"Comprehensive multi-tenant employee management system with modular architecture"};function a({children:e}){return n.jsx("html",{lang:"en",children:n.jsx("body",{className:s().className,children:e})})}},10594:(e,r,t)=>{"use strict";t.d(r,{Zb:()=>l,aY:()=>m,SZ:()=>u,Ol:()=>d,ll:()=>c});var n=t(25036),o=t(40002),s=t(70990),i=t(81774);function a(...e){return(0,i.m6)((0,s.W)(e))}let l=o.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:a("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));l.displayName="Card";let d=o.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:a("flex flex-col space-y-1.5 p-6",e),...r}));d.displayName="CardHeader";let c=o.forwardRef(({className:e,...r},t)=>n.jsx("h3",{ref:t,className:a("text-2xl font-semibold leading-none tracking-tight",e),...r}));c.displayName="CardTitle";let u=o.forwardRef(({className:e,...r},t)=>n.jsx("p",{ref:t,className:a("text-sm text-muted-foreground",e),...r}));u.displayName="CardDescription";let m=o.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:a("p-6 pt-0",e),...r}));m.displayName="CardContent",o.forwardRef(({className:e,...r},t)=>n.jsx("div",{ref:t,className:a("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},5023:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[6225,3883,6349,4345],()=>t(97529));module.exports=n})();