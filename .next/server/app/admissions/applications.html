<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/admissions/applications/page-22240876d59a5556.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900">Applications</h1><p class="text-gray-600">Manage student admission applications</p></div><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">+ New Application</button></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 pt-6"><div class="grid grid-cols-1 md:grid-cols-4 gap-4"><div><label class="block text-sm font-medium text-gray-700 mb-2">Search</label><input type="text" placeholder="Search by name, email, or application number..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value=""/></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Status</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Statuses</option><option value="pending">Pending</option><option value="under_review">Under Review</option><option value="interview_scheduled">Interview Scheduled</option><option value="accepted">Accepted</option><option value="rejected">Rejected</option><option value="waitlisted">Waitlisted</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Grade</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Grades</option><option value="Grade 9">Grade 9</option><option value="Grade 10">Grade 10</option><option value="Grade 11">Grade 11</option><option value="Grade 12">Grade 12</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="submittedDate-desc" selected="">Newest First</option><option value="submittedDate-asc">Oldest First</option><option value="studentName-asc">Name A-Z</option><option value="studentName-desc">Name Z-A</option><option value="status-asc">Status</option></select></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Applications (<!-- -->5<!-- -->)</h3><p class="text-sm text-muted-foreground">5<!-- --> of <!-- -->5<!-- --> applications shown</p></div><div class="p-6 pt-0"><div class="overflow-x-auto"><table class="w-full table-auto"><thead><tr class="border-b border-gray-200"><th class="text-left py-3 px-4 font-medium text-gray-900">Application</th><th class="text-left py-3 px-4 font-medium text-gray-900">Student</th><th class="text-left py-3 px-4 font-medium text-gray-900">Grade/Program</th><th class="text-left py-3 px-4 font-medium text-gray-900">Status</th><th class="text-left py-3 px-4 font-medium text-gray-900">Documents</th><th class="text-left py-3 px-4 font-medium text-gray-900">Priority</th><th class="text-left py-3 px-4 font-medium text-gray-900">Submitted</th><th class="text-left py-3 px-4 font-medium text-gray-900">Actions</th></tr></thead><tbody><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><div><div class="font-medium text-gray-900">APP-2024-001</div><div class="text-sm text-gray-500"><EMAIL></div></div></td><td class="py-3 px-4"><div><div class="font-medium text-gray-900">John Smith</div><div class="text-sm text-gray-500">+1-555-0123</div></div></td><td class="py-3 px-4"><div><div class="font-medium text-gray-900">Grade 9</div><div class="text-sm text-gray-500">Science Stream</div></div></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">PENDING</span></td><td class="py-3 px-4"><div class="text-sm"><span class="font-medium text-orange-600">3<!-- -->/<!-- -->5</span><div class="text-gray-500">documents</div></div></td><td class="py-3 px-4"><span class="text-sm font-medium text-red-600">HIGH</span></td><td class="py-3 px-4"><div class="text-sm text-gray-900">2024-01-15</div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Edit</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><div><div class="font-medium text-gray-900">APP-2024-002</div><div class="text-sm text-gray-500"><EMAIL></div></div></td><td class="py-3 px-4"><div><div class="font-medium text-gray-900">Sarah Johnson</div><div class="text-sm text-gray-500">+1-555-0124</div></div></td><td class="py-3 px-4"><div><div class="font-medium text-gray-900">Grade 10</div><div class="text-sm text-gray-500">Arts Stream</div></div></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">UNDER REVIEW</span></td><td class="py-3 px-4"><div class="text-sm"><span class="font-medium text-green-600">5<!-- -->/<!-- -->5</span><div class="text-gray-500">documents</div></div></td><td class="py-3 px-4"><span class="text-sm font-medium text-yellow-600">MEDIUM</span></td><td class="py-3 px-4"><div class="text-sm text-gray-900">2024-01-14</div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Edit</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><div><div class="font-medium text-gray-900">APP-2024-003</div><div class="text-sm text-gray-500"><EMAIL></div></div></td><td class="py-3 px-4"><div><div class="font-medium text-gray-900">Michael Brown</div><div class="text-sm text-gray-500">+1-555-0125</div></div></td><td class="py-3 px-4"><div><div class="font-medium text-gray-900">Grade 11</div><div class="text-sm text-gray-500">Commerce Stream</div></div></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">INTERVIEW SCHEDULED</span></td><td class="py-3 px-4"><div class="text-sm"><span class="font-medium text-green-600">5<!-- -->/<!-- -->5</span><div class="text-gray-500">documents</div></div></td><td class="py-3 px-4"><span class="text-sm font-medium text-red-600">HIGH</span></td><td class="py-3 px-4"><div class="text-sm text-gray-900">2024-01-13</div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Edit</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><div><div class="font-medium text-gray-900">APP-2024-004</div><div class="text-sm text-gray-500"><EMAIL></div></div></td><td class="py-3 px-4"><div><div class="font-medium text-gray-900">Emily Davis</div><div class="text-sm text-gray-500">+1-555-0126</div></div></td><td class="py-3 px-4"><div><div class="font-medium text-gray-900">Grade 9</div><div class="text-sm text-gray-500">Science Stream</div></div></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">ACCEPTED</span></td><td class="py-3 px-4"><div class="text-sm"><span class="font-medium text-green-600">5<!-- -->/<!-- -->5</span><div class="text-gray-500">documents</div></div></td><td class="py-3 px-4"><span class="text-sm font-medium text-yellow-600">MEDIUM</span></td><td class="py-3 px-4"><div class="text-sm text-gray-900">2024-01-12</div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Edit</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><div><div class="font-medium text-gray-900">APP-2024-005</div><div class="text-sm text-gray-500"><EMAIL></div></div></td><td class="py-3 px-4"><div><div class="font-medium text-gray-900">David Wilson</div><div class="text-sm text-gray-500">+1-555-0127</div></div></td><td class="py-3 px-4"><div><div class="font-medium text-gray-900">Grade 10</div><div class="text-sm text-gray-500">Science Stream</div></div></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">REJECTED</span></td><td class="py-3 px-4"><div class="text-sm"><span class="font-medium text-orange-600">4<!-- -->/<!-- -->5</span><div class="text-gray-500">documents</div></div></td><td class="py-3 px-4"><span class="text-sm font-medium text-green-600">LOW</span></td><td class="py-3 px-4"><div class="text-sm text-gray-900">2024-01-11</div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Edit</button></div></td></tr></tbody></table></div></div></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[9018,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"7441\",\"static/chunks/app/admissions/applications/page-22240876d59a5556.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\nb:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"DEc8SW0ph9C_XxX68bz_T\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/admissions/applications\",\"initialTree\":[\"\",{\"children\":[\"admissions\",{\"children\":[\"applications\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"admissions\",{\"children\":[\"applications\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"admissions\",\"children\",\"applications\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"admissions\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$La\"],\"globalErrorComponent\":\"$b\"}]]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>