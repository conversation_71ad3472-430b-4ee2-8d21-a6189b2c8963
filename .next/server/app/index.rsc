2:I[5250,["5250","static/chunks/5250-c6905a3e4a59cbd2.js","1931","static/chunks/app/page-4cda9109a41ae052.js"],""]
3:I[5613,[],""]
4:I[1778,[],""]
0:["Tn7xWRdXVWNLs4FpbaknK",[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",{"children":["__PAGE__",{},["$L1",["$","main",null,{"className":"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100","children":[["$","div",null,{"className":"bg-white shadow-sm","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6","children":["$","div",null,{"className":"flex items-center justify-between","children":[["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center","children":["$","span",null,{"className":"text-white font-bold text-lg","children":"EMS"}]}],["$","div",null,{"children":[["$","h1",null,{"className":"text-2xl font-bold text-gray-900","children":"Employee Management System"}],["$","p",null,{"className":"text-sm text-gray-600","children":"Multi-Tenant Modular Architecture"}]]}]]}],["$","$L2",null,{"href":"/dashboard","className":"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors","children":"Go to Dashboard"}]]}]}]}],["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12","children":[["$","div",null,{"className":"text-center mb-12","children":[["$","h2",null,{"className":"text-4xl font-bold text-gray-900 mb-4","children":"Complete School Management Solution"}],["$","p",null,{"className":"text-xl text-gray-600 max-w-3xl mx-auto","children":"Comprehensive multi-tenant platform with 7 integrated modules for complete educational institution management"}]]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12","children":[["$","$L2","0",{"href":"/admissions","className":"group bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1","children":["$","div",null,{"className":"p-6","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white text-2xl mr-4","children":"👥"}],["$","h3",null,{"className":"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors","children":"Student Admissions"}]]}],["$","p",null,{"className":"text-gray-600 text-sm leading-relaxed","children":"Manage student applications, enrollment, and admissions workflow"}],["$","div",null,{"className":"mt-4 flex items-center text-blue-600 text-sm font-medium","children":[["$","span",null,{"children":"Explore Module"}],["$","svg",null,{"className":"ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]}],["$","$L2","1",{"href":"/academic","className":"group bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1","children":["$","div",null,{"className":"p-6","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center text-white text-2xl mr-4","children":"📚"}],["$","h3",null,{"className":"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors","children":"Academic Management"}]]}],["$","p",null,{"className":"text-gray-600 text-sm leading-relaxed","children":"Curriculum, assessments, grading, and student progress tracking"}],["$","div",null,{"className":"mt-4 flex items-center text-blue-600 text-sm font-medium","children":[["$","span",null,{"children":"Explore Module"}],["$","svg",null,{"className":"ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]}],["$","$L2","2",{"href":"/financials","className":"group bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1","children":["$","div",null,{"className":"p-6","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center text-white text-2xl mr-4","children":"💰"}],["$","h3",null,{"className":"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors","children":"Student Financials"}]]}],["$","p",null,{"className":"text-gray-600 text-sm leading-relaxed","children":"Fee management, billing, payments, and financial reporting"}],["$","div",null,{"className":"mt-4 flex items-center text-blue-600 text-sm font-medium","children":[["$","span",null,{"children":"Explore Module"}],["$","svg",null,{"className":"ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]}],["$","$L2","3",{"href":"/library","className":"group bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1","children":["$","div",null,{"className":"p-6","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center text-white text-2xl mr-4","children":"📖"}],["$","h3",null,{"className":"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors","children":"Library Management"}]]}],["$","p",null,{"className":"text-gray-600 text-sm leading-relaxed","children":"Digital and physical library with circulation and inventory"}],["$","div",null,{"className":"mt-4 flex items-center text-blue-600 text-sm font-medium","children":[["$","span",null,{"children":"Explore Module"}],["$","svg",null,{"className":"ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]}],["$","$L2","4",{"href":"/alumni","className":"group bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1","children":["$","div",null,{"className":"p-6","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center text-white text-2xl mr-4","children":"🎓"}],["$","h3",null,{"className":"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors","children":"Alumni Engagement"}]]}],["$","p",null,{"className":"text-gray-600 text-sm leading-relaxed","children":"Alumni directory, events, job board, and community management"}],["$","div",null,{"className":"mt-4 flex items-center text-blue-600 text-sm font-medium","children":[["$","span",null,{"children":"Explore Module"}],["$","svg",null,{"className":"ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]}],["$","$L2","5",{"href":"/hostel","className":"group bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1","children":["$","div",null,{"className":"p-6","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center text-white text-2xl mr-4","children":"🏠"}],["$","h3",null,{"className":"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors","children":"Hostel Management"}]]}],["$","p",null,{"className":"text-gray-600 text-sm leading-relaxed","children":"Room allocation, resident tracking, and hostel operations"}],["$","div",null,{"className":"mt-4 flex items-center text-blue-600 text-sm font-medium","children":[["$","span",null,{"children":"Explore Module"}],["$","svg",null,{"className":"ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]}],["$","$L2","6",{"href":"/teachers","className":"group bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1","children":["$","div",null,{"className":"p-6","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"w-12 h-12 bg-teal-500 rounded-lg flex items-center justify-center text-white text-2xl mr-4","children":"👨‍🏫"}],["$","h3",null,{"className":"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors","children":"Teacher Management"}]]}],["$","p",null,{"className":"text-gray-600 text-sm leading-relaxed","children":"Teacher profiles, evaluations, and professional development"}],["$","div",null,{"className":"mt-4 flex items-center text-blue-600 text-sm font-medium","children":[["$","span",null,{"children":"Explore Module"}],["$","svg",null,{"className":"ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]}]]}],["$","div",null,{"className":"bg-white rounded-2xl shadow-lg p-8","children":[["$","h3",null,{"className":"text-2xl font-bold text-gray-900 mb-6 text-center","children":"Key Features"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6","children":[["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4","children":["$","span",null,{"className":"text-2xl","children":"🏢"}]}],["$","h4",null,{"className":"font-semibold text-gray-900 mb-2","children":"Multi-Tenant"}],["$","p",null,{"className":"text-sm text-gray-600","children":"Complete data isolation between institutions"}]]}],["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4","children":["$","span",null,{"className":"text-2xl","children":"🔧"}]}],["$","h4",null,{"className":"font-semibold text-gray-900 mb-2","children":"Modular"}],["$","p",null,{"className":"text-sm text-gray-600","children":"Enable/disable modules per tenant needs"}]]}],["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4","children":["$","span",null,{"className":"text-2xl","children":"🔒"}]}],["$","h4",null,{"className":"font-semibold text-gray-900 mb-2","children":"Secure"}],["$","p",null,{"className":"text-sm text-gray-600","children":"Role-based access control and permissions"}]]}],["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4","children":["$","span",null,{"className":"text-2xl","children":"📱"}]}],["$","h4",null,{"className":"font-semibold text-gray-900 mb-2","children":"Responsive"}],["$","p",null,{"className":"text-sm text-gray-600","children":"Works seamlessly on all devices"}]]}]]}]]}]]}]]}],null]]},[null,["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__className_e8ce0c","children":["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[],"styles":null}]}]}],null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/ce7d169ac7e92b8e.css","precedence":"next","crossOrigin":""}]],"$L5"]]]]
5:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"EMS - Employee Management System"}],["$","meta","3",{"name":"description","content":"Comprehensive multi-tenant employee management system with modular architecture"}],["$","meta","4",{"name":"next-size-adjust"}]]
1:null
