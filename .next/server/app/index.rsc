2:I[5250,["5250","static/chunks/5250-c6905a3e4a59cbd2.js","1931","static/chunks/app/page-4cda9109a41ae052.js"],""]
3:I[5613,[],""]
4:I[1778,[],""]
0:["az92YWeqjFDK2ONnGo-1U",[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",{"children":["__PAGE__",{},["$L1",["$","div",null,{"className":"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100","children":["$","div",null,{"className":"container mx-auto px-4 py-16","children":["$","div",null,{"className":"text-center","children":[["$","h1",null,{"className":"text-6xl font-bold text-gray-900 mb-8","children":"EMS Platform"}],["$","p",null,{"className":"text-xl text-gray-600 mb-12 max-w-2xl mx-auto","children":"Employee Management System - Multi-Tenant Modular Architecture"}],["$","div",null,{"className":"space-y-4 mb-12","children":[["$","div",null,{"className":"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded max-w-md mx-auto","children":"✅ Deployment Successful"}],["$","div",null,{"className":"bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded max-w-md mx-auto","children":"✅ Next.js 14 App Router"}],["$","div",null,{"className":"bg-purple-100 border border-purple-400 text-purple-700 px-4 py-3 rounded max-w-md mx-auto","children":"✅ TypeScript & Tailwind CSS"}]]}],["$","div",null,{"className":"space-x-4","children":[["$","$L2",null,{"href":"/dashboard","className":"bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-block","children":"Go to Dashboard"}],["$","$L2",null,{"href":"/test","className":"bg-gray-600 text-white px-8 py-3 rounded-lg hover:bg-gray-700 transition-colors inline-block","children":"Test Page"}]]}],["$","div",null,{"className":"mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto","children":[["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md","children":[["$","h3",null,{"className":"text-lg font-semibold mb-2","children":"🏫 Multi-Tenant"}],["$","p",null,{"className":"text-gray-600","children":"Complete data isolation between institutions"}]]}],["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md","children":[["$","h3",null,{"className":"text-lg font-semibold mb-2","children":"🔧 Modular"}],["$","p",null,{"className":"text-gray-600","children":"Enable/disable modules per tenant needs"}]]}],["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md","children":[["$","h3",null,{"className":"text-lg font-semibold mb-2","children":"🔒 Secure"}],["$","p",null,{"className":"text-gray-600","children":"Role-based access control and permissions"}]]}]]}]]}]}]}],null]]},[null,["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__className_e8ce0c","children":["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[],"styles":null}]}]}],null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/1efa94dc89f20134.css","precedence":"next","crossOrigin":""}]],"$L5"]]]]
5:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"EMS - Employee Management System"}],["$","meta","3",{"name":"description","content":"Comprehensive multi-tenant employee management system with modular architecture"}],["$","meta","4",{"name":"next-size-adjust"}]]
1:null
