2:I[7108,["5250","static/chunks/5250-c6905a3e4a59cbd2.js","7663","static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js"],""]
3:I[5613,[],""]
4:I[1778,[],""]
0:["WyP9Wc4exBySD9T7YRTKE",[[["",{"children":["dashboard",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",{"children":["dashboard",{"children":["__PAGE__",{},["$L1",["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"className":"flex items-center justify-between","children":[["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold tracking-tight","children":"Dashboard"}],["$","p",null,{"className":"text-muted-foreground","children":["Welcome back, ","Demo","! Here's what's happening at ","Demo School","."]}]]}],["$","div",null,{"className":"flex items-center space-x-2","children":["$","span",null,{"className":"text-sm text-muted-foreground","children":["PREMIUM"," Plan"]}]}]]}],["$","div",null,{"className":"grid gap-4 md:grid-cols-2 lg:grid-cols-4","children":[["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm","children":[["$","div",null,{"className":"p-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","h3",null,{"className":"tracking-tight text-sm font-medium","children":"Total Students"}],["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"2","className":"h-4 w-4 text-muted-foreground","children":[["$","path",null,{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","circle",null,{"cx":"9","cy":"7","r":"4"}],["$","path",null,{"d":"m22 21-3-3m0 0a5.5 5.5 0 1 0-7.78-7.78 5.5 5.5 0 0 0 7.78 7.78Z"}]]}]]}],["$","div",null,{"className":"p-6 pt-0","children":[["$","div",null,{"className":"text-2xl font-bold","children":1250}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"+5% from last month"}]]}]]}],["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm","children":[["$","div",null,{"className":"p-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","h3",null,{"className":"tracking-tight text-sm font-medium","children":"Teachers"}],["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"2","className":"h-4 w-4 text-muted-foreground","children":["$","path",null,{"d":"M22 12h-4l-3 9L9 3l-3 9H2"}]}]]}],["$","div",null,{"className":"p-6 pt-0","children":[["$","div",null,{"className":"text-2xl font-bold","children":85}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"+2 new this month"}]]}]]}],["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm","children":[["$","div",null,{"className":"p-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","h3",null,{"className":"tracking-tight text-sm font-medium","children":"Pending Admissions"}],["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"2","className":"h-4 w-4 text-muted-foreground","children":[["$","circle",null,{"cx":"12","cy":"12","r":"10"}],["$","polyline",null,{"points":"12,6 12,12 16,14"}]]}]]}],["$","div",null,{"className":"p-6 pt-0","children":[["$","div",null,{"className":"text-2xl font-bold","children":23}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"Requires review"}]]}]]}],["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm","children":[["$","div",null,{"className":"p-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","h3",null,{"className":"tracking-tight text-sm font-medium","children":"Revenue"}],["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":"2","className":"h-4 w-4 text-muted-foreground","children":["$","path",null,{"d":"M12 2v20m9-9H3"}]}]]}],["$","div",null,{"className":"p-6 pt-0","children":[["$","div",null,{"className":"text-2xl font-bold","children":["$$","125,000"]}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"+12% from last month"}]]}]]}]]}],["$","div",null,{"children":[["$","h2",null,{"className":"text-2xl font-bold tracking-tight mb-4","children":"Available Modules"}],["$","div",null,{"className":"grid gap-4 md:grid-cols-2 lg:grid-cols-3","children":[["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer","children":[["$","div",null,{"className":"flex flex-col space-y-1.5 p-6","children":[["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-2xl","children":"👥"}],["$","h3",null,{"className":"font-semibold tracking-tight text-lg","children":"Student Admissions"}]]}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Manage student applications and enrollment"}]]}],["$","div",null,{"className":"p-6 pt-0","children":[["$","p",null,{"className":"text-sm text-muted-foreground mb-3","children":"23 pending applications"}],["$","a",null,{"href":"/dashboard/admissions","className":"inline-flex items-center text-sm font-medium text-primary hover:text-primary/80","children":["Open Module",["$","svg",null,{"className":"ml-1 h-4 w-4","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]]}],["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer","children":[["$","div",null,{"className":"flex flex-col space-y-1.5 p-6","children":[["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-2xl","children":"📚"}],["$","h3",null,{"className":"font-semibold tracking-tight text-lg","children":"Academic Management"}]]}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Curriculum, assessments, and learning resources"}]]}],["$","div",null,{"className":"p-6 pt-0","children":[["$","p",null,{"className":"text-sm text-muted-foreground mb-3","children":"1250 active students"}],["$","a",null,{"href":"/dashboard/academic","className":"inline-flex items-center text-sm font-medium text-primary hover:text-primary/80","children":["Open Module",["$","svg",null,{"className":"ml-1 h-4 w-4","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]]}],["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer","children":[["$","div",null,{"className":"flex flex-col space-y-1.5 p-6","children":[["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-2xl","children":"💰"}],["$","h3",null,{"className":"font-semibold tracking-tight text-lg","children":"Student Financials"}]]}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Fee management, billing, and payments"}]]}],["$","div",null,{"className":"p-6 pt-0","children":[["$","p",null,{"className":"text-sm text-muted-foreground mb-3","children":"$$125,000 revenue"}],["$","a",null,{"href":"/dashboard/financials","className":"inline-flex items-center text-sm font-medium text-primary hover:text-primary/80","children":["Open Module",["$","svg",null,{"className":"ml-1 h-4 w-4","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]]}],["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer","children":[["$","div",null,{"className":"flex flex-col space-y-1.5 p-6","children":[["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-2xl","children":"📖"}],["$","h3",null,{"className":"font-semibold tracking-tight text-lg","children":"Library Management"}]]}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Catalog, circulation, and digital resources"}]]}],["$","div",null,{"className":"p-6 pt-0","children":[["$","p",null,{"className":"text-sm text-muted-foreground mb-3","children":"5420 books available"}],["$","a",null,{"href":"/dashboard/library","className":"inline-flex items-center text-sm font-medium text-primary hover:text-primary/80","children":["Open Module",["$","svg",null,{"className":"ml-1 h-4 w-4","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]]}],["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer","children":[["$","div",null,{"className":"flex flex-col space-y-1.5 p-6","children":[["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-2xl","children":"👨‍🏫"}],["$","h3",null,{"className":"font-semibold tracking-tight text-lg","children":"Teacher Management"}]]}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Teacher profiles and performance tracking"}]]}],["$","div",null,{"className":"p-6 pt-0","children":[["$","p",null,{"className":"text-sm text-muted-foreground mb-3","children":"85 active teachers"}],["$","a",null,{"href":"/dashboard/teachers","className":"inline-flex items-center text-sm font-medium text-primary hover:text-primary/80","children":["Open Module",["$","svg",null,{"className":"ml-1 h-4 w-4","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]]}],["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer","children":[["$","div",null,{"className":"flex flex-col space-y-1.5 p-6","children":[["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-2xl","children":"🏠"}],["$","h3",null,{"className":"font-semibold tracking-tight text-lg","children":"Hostel Management"}]]}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Room allocation and resident management"}]]}],["$","div",null,{"className":"p-6 pt-0","children":[["$","p",null,{"className":"text-sm text-muted-foreground mb-3","children":"78% occupancy"}],["$","a",null,{"href":"/dashboard/hostel","className":"inline-flex items-center text-sm font-medium text-primary hover:text-primary/80","children":["Open Module",["$","svg",null,{"className":"ml-1 h-4 w-4","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]]}],["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer","children":[["$","div",null,{"className":"flex flex-col space-y-1.5 p-6","children":[["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-2xl","children":"🚌"}],["$","h3",null,{"className":"font-semibold tracking-tight text-lg","children":"Transport Management"}]]}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Vehicle fleet and route management"}]]}],["$","div",null,{"className":"p-6 pt-0","children":[["$","p",null,{"className":"text-sm text-muted-foreground mb-3","children":"20 vehicles, 12 routes"}],["$","a",null,{"href":"/dashboard/transport","className":"inline-flex items-center text-sm font-medium text-primary hover:text-primary/80","children":["Open Module",["$","svg",null,{"className":"ml-1 h-4 w-4","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]]}],["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer","children":[["$","div",null,{"className":"flex flex-col space-y-1.5 p-6","children":[["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-2xl","children":"🎓"}],["$","h3",null,{"className":"font-semibold tracking-tight text-lg","children":"Alumni Engagement"}]]}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Alumni network and community management"}]]}],["$","div",null,{"className":"p-6 pt-0","children":[["$","p",null,{"className":"text-sm text-muted-foreground mb-3","children":"Connect with graduates"}],["$","a",null,{"href":"/dashboard/alumni","className":"inline-flex items-center text-sm font-medium text-primary hover:text-primary/80","children":["Open Module",["$","svg",null,{"className":"ml-1 h-4 w-4","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 5l7 7-7 7"}]}]]}]]}]]}]]}]]}],["$","div",null,{"className":"rounded-lg border bg-card text-card-foreground shadow-sm","children":[["$","div",null,{"className":"flex flex-col space-y-1.5 p-6","children":[["$","h3",null,{"className":"text-2xl font-semibold leading-none tracking-tight","children":"Recent Activity"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Latest updates across all modules"}]]}],["$","div",null,{"className":"p-6 pt-0","children":["$","div",null,{"className":"space-y-4","children":[["$","div","0",{"className":"flex items-center justify-between p-3 border rounded-lg","children":[["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"w-2 h-2 bg-primary rounded-full"}],["$","div",null,{"children":[["$","p",null,{"className":"font-medium","children":"New admission application"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":["John Smith"," • ","Admissions"]}]]}]]}],["$","span",null,{"className":"text-sm text-muted-foreground","children":"2 hours ago"}]]}],["$","div","1",{"className":"flex items-center justify-between p-3 border rounded-lg","children":[["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"w-2 h-2 bg-primary rounded-full"}],["$","div",null,{"children":[["$","p",null,{"className":"font-medium","children":"Fee payment received"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":["Sarah Johnson"," • ","Financials"]}]]}]]}],["$","span",null,{"className":"text-sm text-muted-foreground","children":"4 hours ago"}]]}],["$","div","2",{"className":"flex items-center justify-between p-3 border rounded-lg","children":[["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"w-2 h-2 bg-primary rounded-full"}],["$","div",null,{"children":[["$","p",null,{"className":"font-medium","children":"Book issued"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":["Michael Brown"," • ","Library"]}]]}]]}],["$","span",null,{"className":"text-sm text-muted-foreground","children":"6 hours ago"}]]}],["$","div","3",{"className":"flex items-center justify-between p-3 border rounded-lg","children":[["$","div",null,{"className":"flex items-center space-x-3","children":[["$","div",null,{"className":"w-2 h-2 bg-primary rounded-full"}],["$","div",null,{"children":[["$","p",null,{"className":"font-medium","children":"Assignment submitted"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":["Emily Davis"," • ","Academic"]}]]}]]}],["$","span",null,{"className":"text-sm text-muted-foreground","children":"8 hours ago"}]]}]]}]}]]}]]}],null]]},[null,["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","dashboard","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}],"params":{}}],null]]},[null,["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__className_e8ce0c","children":["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[],"styles":null}]}]}],null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/1efa94dc89f20134.css","precedence":"next","crossOrigin":""}]],"$L5"]]]]
5:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"EMS - Employee Management System"}],["$","meta","3",{"name":"description","content":"Comprehensive multi-tenant employee management system with modular architecture"}],["$","meta","4",{"name":"next-size-adjust"}]]
1:null
