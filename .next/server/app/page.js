(()=>{var e={};e.id=1931,e.ids=[1931],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},96445:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(50482),n=s(69108),a=s(62563),i=s.n(a),o=s(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,51136)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,21342)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/page.tsx"],m="/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89226:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},17512:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,61476,23))},74117:()=>{},48026:(e,t,s)=>{let{createProxy:r}=s(86843);e.exports=r("/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/link.js")},40646:(e,t,s)=>{e.exports=s(48026)},21342:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>i});var r=s(25036),n=s(42195),a=s.n(n);s(5023);let i={title:"EMS - Employee Management System",description:"Comprehensive multi-tenant employee management system with modular architecture"};function o({children:e}){return r.jsx("html",{lang:"en",children:r.jsx("body",{className:a().className,children:e})})}},51136:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(25036),n=s(40646),a=s.n(n);function i(){return r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:r.jsx("div",{className:"container mx-auto px-4 py-16",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h1",{className:"text-6xl font-bold text-gray-900 mb-8",children:"EMS Platform"}),r.jsx("p",{className:"text-xl text-gray-600 mb-12 max-w-2xl mx-auto",children:"Employee Management System - Multi-Tenant Modular Architecture"}),(0,r.jsxs)("div",{className:"space-y-4 mb-12",children:[r.jsx("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded max-w-md mx-auto",children:"✅ Deployment Successful"}),r.jsx("div",{className:"bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded max-w-md mx-auto",children:"✅ Next.js 14 App Router"}),r.jsx("div",{className:"bg-purple-100 border border-purple-400 text-purple-700 px-4 py-3 rounded max-w-md mx-auto",children:"✅ TypeScript & Tailwind CSS"})]}),(0,r.jsxs)("div",{className:"space-x-4",children:[r.jsx(a(),{href:"/dashboard",className:"bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-block",children:"Go to Dashboard"}),r.jsx(a(),{href:"/test",className:"bg-gray-600 text-white px-8 py-3 rounded-lg hover:bg-gray-700 transition-colors inline-block",children:"Test Page"})]}),(0,r.jsxs)("div",{className:"mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[r.jsx("h3",{className:"text-lg font-semibold mb-2",children:"\uD83C\uDFEB Multi-Tenant"}),r.jsx("p",{className:"text-gray-600",children:"Complete data isolation between institutions"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[r.jsx("h3",{className:"text-lg font-semibold mb-2",children:"\uD83D\uDD27 Modular"}),r.jsx("p",{className:"text-gray-600",children:"Enable/disable modules per tenant needs"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[r.jsx("h3",{className:"text-lg font-semibold mb-2",children:"\uD83D\uDD12 Secure"}),r.jsx("p",{className:"text-gray-600",children:"Role-based access control and permissions"})]})]})]})})})}},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,3883,1476],()=>s(96445));module.exports=r})();