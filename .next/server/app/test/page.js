(()=>{var e={};e.id=7928,e.ids=[7928],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10648:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>p});var s=r(50482),n=r(69108),a=r(62563),i=r.n(a),o=r(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let p=["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,68482)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/test/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,21342)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/test/page.tsx"],c="/test/page",u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},89226:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},74117:()=>{},35303:()=>{},21342:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>i});var s=r(25036),n=r(42195),a=r.n(n);r(5023);let i={title:"EMS - Employee Management System",description:"Comprehensive multi-tenant employee management system with modular architecture"};function o({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:a().className,children:e})})}},68482:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(25036);function n(){return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"EMS Test Page"}),s.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"If you can see this, the deployment is working!"}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded",children:"✅ Next.js App Router is working"}),s.jsx("div",{className:"bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded",children:"✅ Tailwind CSS is working"}),s.jsx("div",{className:"bg-purple-100 border border-purple-400 text-purple-700 px-4 py-3 rounded",children:"✅ TypeScript compilation is working"})]}),s.jsx("div",{className:"mt-8",children:s.jsx("a",{href:"/",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Go to Home Page"})})]})})}},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,3883],()=>r(10648));module.exports=s})();