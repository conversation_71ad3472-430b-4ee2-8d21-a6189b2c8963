<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/alumni/jobs/page-a9a41ceb3795e592.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Alumni Job Board</h1><p class="text-muted-foreground">Career opportunities and job postings from alumni network</p></div><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Post Job</button></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Active Jobs</h3><span class="text-2xl">💼</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">45</div><p class="text-xs text-muted-foreground">Currently open</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Applications</h3><span class="text-2xl">📝</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">234</div><p class="text-xs text-muted-foreground">This month</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Placements</h3><span class="text-2xl">✅</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">89</div><p class="text-xs text-muted-foreground">Successful hires</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Companies</h3><span class="text-2xl">🏢</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">28</div><p class="text-xs text-muted-foreground">Hiring partners</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Latest Job Opportunities</h3><p class="text-sm text-muted-foreground">Recent job postings from alumni and partner companies</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Senior Software Engineer</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Full-time</span></div><p class="text-blue-600 font-medium">Google</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📍 Location:</span> <!-- -->Bangalore, India</p><p><span class="font-medium">💼 Experience:</span> <!-- -->3-5 years</p></div><div><p><span class="font-medium">💰 Salary:</span> <!-- -->₹25-35 LPA</p><p><span class="font-medium">📝 Applications:</span> <!-- -->23</p></div></div><div class="mt-3"><p class="text-sm text-gray-600 mb-2"><span class="font-medium">Posted by:</span> <!-- -->Rajesh Kumar (Class of 2018)<!-- --> • <!-- -->2 days ago</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">React</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Node.js</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Python</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">AWS</span></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Apply Now</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Save Job</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Product Manager</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Full-time</span></div><p class="text-blue-600 font-medium">Microsoft</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📍 Location:</span> <!-- -->Hyderabad, India</p><p><span class="font-medium">💼 Experience:</span> <!-- -->4-6 years</p></div><div><p><span class="font-medium">💰 Salary:</span> <!-- -->₹30-45 LPA</p><p><span class="font-medium">📝 Applications:</span> <!-- -->18</p></div></div><div class="mt-3"><p class="text-sm text-gray-600 mb-2"><span class="font-medium">Posted by:</span> <!-- -->Priya Sharma (Class of 2017)<!-- --> • <!-- -->5 days ago</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Product Strategy</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Analytics</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Agile</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Leadership</span></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Apply Now</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Save Job</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Data Scientist</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Full-time</span></div><p class="text-blue-600 font-medium">Amazon</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📍 Location:</span> <!-- -->Remote</p><p><span class="font-medium">💼 Experience:</span> <!-- -->2-4 years</p></div><div><p><span class="font-medium">💰 Salary:</span> <!-- -->₹20-30 LPA</p><p><span class="font-medium">📝 Applications:</span> <!-- -->31</p></div></div><div class="mt-3"><p class="text-sm text-gray-600 mb-2"><span class="font-medium">Posted by:</span> <!-- -->Amit Patel (Class of 2019)<!-- --> • <!-- -->1 week ago</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Python</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Machine Learning</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">SQL</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Statistics</span></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Apply Now</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Save Job</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">UX Designer</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Full-time</span></div><p class="text-blue-600 font-medium">Flipkart</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📍 Location:</span> <!-- -->Bangalore, India</p><p><span class="font-medium">💼 Experience:</span> <!-- -->2-3 years</p></div><div><p><span class="font-medium">💰 Salary:</span> <!-- -->₹15-25 LPA</p><p><span class="font-medium">📝 Applications:</span> <!-- -->15</p></div></div><div class="mt-3"><p class="text-sm text-gray-600 mb-2"><span class="font-medium">Posted by:</span> <!-- -->Sneha Gupta (Class of 2020)<!-- --> • <!-- -->1 week ago</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Figma</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">User Research</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Prototyping</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Design Systems</span></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Apply Now</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Save Job</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Job Categories</h3><p class="text-sm text-muted-foreground">Jobs by industry</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><span class="text-sm font-medium">Technology</span><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">18<!-- --> jobs</span></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Finance</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">12<!-- --> jobs</span></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Consulting</span><span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">8<!-- --> jobs</span></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Healthcare</span><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">5<!-- --> jobs</span></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Education</span><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">2<!-- --> jobs</span></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Top Hiring Companies</h3><p class="text-sm text-muted-foreground">Companies with most job postings</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Google</span><p class="text-xs text-gray-500">12<!-- --> alumni working</p></div><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">8<!-- --> jobs</span></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Microsoft</span><p class="text-xs text-gray-500">9<!-- --> alumni working</p></div><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">6<!-- --> jobs</span></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Amazon</span><p class="text-xs text-gray-500">15<!-- --> alumni working</p></div><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">5<!-- --> jobs</span></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Flipkart</span><p class="text-xs text-gray-500">7<!-- --> alumni working</p></div><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">4<!-- --> jobs</span></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Zomato</span><p class="text-xs text-gray-500">5<!-- --> alumni working</p></div><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">3<!-- --> jobs</span></div></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[4254,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"8221\",\"static/chunks/app/dashboard/alumni/jobs/page-a9a41ceb3795e592.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"WyP9Wc4exBySD9T7YRTKE\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/alumni/jobs\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"alumni\",{\"children\":[\"jobs\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"alumni\",{\"children\":[\"jobs\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"alumni\",\"children\",\"jobs\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"alumni\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>