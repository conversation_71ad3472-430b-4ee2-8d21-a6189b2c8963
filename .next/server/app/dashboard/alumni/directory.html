<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/alumni/directory/page-72691c2bf3927a71.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Alumni Directory</h1><p class="text-muted-foreground">Browse and search alumni profiles and contact information</p></div><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Add Alumni</button></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Total Alumni</h3><span class="text-2xl">👥</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">5,234</div><p class="text-xs text-muted-foreground">All time</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Active Profiles</h3><span class="text-2xl">✅</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">3,456</div><p class="text-xs text-muted-foreground">Updated profiles</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Recent Graduates</h3><span class="text-2xl">🎓</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">234</div><p class="text-xs text-muted-foreground">Class of 2024</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Countries</h3><span class="text-2xl">🌍</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">45</div><p class="text-xs text-muted-foreground">Global presence</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Search Alumni</h3><p class="text-sm text-muted-foreground">Find alumni by name, batch, profession, or location</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-4"><div><label class="text-sm font-medium">Search Name</label><input type="text" placeholder="Enter name..." class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"/></div><div><label class="text-sm font-medium">Graduation Year</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">All Years</option><option value="2024">2024</option><option value="2023">2023</option><option value="2022">2022</option><option value="2021">2021</option><option value="2020">2020</option></select></div><div><label class="text-sm font-medium">Industry</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">All Industries</option><option value="technology">Technology</option><option value="finance">Finance</option><option value="healthcare">Healthcare</option><option value="education">Education</option><option value="consulting">Consulting</option></select></div><div><label class="text-sm font-medium">Location</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">All Locations</option><option value="india">India</option><option value="usa">United States</option><option value="uk">United Kingdom</option><option value="canada">Canada</option><option value="australia">Australia</option></select></div></div><div class="mt-4 flex space-x-2"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Search</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Clear Filters</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Alumni Profiles</h3><p class="text-sm text-muted-foreground">Browse alumni directory</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex items-start space-x-4"><div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center text-2xl">👨‍💻</div><div class="flex-1"><h3 class="text-lg font-semibold">Rajesh Kumar</h3><p class="text-sm text-gray-600">B.Tech Computer Science<!-- --> • Class of <!-- -->2018</p><p class="text-sm font-medium text-blue-600">Senior Software Engineer</p><p class="text-sm text-gray-600">Google<!-- --> • <!-- -->Bangalore, India</p><div class="mt-2 flex space-x-4 text-sm"><a href="mailto:<EMAIL>" class="text-blue-600 hover:underline">📧 Email</a><a href="https://linkedin.com/in/rajeshkumar" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">💼 LinkedIn</a></div></div></div><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm">View Profile</button><button class="text-green-600 hover:text-green-800 text-sm">Connect</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex items-start space-x-4"><div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center text-2xl">👩‍💼</div><div class="flex-1"><h3 class="text-lg font-semibold">Priya Sharma</h3><p class="text-sm text-gray-600">MBA Finance<!-- --> • Class of <!-- -->2019</p><p class="text-sm font-medium text-blue-600">Investment Analyst</p><p class="text-sm text-gray-600">Goldman Sachs<!-- --> • <!-- -->Mumbai, India</p><div class="mt-2 flex space-x-4 text-sm"><a href="mailto:<EMAIL>" class="text-blue-600 hover:underline">📧 Email</a><a href="https://linkedin.com/in/priyasharma" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">💼 LinkedIn</a></div></div></div><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm">View Profile</button><button class="text-green-600 hover:text-green-800 text-sm">Connect</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex items-start space-x-4"><div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center text-2xl">👨‍🔬</div><div class="flex-1"><h3 class="text-lg font-semibold">Amit Patel</h3><p class="text-sm text-gray-600">M.Tech AI/ML<!-- --> • Class of <!-- -->2020</p><p class="text-sm font-medium text-blue-600">Data Scientist</p><p class="text-sm text-gray-600">Microsoft<!-- --> • <!-- -->Seattle, USA</p><div class="mt-2 flex space-x-4 text-sm"><a href="mailto:<EMAIL>" class="text-blue-600 hover:underline">📧 Email</a><a href="https://linkedin.com/in/amitpatel" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">💼 LinkedIn</a></div></div></div><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm">View Profile</button><button class="text-green-600 hover:text-green-800 text-sm">Connect</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex items-start space-x-4"><div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center text-2xl">👩‍🔧</div><div class="flex-1"><h3 class="text-lg font-semibold">Sneha Gupta</h3><p class="text-sm text-gray-600">B.Tech Electronics<!-- --> • Class of <!-- -->2021</p><p class="text-sm font-medium text-blue-600">Hardware Engineer</p><p class="text-sm text-gray-600">Tesla<!-- --> • <!-- -->California, USA</p><div class="mt-2 flex space-x-4 text-sm"><a href="mailto:<EMAIL>" class="text-blue-600 hover:underline">📧 Email</a><a href="https://linkedin.com/in/snehagupta" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">💼 LinkedIn</a></div></div></div><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm">View Profile</button><button class="text-green-600 hover:text-green-800 text-sm">Connect</button></div></div></div></div><div class="mt-6 flex items-center justify-between"><p class="text-sm text-gray-600">Showing 1-4 of 5,234 alumni</p><div class="flex space-x-2"><button class="px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50">Previous</button><button class="px-3 py-1 bg-blue-600 text-white rounded-md">1</button><button class="px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50">2</button><button class="px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50">3</button><button class="px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50">Next</button></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[7468,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"6657\",\"static/chunks/app/dashboard/alumni/directory/page-72691c2bf3927a71.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"WyP9Wc4exBySD9T7YRTKE\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/alumni/directory\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"alumni\",{\"children\":[\"directory\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"alumni\",{\"children\":[\"directory\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"alumni\",\"children\",\"directory\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"alumni\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>