(()=>{var e={};e.id=600,e.ids=[600],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},65403:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=t(50482),n=t(69108),r=t(62563),i=t.n(r),l=t(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o=["",{children:["dashboard",{children:["transport",{children:["routes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,13069)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/transport/routes/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/transport/routes/page.tsx"],m="/dashboard/transport/routes/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/dashboard/transport/routes/page",pathname:"/dashboard/transport/routes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},62552:(e,s,t)=>{Promise.resolve().then(t.bind(t,44679))},44679:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(95344),n=t(3729),r=t(18791);function i({tenantId:e,onCreateRoute:s,onEditRoute:t,onOptimizeRoute:i,onAssignVehicle:l}){let[d,o]=(0,n.useState)(""),[c,m]=(0,n.useState)("all"),[u,x]=(0,n.useState)("all"),[h,p]=(0,n.useState)(null),g=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"inactive":default:return"bg-gray-100 text-gray-800";case"under_review":return"bg-yellow-100 text-yellow-800";case"suspended":return"bg-red-100 text-red-800"}},j=e=>{switch(e){case"low":return"bg-green-100 text-green-800";case"medium":return"bg-yellow-100 text-yellow-800";case"high":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},v=e=>{switch(e){case"pickup":return"bg-blue-100 text-blue-800";case"drop":return"bg-purple-100 text-purple-800";case"both":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},f=[{id:"1",routeId:"RT-001",routeName:"Route A - Central Delhi",description:"Main route covering central Delhi residential areas",area:"Central Delhi",startLocation:{name:"School Campus",address:"ABC School, Connaught Place, New Delhi",coordinates:{latitude:28.6315,longitude:77.2167}},endLocation:{name:"School Campus",address:"ABC School, Connaught Place, New Delhi",coordinates:{latitude:28.6315,longitude:77.2167}},stops:[{id:"S1",name:"Rajouri Garden Metro",address:"Rajouri Garden Metro Station, Delhi",coordinates:{latitude:28.6467,longitude:77.12},estimatedTime:"07:15",studentsCount:8,landmark:"Near Metro Station",stopType:"both",sequence:1},{id:"S2",name:"Karol Bagh Market",address:"Karol Bagh Market, Delhi",coordinates:{latitude:28.6519,longitude:77.1909},estimatedTime:"07:25",studentsCount:12,landmark:"Main Market Area",stopType:"both",sequence:2},{id:"S3",name:"Paharganj",address:"Paharganj Main Road, Delhi",coordinates:{latitude:28.6414,longitude:77.2085},estimatedTime:"07:35",studentsCount:6,landmark:"Near Railway Station",stopType:"both",sequence:3},{id:"S4",name:"Connaught Place",address:"Connaught Place, Central Delhi",coordinates:{latitude:28.6304,longitude:77.2177},estimatedTime:"07:45",studentsCount:12,landmark:"Central Park",stopType:"both",sequence:4}],schedule:{morningPickup:{startTime:"07:00",endTime:"08:00",estimatedDuration:60},afternoonDrop:{startTime:"14:30",endTime:"15:30",estimatedDuration:60}},distance:{totalKm:25,estimatedTime:60,fuelConsumption:3},vehicleAssignment:{vehicleId:"VEH-2024-001",vehicleNumber:"DL-01-AB-1234",driverId:"DRV-001",driverName:"Rajesh Kumar",conductorId:"CON-001",conductorName:"Suresh Singh"},capacity:{maxStudents:45,currentStudents:38,availableSeats:7},operatingDays:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],status:"active",safetyFeatures:{schoolZones:3,trafficSignals:8,speedBreakers:12,riskLevel:"medium",emergencyContacts:["+91-9876543210","+91-9876543211"]},performance:{onTimePercentage:92,averageDelay:3.5,studentSatisfaction:4.2,fuelEfficiency:8.5,incidentCount:1},costs:{dailyOperatingCost:1200,monthlyFuelCost:18e3,maintenanceCost:5e3,driverSalary:25e3,totalMonthlyCost:48e3},lastUpdated:"2024-02-05",createdDate:"2023-06-15"},{id:"2",routeId:"RT-002",routeName:"Route B - South Delhi",description:"Route covering South Delhi residential areas",area:"South Delhi",startLocation:{name:"School Campus",address:"ABC School, Connaught Place, New Delhi",coordinates:{latitude:28.6315,longitude:77.2167}},endLocation:{name:"School Campus",address:"ABC School, Connaught Place, New Delhi",coordinates:{latitude:28.6315,longitude:77.2167}},stops:[{id:"S5",name:"Lajpat Nagar",address:"Lajpat Nagar Central Market, Delhi",coordinates:{latitude:28.5678,longitude:77.2434},estimatedTime:"07:20",studentsCount:5,landmark:"Central Market",stopType:"both",sequence:1},{id:"S6",name:"Defence Colony",address:"Defence Colony Market, Delhi",coordinates:{latitude:28.5729,longitude:77.2295},estimatedTime:"07:30",studentsCount:3,landmark:"Market Area",stopType:"both",sequence:2},{id:"S7",name:"Khan Market",address:"Khan Market, Delhi",coordinates:{latitude:28.5984,longitude:77.2295},estimatedTime:"07:40",studentsCount:2,landmark:"Main Market",stopType:"both",sequence:3}],schedule:{morningPickup:{startTime:"07:15",endTime:"08:00",estimatedDuration:45},afternoonDrop:{startTime:"14:30",endTime:"15:15",estimatedDuration:45}},distance:{totalKm:18,estimatedTime:45,fuelConsumption:1.5},vehicleAssignment:{vehicleId:"VEH-2024-002",vehicleNumber:"DL-02-CD-5678",driverId:"DRV-002",driverName:"Amit Sharma"},capacity:{maxStudents:12,currentStudents:10,availableSeats:2},operatingDays:["Monday","Tuesday","Wednesday","Thursday","Friday"],status:"active",safetyFeatures:{schoolZones:2,trafficSignals:6,speedBreakers:8,riskLevel:"low",emergencyContacts:["+91-9876543220"]},performance:{onTimePercentage:95,averageDelay:2,studentSatisfaction:4.5,fuelEfficiency:12,incidentCount:0},costs:{dailyOperatingCost:800,monthlyFuelCost:12e3,maintenanceCost:3e3,driverSalary:22e3,totalMonthlyCost:37e3},lastUpdated:"2024-02-03",createdDate:"2023-08-20"}].filter(e=>{let s=e.routeName.toLowerCase().includes(d.toLowerCase())||e.routeId.toLowerCase().includes(d.toLowerCase())||e.area.toLowerCase().includes(d.toLowerCase())||e.vehicleAssignment.vehicleNumber?.toLowerCase().includes(d.toLowerCase())||e.vehicleAssignment.driverName?.toLowerCase().includes(d.toLowerCase()),t="all"===c||e.area===c,a="all"===u||e.status===u;return s&&t&&a});return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Route Planning & Management"}),a.jsx("p",{className:"text-gray-600",children:"Manage and optimize school transport routes"})]}),(0,a.jsxs)("button",{onClick:()=>s?.(),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2",children:[a.jsx("span",{children:"+"}),a.jsx("span",{children:"Create New Route"})]})]}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border p-4 mb-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search Routes"}),a.jsx("input",{type:"text",placeholder:"Search by route name, ID, area, vehicle...",value:d,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"area-select",className:"block text-sm font-medium text-gray-700 mb-1",children:"Area"}),(0,a.jsxs)("select",{id:"area-select",value:c,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"all",children:"All Areas"}),a.jsx("option",{value:"Central Delhi",children:"Central Delhi"}),a.jsx("option",{value:"South Delhi",children:"South Delhi"}),a.jsx("option",{value:"North Delhi",children:"North Delhi"}),a.jsx("option",{value:"East Delhi",children:"East Delhi"}),a.jsx("option",{value:"West Delhi",children:"West Delhi"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"status-select",className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{id:"status-select",value:u,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"all",children:"All Status"}),a.jsx("option",{value:"active",children:"Active"}),a.jsx("option",{value:"inactive",children:"Inactive"}),a.jsx("option",{value:"under_review",children:"Under Review"}),a.jsx("option",{value:"suspended",children:"Suspended"})]})]}),a.jsx("div",{className:"flex items-end",children:a.jsx("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Routes"})})]})}),a.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:f.map(e=>(0,a.jsxs)(r.Zb,{className:"hover:shadow-lg transition-shadow cursor-pointer",children:[a.jsx(r.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(r.ll,{className:"text-lg",children:e.routeName}),(0,a.jsxs)(r.SZ,{className:"text-sm",children:[e.routeId," • ",e.area]})]}),a.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${g(e.status)}`,children:e.status.replace("_"," ").toUpperCase()})]})}),a.jsx(r.aY,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Distance:"}),(0,a.jsxs)("div",{className:"text-gray-600",children:[e.distance.totalKm," km"]})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Duration:"}),(0,a.jsxs)("div",{className:"text-gray-600",children:[e.distance.estimatedTime," min"]})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Students:"}),(0,a.jsxs)("div",{className:"text-gray-600",children:[e.capacity.currentStudents,"/",e.capacity.maxStudents]})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Stops:"}),a.jsx("div",{className:"text-gray-600",children:e.stops.length})]})]}),(0,a.jsxs)("div",{className:"border-t pt-3",children:[(0,a.jsxs)("div",{className:"text-sm",children:[a.jsx("span",{className:"font-medium",children:"Vehicle:"}),a.jsx("div",{className:"text-gray-600",children:e.vehicleAssignment.vehicleNumber||"Not assigned"})]}),(0,a.jsxs)("div",{className:"text-sm mt-1",children:[a.jsx("span",{className:"font-medium",children:"Driver:"}),a.jsx("div",{className:"text-gray-600",children:e.vehicleAssignment.driverName||"Not assigned"})]})]}),a.jsx("div",{className:"border-t pt-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Performance:"}),(0,a.jsxs)("div",{className:"text-gray-600",children:[e.performance.onTimePercentage,"% on-time"]})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Risk:"}),a.jsx("span",{className:`ml-1 px-2 py-1 text-xs rounded-full ${j(e.safetyFeatures.riskLevel)}`,children:e.safetyFeatures.riskLevel.toUpperCase()})]})]})}),(0,a.jsxs)("div",{className:"border-t pt-3 flex space-x-2",children:[a.jsx("button",{onClick:()=>p(e),className:"flex-1 bg-blue-50 text-blue-700 px-3 py-2 rounded text-sm hover:bg-blue-100",children:"View Details"}),a.jsx("button",{onClick:()=>i?.(e.id),className:"bg-green-50 text-green-700 px-3 py-2 rounded text-sm hover:bg-green-100",children:"Optimize"}),a.jsx("button",{onClick:()=>t?.(e.id),className:"bg-gray-50 text-gray-700 px-3 py-2 rounded text-sm hover:bg-gray-100",children:"Edit"})]})]})})]},e.id))}),0===f.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-500 text-lg",children:"No routes found matching your criteria"}),a.jsx("button",{onClick:()=>s?.(),className:"mt-4 bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700",children:"Create Your First Route"})]}),a.jsx(()=>h?a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h3",{className:"text-xl font-semibold",children:h.routeName}),a.jsx("button",{onClick:()=>p(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium mb-3",children:"Route Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Route ID:"})," ",h.routeId]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Area:"})," ",h.area]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Total Distance:"})," ",h.distance.totalKm," km"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Estimated Time:"})," ",h.distance.estimatedTime," minutes"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Fuel Consumption:"})," ",h.distance.fuelConsumption," L/trip"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Status:"}),a.jsx("span",{className:`ml-2 px-2 py-1 text-xs rounded-full ${g(h.status)}`,children:h.status.replace("_"," ").toUpperCase()})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Operating Days:"})," ",h.operatingDays.length," days/week"]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium mb-3",children:"Capacity & Assignment"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Max Capacity:"})," ",h.capacity.maxStudents]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Current Students:"})," ",h.capacity.currentStudents]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Available Seats:"})," ",h.capacity.availableSeats]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Occupancy Rate:"})," ",Math.round(h.capacity.currentStudents/h.capacity.maxStudents*100),"%"]}),h.vehicleAssignment.vehicleNumber&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Vehicle:"})," ",h.vehicleAssignment.vehicleNumber]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Driver:"})," ",h.vehicleAssignment.driverName]}),h.vehicleAssignment.conductorName&&(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Conductor:"})," ",h.vehicleAssignment.conductorName]})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium mb-3",children:"Schedule"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Morning Pickup:"}),(0,a.jsxs)("div",{className:"text-gray-600 ml-2",children:[h.schedule.morningPickup.startTime," - ",h.schedule.morningPickup.endTime]}),(0,a.jsxs)("div",{className:"text-gray-600 ml-2",children:["Duration: ",h.schedule.morningPickup.estimatedDuration," minutes"]})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Afternoon Drop:"}),(0,a.jsxs)("div",{className:"text-gray-600 ml-2",children:[h.schedule.afternoonDrop.startTime," - ",h.schedule.afternoonDrop.endTime]}),(0,a.jsxs)("div",{className:"text-gray-600 ml-2",children:["Duration: ",h.schedule.afternoonDrop.estimatedDuration," minutes"]})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h4",{className:"font-medium mb-3",children:"Description"}),a.jsx("p",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded",children:h.description})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"font-medium mb-3",children:["Route Stops (",h.stops.length,")"]}),a.jsx("div",{className:"space-y-3",children:h.stops.map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium",children:["Stop ",e.sequence]}),a.jsx("h5",{className:"font-medium",children:e.name}),a.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${v(e.stopType)}`,children:e.stopType.toUpperCase()})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[e.studentsCount," students • ",e.estimatedTime]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[a.jsx("div",{children:e.address}),e.landmark&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Landmark: ",e.landmark]})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium mb-3",children:"Safety Features"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm bg-gray-50 p-3 rounded",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"School Zones:"})," ",h.safetyFeatures.schoolZones]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Traffic Signals:"})," ",h.safetyFeatures.trafficSignals]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Speed Breakers:"})," ",h.safetyFeatures.speedBreakers]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Risk Level:"}),a.jsx("span",{className:`ml-2 px-2 py-1 text-xs rounded-full ${j(h.safetyFeatures.riskLevel)}`,children:h.safetyFeatures.riskLevel.toUpperCase()})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Emergency Contacts:"}),a.jsx("div",{className:"mt-1",children:h.safetyFeatures.emergencyContacts.map((e,s)=>a.jsx("div",{className:"text-gray-600",children:e},s))})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium mb-3",children:"Performance Metrics"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm bg-gray-50 p-3 rounded",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"On-Time Performance:"})," ",h.performance.onTimePercentage,"%"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Average Delay:"})," ",h.performance.averageDelay," minutes"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Student Satisfaction:"})," ⭐ ",h.performance.studentSatisfaction,"/5"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Fuel Efficiency:"})," ",h.performance.fuelEfficiency," km/l"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Incident Count:"})," ",h.performance.incidentCount," (this month)"]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h4",{className:"font-medium mb-3",children:"Cost Analysis"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-3 rounded",children:[a.jsx("div",{className:"font-medium text-blue-800",children:"Daily Operating"}),(0,a.jsxs)("div",{className:"text-lg font-bold text-blue-600",children:["₹",h.costs.dailyOperatingCost]})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-3 rounded",children:[a.jsx("div",{className:"font-medium text-green-800",children:"Monthly Fuel"}),(0,a.jsxs)("div",{className:"text-lg font-bold text-green-600",children:["₹",h.costs.monthlyFuelCost.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 p-3 rounded",children:[a.jsx("div",{className:"font-medium text-yellow-800",children:"Maintenance"}),(0,a.jsxs)("div",{className:"text-lg font-bold text-yellow-600",children:["₹",h.costs.maintenanceCost.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"bg-purple-50 p-3 rounded",children:[a.jsx("div",{className:"font-medium text-purple-800",children:"Driver Salary"}),(0,a.jsxs)("div",{className:"text-lg font-bold text-purple-600",children:["₹",h.costs.driverSalary.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"bg-red-50 p-3 rounded",children:[a.jsx("div",{className:"font-medium text-red-800",children:"Total Monthly"}),(0,a.jsxs)("div",{className:"text-lg font-bold text-red-600",children:["₹",h.costs.totalMonthlyCost.toLocaleString()]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h4",{className:"font-medium mb-3",children:"Operating Days"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"].map(e=>a.jsx("span",{className:`px-3 py-1 text-sm rounded-full ${h.operatingDays.includes(e)?"bg-green-100 text-green-800":"bg-gray-100 text-gray-500"}`,children:e},e))})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[a.jsx("button",{onClick:()=>i?.(h.id),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Optimize Route"}),a.jsx("button",{onClick:()=>t?.(h.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Edit Route"}),a.jsx("button",{className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700",children:"Generate Report"})]})]})}):null,{})]})}function l(){return a.jsx(i,{tenantId:"demo-tenant-uuid-1234567890",onCreateRoute:()=>{console.log("Create new route")},onEditRoute:e=>{console.log("Edit route:",e)},onOptimizeRoute:e=>{console.log("Optimize route:",e),alert(`Route ${e} optimization started`)},onAssignVehicle:(e,s)=>{console.log("Assign vehicle to route:",{routeId:e,vehicleId:s}),alert(`Vehicle ${s} assigned to route ${e}`)}})}},18791:(e,s,t)=>{"use strict";t.d(s,{Zb:()=>d,aY:()=>u,SZ:()=>m,Ol:()=>o,ll:()=>c});var a=t(95344),n=t(3729),r=t(56815),i=t(79377);function l(...e){return(0,i.m6)((0,r.W)(e))}let d=n.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:l("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));d.displayName="Card";let o=n.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:l("flex flex-col space-y-1.5 p-6",e),...s}));o.displayName="CardHeader";let c=n.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:l("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let m=n.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:l("text-sm text-muted-foreground",e),...s}));m.displayName="CardDescription";let u=n.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:l("p-6 pt-0",e),...s}));u.displayName="CardContent",n.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:l("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},13069:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>r,__esModule:()=>n,default:()=>i});let a=(0,t(86843).createProxy)(String.raw`/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/transport/routes/page.tsx`),{__esModule:n,$$typeof:r}=a,i=a.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[6225,3883,3945,1476,8510],()=>t(65403));module.exports=a})();