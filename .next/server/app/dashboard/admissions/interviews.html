<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/admissions/interviews/page-388b3cbc7a369088.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Admission Interviews</h1><p class="text-muted-foreground">Schedule, conduct, and manage student admission interviews</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Interview Report</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Schedule Interview</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Scheduled Interviews</h3><span class="text-2xl">📅</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">89</div><p class="text-xs text-muted-foreground">This week</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Completed</h3><span class="text-2xl">✅</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">234</div><p class="text-xs text-muted-foreground">This month</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Pending Feedback</h3><span class="text-2xl">📝</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">23</div><p class="text-xs text-muted-foreground">Awaiting scores</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Success Rate</h3><span class="text-2xl">🎯</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">78%</div><p class="text-xs text-muted-foreground">Pass rate</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Today&#x27;s Interview Schedule</h3><p class="text-sm text-muted-foreground">Interviews scheduled for today - <!-- -->7/10/2025</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Aarav Sharma</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9</span><span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Scheduled</span></div><p class="text-blue-600 font-medium">APP-2024-001</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">🕒 Time:</span> <!-- -->09:00 AM</p><p><span class="font-medium">⏱️ Duration:</span> <!-- -->30 mins</p><p><span class="font-medium">🏢 Room:</span> <!-- -->Conference Room A</p></div><div><p><span class="font-medium">👨‍🏫 Interviewer:</span> <!-- -->Dr. Priya Patel</p><p><span class="font-medium">📞 Parent Contact:</span> <!-- -->+91-9876543210</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm">Start Interview</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Reschedule</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Application</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Kavya Gupta</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 6</span><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">In Progress</span></div><p class="text-blue-600 font-medium">APP-2024-015</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">🕒 Time:</span> <!-- -->09:30 AM</p><p><span class="font-medium">⏱️ Duration:</span> <!-- -->30 mins</p><p><span class="font-medium">🏢 Room:</span> <!-- -->Conference Room B</p></div><div><p><span class="font-medium">👨‍🏫 Interviewer:</span> <!-- -->Mr. Rajesh Kumar</p><p><span class="font-medium">📞 Parent Contact:</span> <!-- -->+91-9876543211</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Complete Interview</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Application</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Arjun Singh</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 11</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Completed</span></div><p class="text-blue-600 font-medium">APP-2024-032</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">🕒 Time:</span> <!-- -->10:00 AM</p><p><span class="font-medium">⏱️ Duration:</span> <!-- -->45 mins</p><p><span class="font-medium">🏢 Room:</span> <!-- -->Conference Room A</p></div><div><p><span class="font-medium">👨‍🏫 Interviewer:</span> <!-- -->Ms. Sneha Gupta</p><p><span class="font-medium">📞 Parent Contact:</span> <!-- -->+91-9876543212</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 text-sm">View Feedback</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Application</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Riya Patel</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 3</span><span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Scheduled</span></div><p class="text-blue-600 font-medium">APP-2024-048</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">🕒 Time:</span> <!-- -->10:45 AM</p><p><span class="font-medium">⏱️ Duration:</span> <!-- -->20 mins</p><p><span class="font-medium">🏢 Room:</span> <!-- -->Conference Room C</p></div><div><p><span class="font-medium">👨‍🏫 Interviewer:</span> <!-- -->Dr. Amit Verma</p><p><span class="font-medium">📞 Parent Contact:</span> <!-- -->+91-9876543213</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm">Start Interview</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Reschedule</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Application</button></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Interview Evaluation</h3><p class="text-sm text-muted-foreground">Complete interview feedback and scoring</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="grid gap-4 md:grid-cols-2"><div><label class="text-sm font-medium">Student Name</label><input type="text" disabled="" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50" value="Arjun Singh"/></div><div><label class="text-sm font-medium">Application ID</label><input type="text" disabled="" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50" value="APP-2024-032"/></div></div><div class="grid gap-4 md:grid-cols-2"><div><label class="text-sm font-medium">Interviewer</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Interviewer</option><option value="dr-priya">Dr. Priya Patel</option><option value="mr-rajesh">Mr. Rajesh Kumar</option><option value="ms-sneha">Ms. Sneha Gupta</option><option value="dr-amit">Dr. Amit Verma</option></select></div><div><label class="text-sm font-medium">Interview Date</label><input type="date" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"/></div></div><div class="space-y-4"><h3 class="text-lg font-semibold">Evaluation Criteria</h3><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium">Academic Knowledge</h4><p class="text-sm text-gray-600">Understanding of subject matter</p></div><div class="flex space-x-2"><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">1</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">2</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">3</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">4</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">5</button></div></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium">Communication Skills</h4><p class="text-sm text-gray-600">Clarity and confidence in expression</p></div><div class="flex space-x-2"><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">1</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">2</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">3</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">4</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">5</button></div></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium">Problem Solving</h4><p class="text-sm text-gray-600">Analytical and logical thinking</p></div><div class="flex space-x-2"><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">1</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">2</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">3</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">4</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">5</button></div></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium">Personality &amp; Attitude</h4><p class="text-sm text-gray-600">Enthusiasm and positive attitude</p></div><div class="flex space-x-2"><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">1</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">2</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">3</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">4</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">5</button></div></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium">Extracurricular Interest</h4><p class="text-sm text-gray-600">Involvement in activities beyond academics</p></div><div class="flex space-x-2"><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">1</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">2</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">3</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">4</button><button class="w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm">5</button></div></div></div></div><div><label class="text-sm font-medium">Additional Comments</label><textarea rows="4" placeholder="Provide detailed feedback about the student&#x27;s performance..." class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"></textarea></div><div class="grid gap-4 md:grid-cols-2"><div><label class="text-sm font-medium">Overall Recommendation</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Recommendation</option><option value="strongly-recommend">Strongly Recommend</option><option value="recommend">Recommend</option><option value="conditional">Conditional Admission</option><option value="not-recommend">Not Recommend</option></select></div><div><label class="text-sm font-medium">Overall Score</label><input type="number" min="0" max="100" placeholder="0-100" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"/></div></div><div class="flex space-x-2"><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">Submit Feedback</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Save as Draft</button><button class="text-red-600 hover:text-red-800">Cancel</button></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Interview Performance</h3><p class="text-sm text-muted-foreground">Success rates by grade level</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Nursery</span><p class="text-xs text-gray-500">42<!-- -->/<!-- -->45<!-- --> passed</p></div><div class="flex items-center space-x-2"><div class="w-20 bg-gray-200 rounded-full h-2"><div class="bg-green-600 h-2 rounded-full" style="width:93%"></div></div><span class="text-sm font-medium">93<!-- -->%</span></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Grade 1</span><p class="text-xs text-gray-500">58<!-- -->/<!-- -->67<!-- --> passed</p></div><div class="flex items-center space-x-2"><div class="w-20 bg-gray-200 rounded-full h-2"><div class="bg-green-600 h-2 rounded-full" style="width:87%"></div></div><span class="text-sm font-medium">87<!-- -->%</span></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Grade 6</span><p class="text-xs text-gray-500">71<!-- -->/<!-- -->89<!-- --> passed</p></div><div class="flex items-center space-x-2"><div class="w-20 bg-gray-200 rounded-full h-2"><div class="bg-green-600 h-2 rounded-full" style="width:80%"></div></div><span class="text-sm font-medium">80<!-- -->%</span></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Grade 9</span><p class="text-xs text-gray-500">95<!-- -->/<!-- -->123<!-- --> passed</p></div><div class="flex items-center space-x-2"><div class="w-20 bg-gray-200 rounded-full h-2"><div class="bg-green-600 h-2 rounded-full" style="width:77%"></div></div><span class="text-sm font-medium">77<!-- -->%</span></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Grade 11</span><p class="text-xs text-gray-500">56<!-- -->/<!-- -->78<!-- --> passed</p></div><div class="flex items-center space-x-2"><div class="w-20 bg-gray-200 rounded-full h-2"><div class="bg-green-600 h-2 rounded-full" style="width:72%"></div></div><span class="text-sm font-medium">72<!-- -->%</span></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Upcoming Interviews</h3><p class="text-sm text-muted-foreground">Next week&#x27;s schedule</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between border-b pb-2"><div><p class="font-medium text-sm">Feb 19</p><p class="text-xs text-gray-500">Dr. Priya Patel</p></div><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">12<!-- --> interviews</span></div><div class="flex items-center justify-between border-b pb-2"><div><p class="font-medium text-sm">Feb 20</p><p class="text-xs text-gray-500">Mr. Rajesh Kumar</p></div><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">15<!-- --> interviews</span></div><div class="flex items-center justify-between border-b pb-2"><div><p class="font-medium text-sm">Feb 21</p><p class="text-xs text-gray-500">Ms. Sneha Gupta</p></div><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">8<!-- --> interviews</span></div><div class="flex items-center justify-between border-b pb-2"><div><p class="font-medium text-sm">Feb 22</p><p class="text-xs text-gray-500">Dr. Amit Verma</p></div><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">18<!-- --> interviews</span></div><div class="flex items-center justify-between border-b pb-2"><div><p class="font-medium text-sm">Feb 23</p><p class="text-xs text-gray-500">Dr. Priya Patel</p></div><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">10<!-- --> interviews</span></div></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[4024,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"2911\",\"static/chunks/app/dashboard/admissions/interviews/page-388b3cbc7a369088.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"DEc8SW0ph9C_XxX68bz_T\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/admissions/interviews\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"admissions\",{\"children\":[\"interviews\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"admissions\",{\"children\":[\"interviews\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"admissions\",\"children\",\"interviews\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"admissions\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>