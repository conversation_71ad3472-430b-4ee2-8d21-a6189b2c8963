<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/admissions/decisions/page-4ae7035a29f8206d.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Admission Decisions</h1><p class="text-muted-foreground">Make admission decisions and send notifications to applicants</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Bulk Actions</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Send Notifications</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Pending Decisions</h3><span class="text-2xl">⏳</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">45</div><p class="text-xs text-muted-foreground">Awaiting decision</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Approved</h3><span class="text-2xl">✅</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">456</div><p class="text-xs text-muted-foreground">85% acceptance rate</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Rejected</h3><span class="text-2xl">❌</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">89</div><p class="text-xs text-muted-foreground">15% rejection rate</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Waitlisted</h3><span class="text-2xl">⏰</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">23</div><p class="text-xs text-muted-foreground">On waiting list</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Applications Pending Decision</h3><p class="text-sm text-muted-foreground">Students awaiting admission decision</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Aarav Sharma</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Strongly Recommend</span><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">High<!-- --> Priority</span></div><p class="text-blue-600 font-medium">APP-2024-001</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📊 Interview Score:</span> <!-- -->85<!-- -->/100</p><p><span class="font-medium">📚 Academic Score:</span> <!-- -->92<!-- -->/100</p><p><span class="font-medium">🎯 Overall Score:</span> <!-- -->88<!-- -->/100</p></div><div><p><span class="font-medium">👨‍🏫 Interviewer:</span> <!-- -->Dr. Priya Patel</p><p><span class="font-medium">📅 Applied:</span> <!-- -->2024-01-15</p><p><span class="font-medium">🎯 Interviewed:</span> <!-- -->2024-02-10</p></div></div><div class="mt-3"><div class="w-full bg-gray-200 rounded-full h-2"><div class="h-2 rounded-full bg-green-600" style="width:88%"></div></div><p class="text-xs text-gray-500 mt-1">Overall Score: <!-- -->88<!-- -->/100</p></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm">Approve</button><button class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 text-sm">Reject</button><button class="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 text-sm">Waitlist</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Kavya Gupta</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 6</span><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Recommend</span><span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Medium<!-- --> Priority</span></div><p class="text-blue-600 font-medium">APP-2024-015</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📊 Interview Score:</span> <!-- -->78<!-- -->/100</p><p><span class="font-medium">📚 Academic Score:</span> <!-- -->85<!-- -->/100</p><p><span class="font-medium">🎯 Overall Score:</span> <!-- -->82<!-- -->/100</p></div><div><p><span class="font-medium">👨‍🏫 Interviewer:</span> <!-- -->Mr. Rajesh Kumar</p><p><span class="font-medium">📅 Applied:</span> <!-- -->2024-01-18</p><p><span class="font-medium">🎯 Interviewed:</span> <!-- -->2024-02-12</p></div></div><div class="mt-3"><div class="w-full bg-gray-200 rounded-full h-2"><div class="h-2 rounded-full bg-yellow-600" style="width:82%"></div></div><p class="text-xs text-gray-500 mt-1">Overall Score: <!-- -->82<!-- -->/100</p></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm">Approve</button><button class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 text-sm">Reject</button><button class="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 text-sm">Waitlist</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Arjun Singh</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 11</span><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Conditional</span><span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Medium<!-- --> Priority</span></div><p class="text-blue-600 font-medium">APP-2024-032</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📊 Interview Score:</span> <!-- -->72<!-- -->/100</p><p><span class="font-medium">📚 Academic Score:</span> <!-- -->88<!-- -->/100</p><p><span class="font-medium">🎯 Overall Score:</span> <!-- -->80<!-- -->/100</p></div><div><p><span class="font-medium">👨‍🏫 Interviewer:</span> <!-- -->Ms. Sneha Gupta</p><p><span class="font-medium">📅 Applied:</span> <!-- -->2024-01-20</p><p><span class="font-medium">🎯 Interviewed:</span> <!-- -->2024-02-14</p></div></div><div class="mt-3"><div class="w-full bg-gray-200 rounded-full h-2"><div class="h-2 rounded-full bg-yellow-600" style="width:80%"></div></div><p class="text-xs text-gray-500 mt-1">Overall Score: <!-- -->80<!-- -->/100</p></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm">Approve</button><button class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 text-sm">Reject</button><button class="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 text-sm">Waitlist</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Decision Statistics</h3><p class="text-sm text-muted-foreground">Admission decisions by grade</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><span class="font-medium">Nursery</span><span class="text-sm text-gray-600">53<!-- --> total</span></div><div class="flex space-x-2 text-xs"><span class="px-2 py-1 bg-green-100 text-green-800 rounded">42<!-- --> Approved</span><span class="px-2 py-1 bg-red-100 text-red-800 rounded">8<!-- --> Rejected</span><span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">3<!-- --> Waitlisted</span></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><span class="font-medium">Grade 1</span><span class="text-sm text-gray-600">75<!-- --> total</span></div><div class="flex space-x-2 text-xs"><span class="px-2 py-1 bg-green-100 text-green-800 rounded">58<!-- --> Approved</span><span class="px-2 py-1 bg-red-100 text-red-800 rounded">12<!-- --> Rejected</span><span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">5<!-- --> Waitlisted</span></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><span class="font-medium">Grade 6</span><span class="text-sm text-gray-600">94<!-- --> total</span></div><div class="flex space-x-2 text-xs"><span class="px-2 py-1 bg-green-100 text-green-800 rounded">71<!-- --> Approved</span><span class="px-2 py-1 bg-red-100 text-red-800 rounded">15<!-- --> Rejected</span><span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">8<!-- --> Waitlisted</span></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><span class="font-medium">Grade 9</span><span class="text-sm text-gray-600">127<!-- --> total</span></div><div class="flex space-x-2 text-xs"><span class="px-2 py-1 bg-green-100 text-green-800 rounded">95<!-- --> Approved</span><span class="px-2 py-1 bg-red-100 text-red-800 rounded">20<!-- --> Rejected</span><span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">12<!-- --> Waitlisted</span></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><span class="font-medium">Grade 11</span><span class="text-sm text-gray-600">80<!-- --> total</span></div><div class="flex space-x-2 text-xs"><span class="px-2 py-1 bg-green-100 text-green-800 rounded">56<!-- --> Approved</span><span class="px-2 py-1 bg-red-100 text-red-800 rounded">18<!-- --> Rejected</span><span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">6<!-- --> Waitlisted</span></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Decisions</h3><p class="text-sm text-muted-foreground">Latest admission decisions made</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between border-b pb-2"><div><p class="font-medium text-sm">Riya Patel</p><p class="text-xs text-gray-500">Grade 3<!-- --> • Score: <!-- -->88</p><p class="text-xs text-gray-400">2024-02-15</p></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Approved</span></div><div class="flex items-center justify-between border-b pb-2"><div><p class="font-medium text-sm">Vikram Singh</p><p class="text-xs text-gray-500">Grade 10<!-- --> • Score: <!-- -->65</p><p class="text-xs text-gray-400">2024-02-15</p></div><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Rejected</span></div><div class="flex items-center justify-between border-b pb-2"><div><p class="font-medium text-sm">Ananya Gupta</p><p class="text-xs text-gray-500">Grade 7<!-- --> • Score: <!-- -->92</p><p class="text-xs text-gray-400">2024-02-14</p></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Approved</span></div><div class="flex items-center justify-between border-b pb-2"><div><p class="font-medium text-sm">Rohit Kumar</p><p class="text-xs text-gray-500">Grade 12<!-- --> • Score: <!-- -->75</p><p class="text-xs text-gray-400">2024-02-14</p></div><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Waitlisted</span></div><div class="flex items-center justify-between border-b pb-2"><div><p class="font-medium text-sm">Priya Sharma</p><p class="text-xs text-gray-500">Grade 5<!-- --> • Score: <!-- -->85</p><p class="text-xs text-gray-400">2024-02-13</p></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Approved</span></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Bulk Decision Actions</h3><p class="text-sm text-muted-foreground">Process multiple applications at once</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-3"><div class="border rounded-lg p-4 text-center"><h3 class="font-semibold mb-2">Auto-Approve High Scorers</h3><p class="text-sm text-gray-600 mb-3">Automatically approve applications with score ≥ 85</p><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm">Auto-Approve (12 applications)</button></div><div class="border rounded-lg p-4 text-center"><h3 class="font-semibold mb-2">Waitlist Borderline Cases</h3><p class="text-sm text-gray-600 mb-3">Waitlist applications with score 70-75</p><button class="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 text-sm">Auto-Waitlist (8 applications)</button></div><div class="border rounded-lg p-4 text-center"><h3 class="font-semibold mb-2">Send Decision Letters</h3><p class="text-sm text-gray-600 mb-3">Send notifications to all decided applications</p><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Send Notifications (156 letters)</button></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[683,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"855\",\"static/chunks/app/dashboard/admissions/decisions/page-4ae7035a29f8206d.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"DEc8SW0ph9C_XxX68bz_T\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/admissions/decisions\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"admissions\",{\"children\":[\"decisions\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"admissions\",{\"children\":[\"decisions\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"admissions\",\"children\",\"decisions\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"admissions\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>