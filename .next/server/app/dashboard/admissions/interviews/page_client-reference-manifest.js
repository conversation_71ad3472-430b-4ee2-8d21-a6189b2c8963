globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/admissions/interviews/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"1778":{"*":{"id":"9295","name":"*","chunks":[],"async":false}},"1902":{"*":{"id":"13225","name":"*","chunks":[],"async":false}},"4024":{"*":{"id":"52268","name":"*","chunks":[],"async":false}},"5250":{"*":{"id":"61476","name":"*","chunks":[],"async":false}},"5613":{"*":{"id":"38771","name":"*","chunks":[],"async":false}},"7108":{"*":{"id":"65485","name":"*","chunks":[],"async":false}},"7690":{"*":{"id":"2583","name":"*","chunks":[],"async":false}},"7831":{"*":{"id":"43982","name":"*","chunks":[],"async":false}},"8955":{"*":{"id":"26840","name":"*","chunks":[],"async":false}},"9860":{"*":{"id":"75375","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/components/app-router.js":{"id":7690,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/components/app-router.js":{"id":7690,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/components/error-boundary.js":{"id":8955,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":8955,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/components/layout-router.js":{"id":5613,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/components/layout-router.js":{"id":5613,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/components/not-found-boundary.js":{"id":1902,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":1902,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/components/render-from-template-context.js":{"id":1778,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":1778,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"id":7831,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/components/static-generation-searchparams-bailout-provider.js":{"id":7831,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":9646,"name":"*","chunks":["3185","static/chunks/app/layout-42225a73217736f3.js"],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/globals.css":{"id":9103,"name":"*","chunks":["3185","static/chunks/app/layout-42225a73217736f3.js"],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/link.js":{"id":5250,"name":"*","chunks":["5250","static/chunks/5250-c6905a3e4a59cbd2.js","1931","static/chunks/app/page-4cda9109a41ae052.js"],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/link.js":{"id":5250,"name":"*","chunks":["5250","static/chunks/5250-c6905a3e4a59cbd2.js","1931","static/chunks/app/page-4cda9109a41ae052.js"],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/layout.tsx":{"id":7108,"name":"*","chunks":["5250","static/chunks/5250-c6905a3e4a59cbd2.js","7663","static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js"],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/admissions/page.tsx":{"id":9860,"name":"*","chunks":["7895","static/chunks/7895-dcf2d3b63c7719ea.js","5250","static/chunks/5250-c6905a3e4a59cbd2.js","7453","static/chunks/app/dashboard/admissions/page-2c74199f450415f1.js"],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/admissions/interviews/page.tsx":{"id":4024,"name":"*","chunks":["7895","static/chunks/7895-dcf2d3b63c7719ea.js","2911","static/chunks/app/dashboard/admissions/interviews/page-388b3cbc7a369088.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/_not-found":[],"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout":["static/css/1efa94dc89f20134.css"],"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/page":[],"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/layout":[],"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/page":[],"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/admissions/page":[],"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/admissions/interviews/page":[]}}