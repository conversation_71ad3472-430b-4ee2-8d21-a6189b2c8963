<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/admissions/classes/page-4da57e1450ca8da1.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Class Allocation</h1><p class="text-muted-foreground">Allocate admitted students to classes and sections</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Auto-Allocate</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Finalize Allocation</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Admitted Students</h3><span class="text-2xl">🎓</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">456</div><p class="text-xs text-muted-foreground">Ready for allocation</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Allocated</h3><span class="text-2xl">✅</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">398</div><p class="text-xs text-muted-foreground">87% allocated</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Pending Allocation</h3><span class="text-2xl">⏳</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">58</div><p class="text-xs text-muted-foreground">Awaiting placement</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Available Seats</h3><span class="text-2xl">🪑</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">142</div><p class="text-xs text-muted-foreground">Across all classes</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Class Capacity Overview</h3><p class="text-sm text-muted-foreground">Current occupancy and available seats by grade</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><div><h3 class="text-lg font-semibold">Nursery</h3><p class="text-sm text-gray-600">2<!-- --> sections: <!-- -->A, B</p></div><div class="text-right"><p class="text-sm font-medium">53<!-- -->/<!-- -->60<!-- --> allocated</p><p class="text-xs text-gray-600">5<!-- --> pending allocation</p></div></div><div class="grid grid-cols-4 gap-4 text-sm mb-3"><div class="text-center"><p class="font-medium text-gray-700">Total Capacity</p><p class="text-lg font-bold text-blue-600">60</p></div><div class="text-center"><p class="font-medium text-gray-700">Allocated</p><p class="text-lg font-bold text-green-600">53</p></div><div class="text-center"><p class="font-medium text-gray-700">Available</p><p class="text-lg font-bold text-orange-600">7</p></div><div class="text-center"><p class="font-medium text-gray-700">Pending</p><p class="text-lg font-bold text-red-600">5</p></div></div><div class="mb-2"><div class="w-full bg-gray-200 rounded-full h-3"><div class="flex h-3 rounded-full overflow-hidden"><div class="bg-green-600" style="width:88.33333333333333%"></div><div class="bg-yellow-600" style="width:8.333333333333332%"></div></div></div><div class="flex justify-between text-xs text-gray-500 mt-1"><span>Allocated: <!-- -->88<!-- -->%</span><span>Available: <!-- -->7<!-- --> seats</span></div></div><div class="flex space-x-2"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View Sections</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">Allocate Students</button></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><div><h3 class="text-lg font-semibold">Grade 1</h3><p class="text-sm text-gray-600">3<!-- --> sections: <!-- -->A, B, C</p></div><div class="text-right"><p class="text-sm font-medium">75<!-- -->/<!-- -->90<!-- --> allocated</p><p class="text-xs text-gray-600">8<!-- --> pending allocation</p></div></div><div class="grid grid-cols-4 gap-4 text-sm mb-3"><div class="text-center"><p class="font-medium text-gray-700">Total Capacity</p><p class="text-lg font-bold text-blue-600">90</p></div><div class="text-center"><p class="font-medium text-gray-700">Allocated</p><p class="text-lg font-bold text-green-600">75</p></div><div class="text-center"><p class="font-medium text-gray-700">Available</p><p class="text-lg font-bold text-orange-600">15</p></div><div class="text-center"><p class="font-medium text-gray-700">Pending</p><p class="text-lg font-bold text-red-600">8</p></div></div><div class="mb-2"><div class="w-full bg-gray-200 rounded-full h-3"><div class="flex h-3 rounded-full overflow-hidden"><div class="bg-green-600" style="width:83.33333333333334%"></div><div class="bg-yellow-600" style="width:8.88888888888889%"></div></div></div><div class="flex justify-between text-xs text-gray-500 mt-1"><span>Allocated: <!-- -->83<!-- -->%</span><span>Available: <!-- -->15<!-- --> seats</span></div></div><div class="flex space-x-2"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View Sections</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">Allocate Students</button></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><div><h3 class="text-lg font-semibold">Grade 6</h3><p class="text-sm text-gray-600">3<!-- --> sections: <!-- -->A, B, C</p></div><div class="text-right"><p class="text-sm font-medium">82<!-- -->/<!-- -->90<!-- --> allocated</p><p class="text-xs text-gray-600">12<!-- --> pending allocation</p></div></div><div class="grid grid-cols-4 gap-4 text-sm mb-3"><div class="text-center"><p class="font-medium text-gray-700">Total Capacity</p><p class="text-lg font-bold text-blue-600">90</p></div><div class="text-center"><p class="font-medium text-gray-700">Allocated</p><p class="text-lg font-bold text-green-600">82</p></div><div class="text-center"><p class="font-medium text-gray-700">Available</p><p class="text-lg font-bold text-orange-600">8</p></div><div class="text-center"><p class="font-medium text-gray-700">Pending</p><p class="text-lg font-bold text-red-600">12</p></div></div><div class="mb-2"><div class="w-full bg-gray-200 rounded-full h-3"><div class="flex h-3 rounded-full overflow-hidden"><div class="bg-green-600" style="width:91.11111111111111%"></div><div class="bg-yellow-600" style="width:13.333333333333334%"></div></div></div><div class="flex justify-between text-xs text-gray-500 mt-1"><span>Allocated: <!-- -->91<!-- -->%</span><span>Available: <!-- -->8<!-- --> seats</span></div></div><div class="flex space-x-2"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View Sections</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">Allocate Students</button><span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">Oversubscribed</span></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><div><h3 class="text-lg font-semibold">Grade 9</h3><p class="text-sm text-gray-600">4<!-- --> sections: <!-- -->A, B, C, D</p></div><div class="text-right"><p class="text-sm font-medium">105<!-- -->/<!-- -->120<!-- --> allocated</p><p class="text-xs text-gray-600">22<!-- --> pending allocation</p></div></div><div class="grid grid-cols-4 gap-4 text-sm mb-3"><div class="text-center"><p class="font-medium text-gray-700">Total Capacity</p><p class="text-lg font-bold text-blue-600">120</p></div><div class="text-center"><p class="font-medium text-gray-700">Allocated</p><p class="text-lg font-bold text-green-600">105</p></div><div class="text-center"><p class="font-medium text-gray-700">Available</p><p class="text-lg font-bold text-orange-600">15</p></div><div class="text-center"><p class="font-medium text-gray-700">Pending</p><p class="text-lg font-bold text-red-600">22</p></div></div><div class="mb-2"><div class="w-full bg-gray-200 rounded-full h-3"><div class="flex h-3 rounded-full overflow-hidden"><div class="bg-green-600" style="width:87.5%"></div><div class="bg-yellow-600" style="width:18.333333333333332%"></div></div></div><div class="flex justify-between text-xs text-gray-500 mt-1"><span>Allocated: <!-- -->88<!-- -->%</span><span>Available: <!-- -->15<!-- --> seats</span></div></div><div class="flex space-x-2"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View Sections</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">Allocate Students</button><span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">Oversubscribed</span></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><div><h3 class="text-lg font-semibold">Grade 11</h3><p class="text-sm text-gray-600">3<!-- --> sections: <!-- -->Science A, Science B, Commerce A</p></div><div class="text-right"><p class="text-sm font-medium">83<!-- -->/<!-- -->90<!-- --> allocated</p><p class="text-xs text-gray-600">11<!-- --> pending allocation</p></div></div><div class="grid grid-cols-4 gap-4 text-sm mb-3"><div class="text-center"><p class="font-medium text-gray-700">Total Capacity</p><p class="text-lg font-bold text-blue-600">90</p></div><div class="text-center"><p class="font-medium text-gray-700">Allocated</p><p class="text-lg font-bold text-green-600">83</p></div><div class="text-center"><p class="font-medium text-gray-700">Available</p><p class="text-lg font-bold text-orange-600">7</p></div><div class="text-center"><p class="font-medium text-gray-700">Pending</p><p class="text-lg font-bold text-red-600">11</p></div></div><div class="mb-2"><div class="w-full bg-gray-200 rounded-full h-3"><div class="flex h-3 rounded-full overflow-hidden"><div class="bg-green-600" style="width:92.22222222222223%"></div><div class="bg-yellow-600" style="width:12.222222222222221%"></div></div></div><div class="flex justify-between text-xs text-gray-500 mt-1"><span>Allocated: <!-- -->92<!-- -->%</span><span>Available: <!-- -->7<!-- --> seats</span></div></div><div class="flex space-x-2"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View Sections</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">Allocate Students</button><span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">Oversubscribed</span></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Students Pending Allocation</h3><p class="text-sm text-muted-foreground">Admitted students awaiting class assignment</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Aarav Sharma</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9</span></div><p class="text-blue-600 font-medium">APP-2024-001</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📊 Admission Score:</span> <!-- -->88<!-- -->/100</p><p><span class="font-medium">🎯 Preferences:</span> <!-- -->Science Stream</p></div><div></div></div></div><div class="flex flex-col space-y-2 ml-4"><select class="px-3 py-1 border border-gray-300 rounded text-sm"><option value="">Select Section</option><option value="9A">Grade 9-A (28/30)</option><option value="9B">Grade 9-B (25/30)</option><option value="9C">Grade 9-C (27/30)</option><option value="9D">Grade 9-D (25/30)</option></select><button class="bg-green-600 text-white px-4 py-1 rounded text-sm hover:bg-green-700">Allocate</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Profile</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Kavya Gupta</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 6</span><span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">Special Needs</span><span class="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">Sibling</span></div><p class="text-blue-600 font-medium">APP-2024-015</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📊 Admission Score:</span> <!-- -->82<!-- -->/100</p><p><span class="font-medium">🎯 Preferences:</span> <!-- -->Section A</p></div><div><p><span class="font-medium">🔧 Special Needs:</span> <!-- -->Learning Support</p><p><span class="font-medium">👨‍👩‍👧‍👦 Sibling in:</span> <!-- -->Grade 4-B</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><select class="px-3 py-1 border border-gray-300 rounded text-sm"><option value="">Select Section</option><option value="9A">Grade 9-A (28/30)</option><option value="9B">Grade 9-B (25/30)</option><option value="9C">Grade 9-C (27/30)</option><option value="9D">Grade 9-D (25/30)</option></select><button class="bg-green-600 text-white px-4 py-1 rounded text-sm hover:bg-green-700">Allocate</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Profile</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Arjun Singh</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 11</span></div><p class="text-blue-600 font-medium">APP-2024-032</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📊 Admission Score:</span> <!-- -->80<!-- -->/100</p><p><span class="font-medium">🎯 Preferences:</span> <!-- -->Science Stream, Commerce Stream</p></div><div></div></div></div><div class="flex flex-col space-y-2 ml-4"><select class="px-3 py-1 border border-gray-300 rounded text-sm"><option value="">Select Section</option><option value="9A">Grade 9-A (28/30)</option><option value="9B">Grade 9-B (25/30)</option><option value="9C">Grade 9-C (27/30)</option><option value="9D">Grade 9-D (25/30)</option></select><button class="bg-green-600 text-white px-4 py-1 rounded text-sm hover:bg-green-700">Allocate</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Profile</button></div></div></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Auto-Allocation Rules</h3><p class="text-sm text-muted-foreground">Configure automatic allocation criteria</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded p-3"><h4 class="font-medium mb-2">Score-Based Allocation</h4><p class="text-sm text-gray-600 mb-2">Allocate students based on admission scores</p><div class="flex items-center space-x-2"><input type="checkbox" class="rounded"/><label class="text-sm">Enable score-based allocation</label></div></div><div class="border rounded p-3"><h4 class="font-medium mb-2">Sibling Preference</h4><p class="text-sm text-gray-600 mb-2">Keep siblings in same section when possible</p><div class="flex items-center space-x-2"><input type="checkbox" class="rounded" checked=""/><label class="text-sm">Prioritize sibling placement</label></div></div><div class="border rounded p-3"><h4 class="font-medium mb-2">Special Needs Support</h4><p class="text-sm text-gray-600 mb-2">Allocate to sections with support facilities</p><div class="flex items-center space-x-2"><input type="checkbox" class="rounded" checked=""/><label class="text-sm">Consider special needs</label></div></div><button class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">Run Auto-Allocation</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Allocation Summary</h3><p class="text-sm text-muted-foreground">Current allocation status</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><span class="text-sm font-medium">Successfully Allocated</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">398<!-- --> students</span></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Pending Allocation</span><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">58<!-- --> students</span></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Allocation Conflicts</span><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">12<!-- --> students</span></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Special Considerations</span><span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">23<!-- --> students</span></div></div><div class="mt-4 pt-3 border-t"><div class="flex items-center justify-between font-medium"><span>Total Students</span><span class="text-blue-600">456</span></div><div class="mt-2"><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-green-600 h-2 rounded-full" style="width:87.28070175438597%"></div></div><p class="text-xs text-gray-500 mt-1">87<!-- -->% allocation complete</p></div></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[6285,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"7544\",\"static/chunks/app/dashboard/admissions/classes/page-4da57e1450ca8da1.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"Tn7xWRdXVWNLs4FpbaknK\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/admissions/classes\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"admissions\",{\"children\":[\"classes\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"admissions\",{\"children\":[\"classes\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"admissions\",\"children\",\"classes\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"admissions\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>