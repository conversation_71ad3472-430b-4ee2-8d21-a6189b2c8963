<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/admissions/applications/page-6540e5e10f9b2118.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Student Applications</h1><p class="text-muted-foreground">Review and process student admission applications</p></div><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Export Applications</button></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Total Applications</h3><span class="text-2xl">📋</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">1,234</div><p class="text-xs text-muted-foreground">This academic year</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Under Review</h3><span class="text-2xl">👀</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">156</div><p class="text-xs text-muted-foreground">Pending review</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Approved</h3><span class="text-2xl">✅</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">789</div><p class="text-xs text-muted-foreground">64% approval rate</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Rejected</h3><span class="text-2xl">❌</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">289</div><p class="text-xs text-muted-foreground">23% rejection rate</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Applications</h3><p class="text-sm text-muted-foreground">Latest student admission applications</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Aarav Sharma</h3><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Under Review</span><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">High<!-- --> Priority</span></div><p class="text-blue-600 font-medium">APP-2024-001</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">🎓 Grade:</span> <!-- -->Grade 9</p><p><span class="font-medium">🏫 Previous School:</span> <!-- -->Delhi Public School</p></div><div><p><span class="font-medium">👨‍👩‍👧‍👦 Parent:</span> <!-- -->Rajesh Sharma</p><p><span class="font-medium">📞 Phone:</span> <!-- -->+91-9876543210</p></div></div><div class="mt-3"><p class="text-sm text-gray-600 mb-2"><span class="font-medium">📅 Submitted:</span> <!-- -->2024-02-15</p><div><span class="text-sm font-medium text-gray-700">Documents:</span><div class="flex flex-wrap gap-1 mt-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Birth Certificate</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Transfer Certificate</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Report Card</span></div></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Review</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Schedule Interview</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Priya Patel</h3><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Approved</span><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Medium<!-- --> Priority</span></div><p class="text-blue-600 font-medium">APP-2024-002</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">🎓 Grade:</span> <!-- -->Grade 6</p><p><span class="font-medium">🏫 Previous School:</span> <!-- -->St. Mary&#x27;s School</p></div><div><p><span class="font-medium">👨‍👩‍👧‍👦 Parent:</span> <!-- -->Amit Patel</p><p><span class="font-medium">📞 Phone:</span> <!-- -->+91-9876543211</p></div></div><div class="mt-3"><p class="text-sm text-gray-600 mb-2"><span class="font-medium">📅 Submitted:</span> <!-- -->2024-02-14</p><div><span class="text-sm font-medium text-gray-700">Documents:</span><div class="flex flex-wrap gap-1 mt-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Birth Certificate</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Transfer Certificate</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Report Card</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Medical Certificate</span></div></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Review</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Schedule Interview</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Arjun Singh</h3><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Interview Scheduled</span><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">High<!-- --> Priority</span></div><p class="text-blue-600 font-medium">APP-2024-003</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">🎓 Grade:</span> <!-- -->Grade 11</p><p><span class="font-medium">🏫 Previous School:</span> <!-- -->Ryan International</p></div><div><p><span class="font-medium">👨‍👩‍👧‍👦 Parent:</span> <!-- -->Sunita Singh</p><p><span class="font-medium">📞 Phone:</span> <!-- -->+91-9876543212</p></div></div><div class="mt-3"><p class="text-sm text-gray-600 mb-2"><span class="font-medium">📅 Submitted:</span> <!-- -->2024-02-13</p><div><span class="text-sm font-medium text-gray-700">Documents:</span><div class="flex flex-wrap gap-1 mt-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Birth Certificate</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Transfer Certificate</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Report Card</span></div></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Review</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Schedule Interview</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Kavya Gupta</h3><span class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">Documents Pending</span><span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Low<!-- --> Priority</span></div><p class="text-blue-600 font-medium">APP-2024-004</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">🎓 Grade:</span> <!-- -->Grade 3</p><p><span class="font-medium">🏫 Previous School:</span> <!-- -->Little Angels School</p></div><div><p><span class="font-medium">👨‍👩‍👧‍👦 Parent:</span> <!-- -->Vikram Gupta</p><p><span class="font-medium">📞 Phone:</span> <!-- -->+91-9876543213</p></div></div><div class="mt-3"><p class="text-sm text-gray-600 mb-2"><span class="font-medium">📅 Submitted:</span> <!-- -->2024-02-12</p><div><span class="text-sm font-medium text-gray-700">Documents:</span><div class="flex flex-wrap gap-1 mt-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Birth Certificate</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Report Card</span></div></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Review</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Schedule Interview</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Application Status</h3><p class="text-sm text-muted-foreground">Current application pipeline</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><span class="text-sm font-medium">Submitted</span><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">156<!-- --> applications</span></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Under Review</span><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">89<!-- --> applications</span></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Interview Scheduled</span><span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">45<!-- --> applications</span></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Documents Pending</span><span class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">23<!-- --> applications</span></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Approved</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">789<!-- --> applications</span></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Rejected</span><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">132<!-- --> applications</span></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Grade-wise Applications</h3><p class="text-sm text-muted-foreground">Applications by grade level</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><span class="text-sm font-medium">Nursery</span><div class="flex items-center space-x-2"><div class="w-20 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:25.79710144927536%"></div></div><span class="text-sm text-gray-600">89</span></div></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Grade 1</span><div class="flex items-center space-x-2"><div class="w-20 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:45.21739130434783%"></div></div><span class="text-sm text-gray-600">156</span></div></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Grade 6</span><div class="flex items-center space-x-2"><div class="w-20 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:67.82608695652173%"></div></div><span class="text-sm text-gray-600">234</span></div></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Grade 9</span><div class="flex items-center space-x-2"><div class="w-20 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:100%"></div></div><span class="text-sm text-gray-600">345</span></div></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Grade 11</span><div class="flex items-center space-x-2"><div class="w-20 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:80.57971014492755%"></div></div><span class="text-sm text-gray-600">278</span></div></div><div class="flex items-center justify-between"><span class="text-sm font-medium">Others</span><div class="flex items-center space-x-2"><div class="w-20 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:38.26086956521739%"></div></div><span class="text-sm text-gray-600">132</span></div></div></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[1824,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"6430\",\"static/chunks/app/dashboard/admissions/applications/page-6540e5e10f9b2118.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"WyP9Wc4exBySD9T7YRTKE\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/admissions/applications\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"admissions\",{\"children\":[\"applications\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"admissions\",{\"children\":[\"applications\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"admissions\",\"children\",\"applications\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"admissions\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>