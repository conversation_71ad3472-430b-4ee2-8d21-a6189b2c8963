<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/admissions/enrollment/page-c3229048db1395c0.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Student Enrollment</h1><p class="text-muted-foreground">Complete student enrollment process and generate student IDs</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Bulk Enrollment</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Generate IDs</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Ready for Enrollment</h3><span class="text-2xl">📋</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">398</div><p class="text-xs text-muted-foreground">Allocated students</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Enrolled</h3><span class="text-2xl">✅</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">342</div><p class="text-xs text-muted-foreground">86% enrollment rate</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Pending Documents</h3><span class="text-2xl">📄</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">34</div><p class="text-xs text-muted-foreground">Missing documents</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Fee Pending</h3><span class="text-2xl">💰</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">22</div><p class="text-xs text-muted-foreground">Payment pending</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Enrollment Pipeline</h3><p class="text-sm text-muted-foreground">Students at different stages of enrollment process</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Aarav Sharma</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9</span><span class="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">9-A</span><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Documents Verified</span></div><p class="text-blue-600 font-medium">APP-2024-001</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📄 Documents:</span><span class="ml-1 text-green-600">Complete</span></p><p><span class="font-medium">💰 Fee Payment:</span><span class="ml-1 text-green-600">Paid</span></p></div><div><p><span class="font-medium">🏥 Medical:</span><span class="ml-1 text-red-600">Pending</span></p><p><span class="font-medium">📞 Contact:</span> <!-- -->+91-**********</p></div></div><div class="mt-3"><p class="text-sm text-gray-600"><span class="font-medium">📅 Enrollment Date:</span> <!-- -->2024-02-20</p></div><div class="mt-3"><div class="flex items-center space-x-2 text-xs mb-1"><span class="text-green-600">📄 Docs</span><span class="text-green-600">💰 Fee</span><span class="text-gray-400">🏥 Medical</span><span class="text-gray-400">✅ Enrolled</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:25%"></div></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Complete Enrollment</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Send Reminder</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Kavya Gupta</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 6</span><span class="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">6-B</span><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Fee Payment</span></div><p class="text-blue-600 font-medium">APP-2024-015</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📄 Documents:</span><span class="ml-1 text-green-600">Complete</span></p><p><span class="font-medium">💰 Fee Payment:</span><span class="ml-1 text-red-600">Pending</span></p></div><div><p><span class="font-medium">🏥 Medical:</span><span class="ml-1 text-green-600">Complete</span></p><p><span class="font-medium">📞 Contact:</span> <!-- -->+91-**********</p></div></div><div class="mt-3"><p class="text-sm text-gray-600"><span class="font-medium">📅 Enrollment Date:</span> <!-- -->2024-02-18</p></div><div class="mt-3"><div class="flex items-center space-x-2 text-xs mb-1"><span class="text-green-600">📄 Docs</span><span class="text-gray-400">💰 Fee</span><span class="text-green-600">🏥 Medical</span><span class="text-gray-400">✅ Enrolled</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:50%"></div></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Complete Enrollment</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Send Reminder</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Arjun Singh</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 11</span><span class="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">11-Science A</span><span class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">Medical Checkup</span></div><p class="text-blue-600 font-medium">APP-2024-032</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📄 Documents:</span><span class="ml-1 text-green-600">Complete</span></p><p><span class="font-medium">💰 Fee Payment:</span><span class="ml-1 text-green-600">Paid</span></p></div><div><p><span class="font-medium">🏥 Medical:</span><span class="ml-1 text-yellow-600">Scheduled</span></p><p><span class="font-medium">📞 Contact:</span> <!-- -->+91-**********</p></div></div><div class="mt-3"><p class="text-sm text-gray-600"><span class="font-medium">📅 Enrollment Date:</span> <!-- -->2024-02-22</p></div><div class="mt-3"><div class="flex items-center space-x-2 text-xs mb-1"><span class="text-green-600">📄 Docs</span><span class="text-green-600">💰 Fee</span><span class="text-gray-400">🏥 Medical</span><span class="text-gray-400">✅ Enrolled</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:75%"></div></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Complete Enrollment</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Send Reminder</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Riya Patel</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 3</span><span class="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">3-A</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Enrolled</span></div><p class="text-blue-600 font-medium">APP-2024-048</p><p class="text-green-600 font-medium">Student ID: <!-- -->STU-2024-0342</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📄 Documents:</span><span class="ml-1 text-green-600">Complete</span></p><p><span class="font-medium">💰 Fee Payment:</span><span class="ml-1 text-green-600">Paid</span></p></div><div><p><span class="font-medium">🏥 Medical:</span><span class="ml-1 text-green-600">Complete</span></p><p><span class="font-medium">📞 Contact:</span> <!-- -->+91-**********</p></div></div><div class="mt-3"><p class="text-sm text-gray-600"><span class="font-medium">📅 Enrollment Date:</span> <!-- -->2024-02-15</p></div><div class="mt-3"><div class="flex items-center space-x-2 text-xs mb-1"><span class="text-green-600">📄 Docs</span><span class="text-green-600">💰 Fee</span><span class="text-green-600">🏥 Medical</span><span class="text-green-600">✅ Enrolled</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:100%"></div></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-green-600 text-white px-4 py-2 rounded-md text-sm">View Profile</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Print ID Card</button><button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Enrollment Checklist</h3><p class="text-sm text-muted-foreground">Required steps for student enrollment</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="border rounded-lg p-4 text-center"><div class="text-3xl mb-2">📄</div><h3 class="font-semibold mb-1">Document Verification</h3><p class="text-sm text-gray-600 mb-3">Birth certificate, transfer certificate, etc.</p><div class="mb-2"><div class="text-lg font-bold text-blue-600">364<!-- -->/<!-- -->398</div><div class="w-full bg-gray-200 rounded-full h-2 mt-1"><div class="bg-blue-600 h-2 rounded-full" style="width:91.*************%"></div></div></div><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">In Progress</span></div><div class="border rounded-lg p-4 text-center"><div class="text-3xl mb-2">💰</div><h3 class="font-semibold mb-1">Fee Payment</h3><p class="text-sm text-gray-600 mb-3">Admission and first term fees</p><div class="mb-2"><div class="text-lg font-bold text-blue-600">342<!-- -->/<!-- -->398</div><div class="w-full bg-gray-200 rounded-full h-2 mt-1"><div class="bg-blue-600 h-2 rounded-full" style="width:85.92964824120602%"></div></div></div><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">In Progress</span></div><div class="border rounded-lg p-4 text-center"><div class="text-3xl mb-2">🏥</div><h3 class="font-semibold mb-1">Medical Checkup</h3><p class="text-sm text-gray-600 mb-3">Health screening and vaccination records</p><div class="mb-2"><div class="text-lg font-bold text-blue-600">298<!-- -->/<!-- -->398</div><div class="w-full bg-gray-200 rounded-full h-2 mt-1"><div class="bg-blue-600 h-2 rounded-full" style="width:74.87437185929649%"></div></div></div><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">In Progress</span></div><div class="border rounded-lg p-4 text-center"><div class="text-3xl mb-2">🆔</div><h3 class="font-semibold mb-1">ID Generation</h3><p class="text-sm text-gray-600 mb-3">Student ID and access cards</p><div class="mb-2"><div class="text-lg font-bold text-blue-600">342<!-- -->/<!-- -->398</div><div class="w-full bg-gray-200 rounded-full h-2 mt-1"><div class="bg-blue-600 h-2 rounded-full" style="width:85.92964824120602%"></div></div></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Ready</span></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Bulk Enrollment Actions</h3><p class="text-sm text-muted-foreground">Process multiple students at once</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4"><h4 class="font-medium mb-2">Generate Student IDs</h4><p class="text-sm text-gray-600 mb-3">Create unique student IDs for enrolled students</p><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Generate IDs (56 students)</button></div><div class="border rounded-lg p-4"><h4 class="font-medium mb-2">Send Welcome Emails</h4><p class="text-sm text-gray-600 mb-3">Send enrollment confirmation to parents</p><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm">Send Emails (342 families)</button></div><div class="border rounded-lg p-4"><h4 class="font-medium mb-2">Print ID Cards</h4><p class="text-sm text-gray-600 mb-3">Generate physical ID cards for students</p><button class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 text-sm">Print Cards (342 cards)</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Enrollment Statistics</h3><p class="text-sm text-muted-foreground">Current enrollment progress</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="text-center"><div class="text-3xl font-bold text-blue-600 mb-1">86%</div><p class="text-sm text-gray-600">Enrollment Complete</p><div class="w-full bg-gray-200 rounded-full h-3 mt-2"><div class="bg-blue-600 h-3 rounded-full" style="width:86%"></div></div></div><div class="space-y-2"><div class="flex justify-between text-sm"><span>Documents Complete</span><span class="font-medium">91%</span></div><div class="flex justify-between text-sm"><span>Fees Collected</span><span class="font-medium">86%</span></div><div class="flex justify-between text-sm"><span>Medical Clearance</span><span class="font-medium">75%</span></div><div class="flex justify-between text-sm"><span>IDs Generated</span><span class="font-medium">86%</span></div></div><div class="pt-3 border-t"><div class="flex justify-between font-medium"><span>Total Enrolled</span><span class="text-green-600">342 / 398</span></div></div></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[1225,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"9791\",\"static/chunks/app/dashboard/admissions/enrollment/page-c3229048db1395c0.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"az92YWeqjFDK2ONnGo-1U\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/admissions/enrollment\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"admissions\",{\"children\":[\"enrollment\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"admissions\",{\"children\":[\"enrollment\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"admissions\",\"children\",\"enrollment\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"admissions\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>