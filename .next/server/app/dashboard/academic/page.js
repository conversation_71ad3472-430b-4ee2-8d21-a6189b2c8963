(()=>{var e={};e.id=2365,e.ids=[2365],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},61392:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>o,originalPathname:()=>m,pages:()=>x,routeModule:()=>p,tree:()=>i});var r=a(50482),t=a(69108),l=a(62563),n=a.n(l),d=a(68300),c={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);a.d(s,c);let i=["",{children:["dashboard",{children:["academic",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,71297)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94699)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,21342)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],x=["/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/page.tsx"],m="/dashboard/academic/page",o={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/dashboard/academic/page",pathname:"/dashboard/academic",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},71450:(e,s,a)=>{Promise.resolve().then(a.bind(a,91134))},91134:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var r=a(95344);a(3729);var t=a(20783),l=a.n(t),n=a(18791);function d(){return(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Academic Management"}),r.jsx("p",{className:"text-muted-foreground",children:"Manage curriculum, schedules, assessments, and academic progress"})]})}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(n.ll,{className:"text-sm font-medium",children:"Active Courses"}),r.jsx("span",{className:"text-2xl",children:"\uD83D\uDCDA"})]}),(0,r.jsxs)(n.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:"45"}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"This semester"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(n.ll,{className:"text-sm font-medium",children:"Upcoming Exams"}),r.jsx("span",{className:"text-2xl",children:"\uD83D\uDCDD"})]}),(0,r.jsxs)(n.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:"12"}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Next 2 weeks"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(n.ll,{className:"text-sm font-medium",children:"Average Attendance"}),r.jsx("span",{className:"text-2xl",children:"✅"})]}),(0,r.jsxs)(n.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:"92%"}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(n.ll,{className:"text-sm font-medium",children:"Grade Reports"}),r.jsx("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,r.jsxs)(n.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:"156"}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Pending review"})]})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[r.jsx(l(),{href:"/dashboard/academic/curriculum",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[r.jsx(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:"text-2xl",children:"\uD83D\uDCDA"}),r.jsx(n.ll,{className:"text-lg",children:"Curriculum"})]})}),(0,r.jsxs)(n.aY,{children:[r.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage course curriculum and syllabus"}),r.jsx("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Curriculum →"})]})]})}),r.jsx(l(),{href:"/dashboard/academic/schedule",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[r.jsx(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:"text-2xl",children:"\uD83D\uDCC5"}),r.jsx(n.ll,{className:"text-lg",children:"Schedule"})]})}),(0,r.jsxs)(n.aY,{children:[r.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Create and manage class schedules"}),r.jsx("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Schedule →"})]})]})}),r.jsx(l(),{href:"/dashboard/academic/assessments",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[r.jsx(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:"text-2xl",children:"\uD83D\uDCDD"}),r.jsx(n.ll,{className:"text-lg",children:"Assessments"})]})}),(0,r.jsxs)(n.aY,{children:[r.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Create and manage exams and assessments"}),r.jsx("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Assessments →"})]})]})}),r.jsx(l(),{href:"/dashboard/academic/grades",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[r.jsx(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),r.jsx(n.ll,{className:"text-lg",children:"Grades"})]})}),(0,r.jsxs)(n.aY,{children:[r.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Record and manage student grades"}),r.jsx("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Grades →"})]})]})}),r.jsx(l(),{href:"/dashboard/academic/attendance",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[r.jsx(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:"text-2xl",children:"✅"}),r.jsx(n.ll,{className:"text-lg",children:"Attendance"})]})}),(0,r.jsxs)(n.aY,{children:[r.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Track and manage student attendance"}),r.jsx("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Attendance →"})]})]})}),r.jsx(l(),{href:"/dashboard/academic/progress",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[r.jsx(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:"text-2xl",children:"\uD83D\uDCC8"}),r.jsx(n.ll,{className:"text-lg",children:"Progress Reports"})]})}),(0,r.jsxs)(n.aY,{children:[r.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Generate student progress reports"}),r.jsx("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Progress →"})]})]})})]})]})}},18791:(e,s,a)=>{"use strict";a.d(s,{Zb:()=>c,aY:()=>o,SZ:()=>m,Ol:()=>i,ll:()=>x});var r=a(95344),t=a(3729),l=a(56815),n=a(79377);function d(...e){return(0,n.m6)((0,l.W)(e))}let c=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));c.displayName="Card";let i=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:d("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let x=t.forwardRef(({className:e,...s},a)=>r.jsx("h3",{ref:a,className:d("text-2xl font-semibold leading-none tracking-tight",e),...s}));x.displayName="CardTitle";let m=t.forwardRef(({className:e,...s},a)=>r.jsx("p",{ref:a,className:d("text-sm text-muted-foreground",e),...s}));m.displayName="CardDescription";let o=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:d("p-6 pt-0",e),...s}));o.displayName="CardContent",t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:d("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},71297:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>l,__esModule:()=>t,default:()=>n});let r=(0,a(86843).createProxy)(String.raw`/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/page.tsx`),{__esModule:t,$$typeof:l}=r,n=r.default}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[1638,3883,3945,1476,8510],()=>a(61392));module.exports=r})();