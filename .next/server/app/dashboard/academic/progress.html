<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/academic/progress/page-365ceb7027d50fe6.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Student Progress Reports</h1><p class="text-muted-foreground">Generate comprehensive progress reports and track student development</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Bulk Generate</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Generate Report</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Reports Generated</h3><span class="text-2xl">📊</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">234</div><p class="text-xs text-muted-foreground">This month</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Improving Students</h3><span class="text-2xl">📈</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">156</div><p class="text-xs text-muted-foreground">Positive trend</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Need Support</h3><span class="text-2xl">⚠️</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">23</div><p class="text-xs text-muted-foreground">Declining performance</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Parent Meetings</h3><span class="text-2xl">👨‍👩‍👧‍👦</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">45</div><p class="text-xs text-muted-foreground">Scheduled</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Generate Progress Report</h3><p class="text-sm text-muted-foreground">Create detailed progress reports for students</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-3"><div><label class="text-sm font-medium">Student</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Student</option><option value="aarav">Aarav Sharma (9A001)</option><option value="kavya">Kavya Gupta (9A002)</option><option value="arjun">Arjun Singh (9A003)</option><option value="riya">Riya Patel (9A004)</option></select></div><div><label class="text-sm font-medium">Report Period</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Period</option><option value="monthly">Monthly Report</option><option value="quarterly">Quarterly Report</option><option value="mid-term">Mid-term Report</option><option value="annual">Annual Report</option></select></div><div><label class="text-sm font-medium">Report Type</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Type</option><option value="comprehensive">Comprehensive Report</option><option value="academic-only">Academic Performance Only</option><option value="behavioral">Behavioral Assessment</option><option value="parent-summary">Parent Summary</option></select></div></div><div class="mt-4 flex space-x-2"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Generate Report</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Preview</button><button class="text-blue-600 hover:text-blue-800">Use Template</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Student Progress Overview - Aarav Sharma (9A001)</h3><p class="text-sm text-muted-foreground">Comprehensive progress tracking for current semester</p></div><div class="p-6 pt-0"><div class="space-y-6"><div><h3 class="text-lg font-semibold mb-3">Academic Performance</h3><div class="grid gap-4 md:grid-cols-2"><div class="space-y-3"><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Mathematics</h4><div class="flex items-center space-x-2"><span class="text-lg font-bold text-blue-600">85<!-- -->%</span><span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">+7</span><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade <!-- -->A</span></div></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:85%"></div></div><p class="text-xs text-gray-500 mt-1">Previous: <!-- -->78<!-- -->%</p></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Science</h4><div class="flex items-center space-x-2"><span class="text-lg font-bold text-blue-600">88<!-- -->%</span><span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">+3</span><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade <!-- -->A</span></div></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:88%"></div></div><p class="text-xs text-gray-500 mt-1">Previous: <!-- -->85<!-- -->%</p></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">English</h4><div class="flex items-center space-x-2"><span class="text-lg font-bold text-blue-600">82<!-- -->%</span><span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">+2</span><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade <!-- -->A-</span></div></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:82%"></div></div><p class="text-xs text-gray-500 mt-1">Previous: <!-- -->80<!-- -->%</p></div></div><div class="space-y-3"><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Social Studies</h4><div class="flex items-center space-x-2"><span class="text-lg font-bold text-blue-600">87<!-- -->%</span><span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">+5</span><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade <!-- -->A</span></div></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:87%"></div></div><p class="text-xs text-gray-500 mt-1">Previous: <!-- -->82<!-- -->%</p></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Hindi</h4><div class="flex items-center space-x-2"><span class="text-lg font-bold text-blue-600">83<!-- -->%</span><span class="text-xs px-2 py-1 rounded-full bg-red-100 text-red-800">-2</span><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade <!-- -->A-</span></div></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:83%"></div></div><p class="text-xs text-gray-500 mt-1">Previous: <!-- -->85<!-- -->%</p></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Physical Education</h4><div class="flex items-center space-x-2"><span class="text-lg font-bold text-blue-600">92<!-- -->%</span><span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">+2</span><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade <!-- -->A+</span></div></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:92%"></div></div><p class="text-xs text-gray-500 mt-1">Previous: <!-- -->90<!-- -->%</p></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div><h3 class="text-lg font-semibold mb-3">Attendance Record</h3><div class="border rounded p-4"><div class="text-center mb-3"><div class="text-3xl font-bold text-green-600">94%</div><p class="text-sm text-gray-600">Overall Attendance</p></div><div class="space-y-2 text-sm"><div class="flex justify-between"><span>Days Present:</span><span class="font-medium">47/50</span></div><div class="flex justify-between"><span>Days Absent:</span><span class="font-medium">3</span></div><div class="flex justify-between"><span>Late Arrivals:</span><span class="font-medium">2</span></div></div></div></div><div><h3 class="text-lg font-semibold mb-3">Behavioral Assessment</h3><div class="border rounded p-4"><div class="space-y-3"><div><div class="flex justify-between text-sm mb-1"><span>Classroom Participation</span><span class="font-medium">4<!-- -->/<!-- -->5</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-green-600 h-2 rounded-full" style="width:80%"></div></div></div><div><div class="flex justify-between text-sm mb-1"><span>Homework Completion</span><span class="font-medium">5<!-- -->/<!-- -->5</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-green-600 h-2 rounded-full" style="width:100%"></div></div></div><div><div class="flex justify-between text-sm mb-1"><span>Peer Interaction</span><span class="font-medium">4<!-- -->/<!-- -->5</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-green-600 h-2 rounded-full" style="width:80%"></div></div></div><div><div class="flex justify-between text-sm mb-1"><span>Following Instructions</span><span class="font-medium">5<!-- -->/<!-- -->5</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-green-600 h-2 rounded-full" style="width:100%"></div></div></div></div></div></div></div><div><h3 class="text-lg font-semibold mb-3">Teacher Comments</h3><div class="space-y-3"><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Mathematics</h4><span class="text-sm text-blue-600">Dr. Priya Patel</span></div><p class="text-sm text-gray-700">Aarav shows excellent problem-solving skills and has improved significantly in algebra. Encourage continued practice with geometry.</p></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Science</h4><span class="text-sm text-blue-600">Mr. Rajesh Kumar</span></div><p class="text-sm text-gray-700">Very engaged in laboratory work and asks thoughtful questions. Strong understanding of scientific concepts.</p></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">English</h4><span class="text-sm text-blue-600">Ms. Sneha Gupta</span></div><p class="text-sm text-gray-700">Good progress in creative writing. Needs to focus more on grammar and vocabulary building.</p></div></div></div><div><h3 class="text-lg font-semibold mb-3">Recommendations</h3><div class="border rounded p-4 bg-blue-50"><ul class="space-y-2 text-sm"><li class="flex items-start space-x-2"><span class="text-blue-600">•</span><span>Continue with current study routine as it&#x27;s showing positive results</span></li><li class="flex items-start space-x-2"><span class="text-blue-600">•</span><span>Focus on improving Hindi language skills through additional reading</span></li><li class="flex items-start space-x-2"><span class="text-blue-600">•</span><span>Consider joining the school science club to further develop interest in STEM</span></li><li class="flex items-start space-x-2"><span class="text-blue-600">•</span><span>Maintain excellent attendance record and punctuality</span></li></ul></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Class Progress Summary - Grade 9A</h3><p class="text-sm text-muted-foreground">Overall class performance and trends</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-3"><div class="text-center"><div class="text-2xl font-bold text-blue-600">85%</div><p class="text-sm text-gray-600">Class Average</p></div><div class="text-center"><div class="text-2xl font-bold text-green-600">78%</div><p class="text-sm text-gray-600">Students Improving</p></div><div class="text-center"><div class="text-2xl font-bold text-yellow-600">15%</div><p class="text-sm text-gray-600">Need Support</p></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[4075,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"5117\",\"static/chunks/app/dashboard/academic/progress/page-365ceb7027d50fe6.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"DEc8SW0ph9C_XxX68bz_T\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/academic/progress\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"academic\",{\"children\":[\"progress\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"academic\",{\"children\":[\"progress\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"academic\",\"children\",\"progress\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"academic\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>