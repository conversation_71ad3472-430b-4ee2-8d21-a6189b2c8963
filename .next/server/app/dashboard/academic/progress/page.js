(()=>{var e={};e.id=5117,e.ids=[5117],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},63461:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>c});var t=r(50482),a=r(69108),l=r(62563),n=r.n(l),d=r(68300),i={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);r.d(s,i);let c=["",{children:["dashboard",{children:["academic",{children:["progress",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43699)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/progress/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94699)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,21342)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],o=["/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/progress/page.tsx"],x="/dashboard/academic/progress/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/academic/progress/page",pathname:"/dashboard/academic/progress",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},46280:(e,s,r)=>{Promise.resolve().then(r.bind(r,13792))},13792:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(95344);r(3729);var a=r(18791);function l(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Student Progress Reports"}),t.jsx("p",{className:"text-muted-foreground",children:"Generate comprehensive progress reports and track student development"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[t.jsx("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Bulk Generate"}),t.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Generate Report"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(a.ll,{className:"text-sm font-medium",children:"Reports Generated"}),t.jsx("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,t.jsxs)(a.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:"234"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(a.ll,{className:"text-sm font-medium",children:"Improving Students"}),t.jsx("span",{className:"text-2xl",children:"\uD83D\uDCC8"})]}),(0,t.jsxs)(a.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:"156"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Positive trend"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(a.ll,{className:"text-sm font-medium",children:"Need Support"}),t.jsx("span",{className:"text-2xl",children:"⚠️"})]}),(0,t.jsxs)(a.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:"23"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Declining performance"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(a.ll,{className:"text-sm font-medium",children:"Parent Meetings"}),t.jsx("span",{className:"text-2xl",children:"\uD83D\uDC68‍\uD83D\uDC69‍\uD83D\uDC67‍\uD83D\uDC66"})]}),(0,t.jsxs)(a.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:"45"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Scheduled"})]})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[t.jsx(a.ll,{children:"Generate Progress Report"}),t.jsx(a.SZ,{children:"Create detailed progress reports for students"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium",children:"Student"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[t.jsx("option",{value:"",children:"Select Student"}),t.jsx("option",{value:"aarav",children:"Aarav Sharma (9A001)"}),t.jsx("option",{value:"kavya",children:"Kavya Gupta (9A002)"}),t.jsx("option",{value:"arjun",children:"Arjun Singh (9A003)"}),t.jsx("option",{value:"riya",children:"Riya Patel (9A004)"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium",children:"Report Period"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[t.jsx("option",{value:"",children:"Select Period"}),t.jsx("option",{value:"monthly",children:"Monthly Report"}),t.jsx("option",{value:"quarterly",children:"Quarterly Report"}),t.jsx("option",{value:"mid-term",children:"Mid-term Report"}),t.jsx("option",{value:"annual",children:"Annual Report"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium",children:"Report Type"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[t.jsx("option",{value:"",children:"Select Type"}),t.jsx("option",{value:"comprehensive",children:"Comprehensive Report"}),t.jsx("option",{value:"academic-only",children:"Academic Performance Only"}),t.jsx("option",{value:"behavioral",children:"Behavioral Assessment"}),t.jsx("option",{value:"parent-summary",children:"Parent Summary"})]})]})]}),(0,t.jsxs)("div",{className:"mt-4 flex space-x-2",children:[t.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Generate Report"}),t.jsx("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Preview"}),t.jsx("button",{className:"text-blue-600 hover:text-blue-800",children:"Use Template"})]})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[t.jsx(a.ll,{children:"Student Progress Overview - Aarav Sharma (9A001)"}),t.jsx(a.SZ,{children:"Comprehensive progress tracking for current semester"})]}),t.jsx(a.aY,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Academic Performance"}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[t.jsx("div",{className:"space-y-3",children:[{subject:"Mathematics",current:85,previous:78,trend:"+7",grade:"A"},{subject:"Science",current:88,previous:85,trend:"+3",grade:"A"},{subject:"English",current:82,previous:80,trend:"+2",grade:"A-"}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[t.jsx("h4",{className:"font-medium",children:e.subject}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[e.current,"%"]}),t.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${e.trend.startsWith("+")?"bg-green-100 text-green-800":e.trend.startsWith("-")?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:e.trend}),(0,t.jsxs)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:["Grade ",e.grade]})]})]}),t.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:t.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.current}%`}})}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Previous: ",e.previous,"%"]})]},s))}),t.jsx("div",{className:"space-y-3",children:[{subject:"Social Studies",current:87,previous:82,trend:"+5",grade:"A"},{subject:"Hindi",current:83,previous:85,trend:"-2",grade:"A-"},{subject:"Physical Education",current:92,previous:90,trend:"+2",grade:"A+"}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[t.jsx("h4",{className:"font-medium",children:e.subject}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[e.current,"%"]}),t.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${e.trend.startsWith("+")?"bg-green-100 text-green-800":e.trend.startsWith("-")?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:e.trend}),(0,t.jsxs)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:["Grade ",e.grade]})]})]}),t.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:t.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.current}%`}})}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Previous: ",e.previous,"%"]})]},s))})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Attendance Record"}),(0,t.jsxs)("div",{className:"border rounded p-4",children:[(0,t.jsxs)("div",{className:"text-center mb-3",children:[t.jsx("div",{className:"text-3xl font-bold text-green-600",children:"94%"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Overall Attendance"})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Days Present:"}),t.jsx("span",{className:"font-medium",children:"47/50"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Days Absent:"}),t.jsx("span",{className:"font-medium",children:"3"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Late Arrivals:"}),t.jsx("span",{className:"font-medium",children:"2"})]})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Behavioral Assessment"}),t.jsx("div",{className:"border rounded p-4",children:t.jsx("div",{className:"space-y-3",children:[{aspect:"Classroom Participation",rating:4,max:5},{aspect:"Homework Completion",rating:5,max:5},{aspect:"Peer Interaction",rating:4,max:5},{aspect:"Following Instructions",rating:5,max:5}].map((e,s)=>(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[t.jsx("span",{children:e.aspect}),(0,t.jsxs)("span",{className:"font-medium",children:[e.rating,"/",e.max]})]}),t.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:t.jsx("div",{className:"bg-green-600 h-2 rounded-full",style:{width:`${e.rating/e.max*100}%`}})})]},s))})})]})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Teacher Comments"}),t.jsx("div",{className:"space-y-3",children:[{teacher:"Dr. Priya Patel",subject:"Mathematics",comment:"Aarav shows excellent problem-solving skills and has improved significantly in algebra. Encourage continued practice with geometry."},{teacher:"Mr. Rajesh Kumar",subject:"Science",comment:"Very engaged in laboratory work and asks thoughtful questions. Strong understanding of scientific concepts."},{teacher:"Ms. Sneha Gupta",subject:"English",comment:"Good progress in creative writing. Needs to focus more on grammar and vocabulary building."}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[t.jsx("h4",{className:"font-medium",children:e.subject}),t.jsx("span",{className:"text-sm text-blue-600",children:e.teacher})]}),t.jsx("p",{className:"text-sm text-gray-700",children:e.comment})]},s))})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Recommendations"}),t.jsx("div",{className:"border rounded p-4 bg-blue-50",children:(0,t.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("li",{className:"flex items-start space-x-2",children:[t.jsx("span",{className:"text-blue-600",children:"•"}),t.jsx("span",{children:"Continue with current study routine as it's showing positive results"})]}),(0,t.jsxs)("li",{className:"flex items-start space-x-2",children:[t.jsx("span",{className:"text-blue-600",children:"•"}),t.jsx("span",{children:"Focus on improving Hindi language skills through additional reading"})]}),(0,t.jsxs)("li",{className:"flex items-start space-x-2",children:[t.jsx("span",{className:"text-blue-600",children:"•"}),t.jsx("span",{children:"Consider joining the school science club to further develop interest in STEM"})]}),(0,t.jsxs)("li",{className:"flex items-start space-x-2",children:[t.jsx("span",{className:"text-blue-600",children:"•"}),t.jsx("span",{children:"Maintain excellent attendance record and punctuality"})]})]})})]})]})})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[t.jsx(a.ll,{children:"Class Progress Summary - Grade 9A"}),t.jsx(a.SZ,{children:"Overall class performance and trends"})]}),t.jsx(a.aY,{children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold text-blue-600",children:"85%"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Class Average"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold text-green-600",children:"78%"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Students Improving"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:"15%"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Need Support"})]})]})})]})]})}},18791:(e,s,r)=>{"use strict";r.d(s,{Zb:()=>i,aY:()=>m,SZ:()=>x,Ol:()=>c,ll:()=>o});var t=r(95344),a=r(3729),l=r(56815),n=r(79377);function d(...e){return(0,n.m6)((0,l.W)(e))}let i=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let c=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:d("flex flex-col space-y-1.5 p-6",e),...s}));c.displayName="CardHeader";let o=a.forwardRef(({className:e,...s},r)=>t.jsx("h3",{ref:r,className:d("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let x=a.forwardRef(({className:e,...s},r)=>t.jsx("p",{ref:r,className:d("text-sm text-muted-foreground",e),...s}));x.displayName="CardDescription";let m=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:d("p-6 pt-0",e),...s}));m.displayName="CardContent",a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:d("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},43699:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>n});let t=(0,r(86843).createProxy)(String.raw`/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/progress/page.tsx`),{__esModule:a,$$typeof:l}=t,n=t.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[1638,3883,3945,1476,8510],()=>r(63461));module.exports=t})();