(()=>{var e={};e.id=4973,e.ids=[4973],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},86068:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>d.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>c});var a=t(50482),r=t(69108),l=t(62563),d=t.n(l),n=t(68300),i={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);t.d(s,i);let c=["",{children:["dashboard",{children:["academic",{children:["assessments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,95766)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/assessments/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/assessments/page.tsx"],x="/dashboard/academic/assessments/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/academic/assessments/page",pathname:"/dashboard/academic/assessments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},37329:(e,s,t)=>{Promise.resolve().then(t.bind(t,16216))},16216:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(95344);t(3729);var r=t(18791);function l(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Assessments & Examinations"}),a.jsx("p",{className:"text-muted-foreground",children:"Create, schedule, and manage student assessments and examinations"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Question Bank"}),a.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Create Assessment"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(r.ll,{className:"text-sm font-medium",children:"Upcoming Exams"}),a.jsx("span",{className:"text-2xl",children:"\uD83D\uDCDD"})]}),(0,a.jsxs)(r.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:"12"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Next 2 weeks"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(r.ll,{className:"text-sm font-medium",children:"Pending Evaluations"}),a.jsx("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,a.jsxs)(r.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:"156"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Answer sheets"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(r.ll,{className:"text-sm font-medium",children:"Completed Assessments"}),a.jsx("span",{className:"text-2xl",children:"✅"})]}),(0,a.jsxs)(r.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:"89"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(r.ll,{className:"text-sm font-medium",children:"Average Score"}),a.jsx("span",{className:"text-2xl",children:"\uD83C\uDFAF"})]}),(0,a.jsxs)(r.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:"78%"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Class average"})]})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[a.jsx(r.ll,{children:"Upcoming Assessments"}),a.jsx(r.SZ,{children:"Scheduled exams and tests for the next two weeks"})]}),a.jsx(r.aY,{children:a.jsx("div",{className:"space-y-4",children:[{subject:"Mathematics",grade:"Grade 9",type:"Unit Test",date:"2024-02-20",time:"10:00 AM - 12:00 PM",duration:"2 hours",teacher:"Dr. Priya Patel",topics:["Polynomials","Linear Equations"],totalMarks:100,students:120},{subject:"Science",grade:"Grade 10",type:"Practical Exam",date:"2024-02-22",time:"2:00 PM - 4:00 PM",duration:"2 hours",teacher:"Mr. Rajesh Kumar",topics:["Light","Electricity"],totalMarks:50,students:115},{subject:"English",grade:"Grade 11",type:"Mid-term Exam",date:"2024-02-25",time:"9:00 AM - 12:00 PM",duration:"3 hours",teacher:"Ms. Sneha Gupta",topics:["Literature","Grammar","Composition"],totalMarks:150,students:95}].map((e,s)=>a.jsx("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx("h3",{className:"text-lg font-semibold",children:e.subject}),a.jsx("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e.grade}),a.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${"Mid-term Exam"===e.type?"bg-red-100 text-red-800":"Unit Test"===e.type?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}`,children:e.type})]}),a.jsx("p",{className:"text-blue-600 font-medium",children:e.teacher}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[a.jsx("span",{className:"font-medium",children:"\uD83D\uDCC5 Date:"})," ",e.date]}),(0,a.jsxs)("p",{children:[a.jsx("span",{className:"font-medium",children:"⏰ Time:"})," ",e.time]}),(0,a.jsxs)("p",{children:[a.jsx("span",{className:"font-medium",children:"⏱️ Duration:"})," ",e.duration]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[a.jsx("span",{className:"font-medium",children:"\uD83D\uDCCA Total Marks:"})," ",e.totalMarks]}),(0,a.jsxs)("p",{children:[a.jsx("span",{className:"font-medium",children:"\uD83D\uDC65 Students:"})," ",e.students]})]})]}),(0,a.jsxs)("div",{className:"mt-3",children:[a.jsx("p",{className:"text-sm font-medium text-gray-700 mb-1",children:"Topics Covered:"}),a.jsx("div",{className:"flex flex-wrap gap-1",children:e.topics.map((e,s)=>a.jsx("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded",children:e},s))})]})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[a.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"View Details"}),a.jsx("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"Edit Assessment"}),a.jsx("button",{className:"text-green-600 hover:text-green-800 text-sm",children:"Generate Paper"})]})]})},s))})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[a.jsx(r.ll,{children:"Create New Assessment"}),a.jsx(r.SZ,{children:"Set up a new exam or test"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium",children:"Subject"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[a.jsx("option",{value:"",children:"Select Subject"}),a.jsx("option",{value:"mathematics",children:"Mathematics"}),a.jsx("option",{value:"science",children:"Science"}),a.jsx("option",{value:"english",children:"English"}),a.jsx("option",{value:"social-studies",children:"Social Studies"}),a.jsx("option",{value:"hindi",children:"Hindi"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium",children:"Grade"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[a.jsx("option",{value:"",children:"Select Grade"}),a.jsx("option",{value:"grade-9",children:"Grade 9"}),a.jsx("option",{value:"grade-10",children:"Grade 10"}),a.jsx("option",{value:"grade-11",children:"Grade 11"}),a.jsx("option",{value:"grade-12",children:"Grade 12"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium",children:"Assessment Type"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[a.jsx("option",{value:"",children:"Select Type"}),a.jsx("option",{value:"unit-test",children:"Unit Test"}),a.jsx("option",{value:"mid-term",children:"Mid-term Exam"}),a.jsx("option",{value:"final-exam",children:"Final Exam"}),a.jsx("option",{value:"practical",children:"Practical Exam"}),a.jsx("option",{value:"assignment",children:"Assignment"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium",children:"Total Marks"}),a.jsx("input",{type:"number",placeholder:"100",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium",children:"Date"}),a.jsx("input",{type:"date",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium",children:"Duration (minutes)"}),a.jsx("input",{type:"number",placeholder:"120",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("label",{className:"text-sm font-medium",children:"Topics to Cover"}),a.jsx("textarea",{rows:3,placeholder:"Enter topics separated by commas...",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("label",{className:"text-sm font-medium",children:"Instructions"}),a.jsx("textarea",{rows:4,placeholder:"Enter exam instructions...",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{className:"mt-4 flex space-x-2",children:[a.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Create Assessment"}),a.jsx("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Save as Draft"}),a.jsx("button",{className:"text-blue-600 hover:text-blue-800",children:"Use Template"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[a.jsx(r.ll,{children:"Recent Assessment Results"}),a.jsx(r.SZ,{children:"Performance overview of completed assessments"})]}),a.jsx(r.aY,{children:a.jsx("div",{className:"space-y-3",children:[{subject:"Mathematics",grade:"Grade 9",average:78,highest:95,lowest:45,students:120},{subject:"Science",grade:"Grade 10",average:82,highest:98,lowest:52,students:115},{subject:"English",grade:"Grade 11",average:75,highest:92,lowest:48,students:95},{subject:"Social Studies",grade:"Grade 9",average:80,highest:94,lowest:58,students:118}].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium text-sm",children:e.subject}),a.jsx("p",{className:"text-xs text-blue-600",children:e.grade})]}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[e.average,"% avg"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-xs text-gray-600",children:[(0,a.jsxs)("div",{children:["Highest: ",e.highest,"%"]}),(0,a.jsxs)("div",{children:["Lowest: ",e.lowest,"%"]}),(0,a.jsxs)("div",{children:["Students: ",e.students]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:a.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.average}%`}})})]},s))})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[a.jsx(r.ll,{children:"Assessment Tools"}),a.jsx(r.SZ,{children:"Tools for creating and managing assessments"})]}),a.jsx(r.aY,{children:a.jsx("div",{className:"space-y-3",children:[{tool:"Question Bank",description:"Access pre-made questions",icon:"❓",count:"2,456 questions"},{tool:"Auto Paper Generator",description:"Generate papers automatically",icon:"\uD83E\uDD16",count:"15 templates"},{tool:"Answer Key Creator",description:"Create answer keys",icon:"\uD83D\uDD11",count:"89 keys"},{tool:"Grade Calculator",description:"Calculate grades and statistics",icon:"\uD83E\uDDEE",count:"Active"},{tool:"Result Analyzer",description:"Analyze assessment results",icon:"\uD83D\uDCC8",count:"12 reports"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between border rounded p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("span",{className:"text-2xl",children:e.icon}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-sm",children:e.tool}),a.jsx("p",{className:"text-xs text-gray-600",children:e.description})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("p",{className:"text-xs text-gray-500",children:e.count}),a.jsx("button",{className:"bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700 mt-1",children:"Use"})]})]},s))})})]})]})]})}},18791:(e,s,t)=>{"use strict";t.d(s,{Zb:()=>i,aY:()=>m,SZ:()=>x,Ol:()=>c,ll:()=>o});var a=t(95344),r=t(3729),l=t(56815),d=t(79377);function n(...e){return(0,d.m6)((0,l.W)(e))}let i=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:n("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let c=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:n("flex flex-col space-y-1.5 p-6",e),...s}));c.displayName="CardHeader";let o=r.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:n("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let x=r.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:n("text-sm text-muted-foreground",e),...s}));x.displayName="CardDescription";let m=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:n("p-6 pt-0",e),...s}));m.displayName="CardContent",r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:n("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},95766:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>d});let a=(0,t(86843).createProxy)(String.raw`/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/assessments/page.tsx`),{__esModule:r,$$typeof:l}=a,d=a.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,3883,3945,1476,8510],()=>t(86068));module.exports=a})();