<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/academic/attendance/page-057137e9f334baef.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Attendance Management</h1><p class="text-muted-foreground">Track and manage student attendance across all classes</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Attendance Report</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Mark Attendance</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Today&#x27;s Attendance</h3><span class="text-2xl">📅</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">92%</div><p class="text-xs text-muted-foreground">456/495 present</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Monthly Average</h3><span class="text-2xl">📊</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">89%</div><p class="text-xs text-muted-foreground">This month</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Chronic Absentees</h3><span class="text-2xl">⚠️</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">12</div><p class="text-xs text-muted-foreground">Below 75%</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Perfect Attendance</h3><span class="text-2xl">🏆</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">23</div><p class="text-xs text-muted-foreground">100% this month</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Mark Attendance - Grade 9A</h3><p class="text-sm text-muted-foreground">Today&#x27;s attendance for Mathematics - Period 1</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="grid gap-4 md:grid-cols-4"><div><label class="text-sm font-medium">Date</label><input type="date" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md" value="2024-02-16"/></div><div><label class="text-sm font-medium">Subject</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="mathematics">Mathematics</option><option value="science">Science</option><option value="english">English</option><option value="social">Social Studies</option></select></div><div><label class="text-sm font-medium">Period</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="1">Period 1 (8:00-8:45)</option><option value="2">Period 2 (8:45-9:30)</option><option value="3">Period 3 (9:45-10:30)</option><option value="4">Period 4 (10:30-11:15)</option></select></div><div><label class="text-sm font-medium">Teacher</label><input type="text" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md" disabled="" value="Dr. Priya Patel"/></div></div><div class="flex space-x-4 mb-4"><button class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">Mark All Present</button><button class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">Mark All Absent</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">Copy from Yesterday</button></div><div class="grid gap-2 md:grid-cols-2 lg:grid-cols-3"><div class="border rounded p-3 border-green-200 bg-green-50"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Aarav Sharma</h4><p class="text-xs text-gray-600">9A001</p></div><div class="flex space-x-1"><button class="w-8 h-8 rounded text-xs font-medium bg-green-600 text-white">P</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">A</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">L</button></div></div><p class="text-xs text-gray-500">Last absent: <!-- -->2024-02-10</p></div><div class="border rounded p-3 border-green-200 bg-green-50"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Kavya Gupta</h4><p class="text-xs text-gray-600">9A002</p></div><div class="flex space-x-1"><button class="w-8 h-8 rounded text-xs font-medium bg-green-600 text-white">P</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">A</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">L</button></div></div><p class="text-xs text-gray-500">Last absent: <!-- -->2024-02-08</p></div><div class="border rounded p-3 border-red-200 bg-red-50"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Arjun Singh</h4><p class="text-xs text-gray-600">9A003</p></div><div class="flex space-x-1"><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">P</button><button class="w-8 h-8 rounded text-xs font-medium bg-red-600 text-white">A</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">L</button></div></div><p class="text-xs text-gray-500">Last absent: <!-- -->2024-02-16</p></div><div class="border rounded p-3 border-green-200 bg-green-50"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Riya Patel</h4><p class="text-xs text-gray-600">9A004</p></div><div class="flex space-x-1"><button class="w-8 h-8 rounded text-xs font-medium bg-green-600 text-white">P</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">A</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">L</button></div></div><p class="text-xs text-gray-500">Last absent: <!-- -->2024-02-05</p></div><div class="border rounded p-3 border-yellow-200 bg-yellow-50"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Vikram Kumar</h4><p class="text-xs text-gray-600">9A005</p></div><div class="flex space-x-1"><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">P</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">A</button><button class="w-8 h-8 rounded text-xs font-medium bg-yellow-600 text-white">L</button></div></div><p class="text-xs text-gray-500">Last absent: <!-- -->2024-02-12</p></div><div class="border rounded p-3 border-green-200 bg-green-50"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Sneha Sharma</h4><p class="text-xs text-gray-600">9A006</p></div><div class="flex space-x-1"><button class="w-8 h-8 rounded text-xs font-medium bg-green-600 text-white">P</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">A</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">L</button></div></div><p class="text-xs text-gray-500">Last absent: <!-- -->2024-02-01</p></div><div class="border rounded p-3 border-green-200 bg-green-50"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Rohit Singh</h4><p class="text-xs text-gray-600">9A007</p></div><div class="flex space-x-1"><button class="w-8 h-8 rounded text-xs font-medium bg-green-600 text-white">P</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">A</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">L</button></div></div><p class="text-xs text-gray-500">Last absent: <!-- -->2024-02-03</p></div><div class="border rounded p-3 border-red-200 bg-red-50"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Priya Gupta</h4><p class="text-xs text-gray-600">9A008</p></div><div class="flex space-x-1"><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">P</button><button class="w-8 h-8 rounded text-xs font-medium bg-red-600 text-white">A</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">L</button></div></div><p class="text-xs text-gray-500">Last absent: <!-- -->2024-02-16</p></div><div class="border rounded p-3 border-green-200 bg-green-50"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Amit Patel</h4><p class="text-xs text-gray-600">9A009</p></div><div class="flex space-x-1"><button class="w-8 h-8 rounded text-xs font-medium bg-green-600 text-white">P</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">A</button><button class="w-8 h-8 rounded text-xs font-medium bg-gray-200 text-gray-600">L</button></div></div><p class="text-xs text-gray-500">Last absent: <!-- -->2024-01-28</p></div></div><div class="flex space-x-2 pt-4 border-t"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Save Attendance</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Save as Draft</button><button class="text-blue-600 hover:text-blue-800">Send Notifications</button></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Class-wise Attendance</h3><p class="text-sm text-muted-foreground">Today&#x27;s attendance by class</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between border rounded p-3"><div><h4 class="font-medium text-sm">Grade 9A</h4><p class="text-xs text-gray-600">28<!-- -->/<!-- -->30<!-- --> present</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">93<!-- -->%</div><div class="w-20 bg-gray-200 rounded-full h-2 mt-1"><div class="h-2 rounded-full bg-blue-600" style="width:93%"></div></div></div></div><div class="flex items-center justify-between border rounded p-3"><div><h4 class="font-medium text-sm">Grade 9B</h4><p class="text-xs text-gray-600">27<!-- -->/<!-- -->30<!-- --> present</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">90<!-- -->%</div><div class="w-20 bg-gray-200 rounded-full h-2 mt-1"><div class="h-2 rounded-full bg-blue-600" style="width:90%"></div></div></div></div><div class="flex items-center justify-between border rounded p-3"><div><h4 class="font-medium text-sm">Grade 10A</h4><p class="text-xs text-gray-600">29<!-- -->/<!-- -->32<!-- --> present</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">91<!-- -->%</div><div class="w-20 bg-gray-200 rounded-full h-2 mt-1"><div class="h-2 rounded-full bg-blue-600" style="width:91%"></div></div></div></div><div class="flex items-center justify-between border rounded p-3"><div><h4 class="font-medium text-sm">Grade 10B</h4><p class="text-xs text-gray-600">30<!-- -->/<!-- -->32<!-- --> present</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">94<!-- -->%</div><div class="w-20 bg-gray-200 rounded-full h-2 mt-1"><div class="h-2 rounded-full bg-blue-600" style="width:94%"></div></div></div></div><div class="flex items-center justify-between border rounded p-3"><div><h4 class="font-medium text-sm">Grade 11A</h4><p class="text-xs text-gray-600">25<!-- -->/<!-- -->28<!-- --> present</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">89<!-- -->%</div><div class="w-20 bg-gray-200 rounded-full h-2 mt-1"><div class="h-2 rounded-full bg-blue-600" style="width:89%"></div></div></div></div><div class="flex items-center justify-between border rounded p-3"><div><h4 class="font-medium text-sm">Grade 12A</h4><p class="text-xs text-gray-600">26<!-- -->/<!-- -->28<!-- --> present</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">93<!-- -->%</div><div class="w-20 bg-gray-200 rounded-full h-2 mt-1"><div class="h-2 rounded-full bg-blue-600" style="width:93%"></div></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Attendance Trends</h3><p class="text-sm text-muted-foreground">Weekly attendance patterns</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><span class="text-sm font-medium w-20">Monday</span><div class="w-32 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:94%"></div></div></div><div class="flex items-center space-x-2"><span class="text-sm font-medium">94<!-- -->%</span><span class="text-xs text-green-600">+2%</span></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><span class="text-sm font-medium w-20">Tuesday</span><div class="w-32 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:92%"></div></div></div><div class="flex items-center space-x-2"><span class="text-sm font-medium">92<!-- -->%</span><span class="text-xs text-gray-600">0%</span></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><span class="text-sm font-medium w-20">Wednesday</span><div class="w-32 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:89%"></div></div></div><div class="flex items-center space-x-2"><span class="text-sm font-medium">89<!-- -->%</span><span class="text-xs text-red-600">-3%</span></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><span class="text-sm font-medium w-20">Thursday</span><div class="w-32 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:91%"></div></div></div><div class="flex items-center space-x-2"><span class="text-sm font-medium">91<!-- -->%</span><span class="text-xs text-green-600">+2%</span></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><span class="text-sm font-medium w-20">Friday</span><div class="w-32 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:87%"></div></div></div><div class="flex items-center space-x-2"><span class="text-sm font-medium">87<!-- -->%</span><span class="text-xs text-red-600">-4%</span></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><span class="text-sm font-medium w-20">Saturday</span><div class="w-32 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:85%"></div></div></div><div class="flex items-center space-x-2"><span class="text-sm font-medium">85<!-- -->%</span><span class="text-xs text-red-600">-2%</span></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Attendance Alerts</h3><p class="text-sm text-muted-foreground">Students requiring attention</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="border rounded-lg p-4 border-red-200 bg-red-50"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h4 class="font-semibold">Arjun Singh</h4><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9A</span><span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">68<!-- -->% attendance</span></div><p class="text-sm text-gray-600">9A003</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">Days Absent:</span> <!-- -->8<!-- --> this month</p></div><div><p><span class="font-medium">Last Present:</span> <!-- -->2024-02-12</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Contact Parent</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 border-red-200 bg-red-50"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h4 class="font-semibold">Priya Gupta</h4><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9A</span><span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">72<!-- -->% attendance</span></div><p class="text-sm text-gray-600">9A008</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">Days Absent:</span> <!-- -->6<!-- --> this month</p></div><div><p><span class="font-medium">Last Present:</span> <!-- -->2024-02-14</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Contact Parent</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 border-red-200 bg-red-50"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h4 class="font-semibold">Rohit Kumar</h4><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 10B</span><span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">65<!-- -->% attendance</span></div><p class="text-sm text-gray-600">10B015</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">Days Absent:</span> <!-- -->9<!-- --> this month</p></div><div><p><span class="font-medium">Last Present:</span> <!-- -->2024-02-10</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Contact Parent</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">View Details</button></div></div></div><div class="border rounded-lg p-4 border-red-200 bg-red-50"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h4 class="font-semibold">Sneha Patel</h4><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 11A</span><span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">70<!-- -->% attendance</span></div><p class="text-sm text-gray-600">11A007</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">Days Absent:</span> <!-- -->7<!-- --> this month</p></div><div><p><span class="font-medium">Last Present:</span> <!-- -->2024-02-13</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Contact Parent</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">View Details</button></div></div></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[851,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"6408\",\"static/chunks/app/dashboard/academic/attendance/page-057137e9f334baef.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"DEc8SW0ph9C_XxX68bz_T\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/academic/attendance\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"academic\",{\"children\":[\"attendance\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"academic\",{\"children\":[\"attendance\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"academic\",\"children\",\"attendance\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"academic\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>