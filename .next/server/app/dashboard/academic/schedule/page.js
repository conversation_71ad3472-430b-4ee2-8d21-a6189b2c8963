(()=>{var e={};e.id=2990,e.ids=[2990],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},43451:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>n});var t=a(50482),r=a(69108),d=a(62563),l=a.n(d),i=a(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(s,c);let n=["",{children:["dashboard",{children:["academic",{children:["schedule",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,86602)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/schedule/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94699)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,21342)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/schedule/page.tsx"],m="/dashboard/academic/schedule/page",x={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/academic/schedule/page",pathname:"/dashboard/academic/schedule",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},15621:(e,s,a)=>{Promise.resolve().then(a.bind(a,66695))},66695:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var t=a(95344);a(3729);var r=a(18791);function d(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Academic Schedule"}),t.jsx("p",{className:"text-muted-foreground",children:"Manage class schedules, timetables, and academic calendar"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[t.jsx("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Generate Timetable"}),t.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Add Schedule"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(r.ll,{className:"text-sm font-medium",children:"Total Classes"}),t.jsx("span",{className:"text-2xl",children:"\uD83C\uDFEB"})]}),(0,t.jsxs)(r.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:"156"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Per week"})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(r.ll,{className:"text-sm font-medium",children:"Active Teachers"}),t.jsx("span",{className:"text-2xl",children:"\uD83D\uDC68‍\uD83C\uDFEB"})]}),(0,t.jsxs)(r.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:"24"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Teaching this week"})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(r.ll,{className:"text-sm font-medium",children:"Classrooms"}),t.jsx("span",{className:"text-2xl",children:"\uD83D\uDEAA"})]}),(0,t.jsxs)(r.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:"18"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Available rooms"})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(r.ll,{className:"text-sm font-medium",children:"Utilization"}),t.jsx("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,t.jsxs)(r.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:"87%"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Room utilization"})]})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{children:[t.jsx(r.ll,{children:"Weekly Timetable - Grade 9A"}),t.jsx(r.SZ,{children:"Current week schedule for Grade 9A"})]}),t.jsx(r.aY,{children:t.jsx("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full border-collapse",children:[t.jsx("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[t.jsx("th",{className:"text-left p-2 font-medium",children:"Time"}),t.jsx("th",{className:"text-left p-2 font-medium",children:"Monday"}),t.jsx("th",{className:"text-left p-2 font-medium",children:"Tuesday"}),t.jsx("th",{className:"text-left p-2 font-medium",children:"Wednesday"}),t.jsx("th",{className:"text-left p-2 font-medium",children:"Thursday"}),t.jsx("th",{className:"text-left p-2 font-medium",children:"Friday"})]})}),t.jsx("tbody",{children:[{time:"8:00-8:45",monday:{subject:"Mathematics",teacher:"Dr. Priya",room:"Room 101"},tuesday:{subject:"English",teacher:"Ms. Sneha",room:"Room 102"},wednesday:{subject:"Science",teacher:"Mr. Rajesh",room:"Lab 1"},thursday:{subject:"Mathematics",teacher:"Dr. Priya",room:"Room 101"},friday:{subject:"Social Studies",teacher:"Dr. Amit",room:"Room 103"}},{time:"8:45-9:30",monday:{subject:"English",teacher:"Ms. Sneha",room:"Room 102"},tuesday:{subject:"Science",teacher:"Mr. Rajesh",room:"Lab 1"},wednesday:{subject:"Mathematics",teacher:"Dr. Priya",room:"Room 101"},thursday:{subject:"Hindi",teacher:"Mrs. Kavya",room:"Room 104"},friday:{subject:"English",teacher:"Ms. Sneha",room:"Room 102"}},{time:"9:30-9:45",monday:{subject:"Break",teacher:"",room:""},tuesday:{subject:"Break",teacher:"",room:""},wednesday:{subject:"Break",teacher:"",room:""},thursday:{subject:"Break",teacher:"",room:""},friday:{subject:"Break",teacher:"",room:""}},{time:"9:45-10:30",monday:{subject:"Science",teacher:"Mr. Rajesh",room:"Lab 1"},tuesday:{subject:"Mathematics",teacher:"Dr. Priya",room:"Room 101"},wednesday:{subject:"Hindi",teacher:"Mrs. Kavya",room:"Room 104"},thursday:{subject:"English",teacher:"Ms. Sneha",room:"Room 102"},friday:{subject:"Science",teacher:"Mr. Rajesh",room:"Lab 1"}},{time:"10:30-11:15",monday:{subject:"Social Studies",teacher:"Dr. Amit",room:"Room 103"},tuesday:{subject:"Hindi",teacher:"Mrs. Kavya",room:"Room 104"},wednesday:{subject:"PE",teacher:"Mr. Vikram",room:"Playground"},thursday:{subject:"Science",teacher:"Mr. Rajesh",room:"Lab 1"},friday:{subject:"Mathematics",teacher:"Dr. Priya",room:"Room 101"}}].map((e,s)=>(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[t.jsx("td",{className:"p-2 font-medium text-sm",children:e.time}),["monday","tuesday","wednesday","thursday","friday"].map(s=>{let a=e[s];return t.jsx("td",{className:"p-2",children:"Break"===a.subject?t.jsx("div",{className:"text-center text-gray-500 text-sm",children:"Break"}):(0,t.jsxs)("div",{className:"text-sm",children:[t.jsx("div",{className:"font-medium text-blue-600",children:a.subject}),t.jsx("div",{className:"text-gray-600",children:a.teacher}),t.jsx("div",{className:"text-gray-500 text-xs",children:a.room})]})},s)})]},s))})]})})})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{children:[t.jsx(r.ll,{children:"Class Schedule Overview"}),t.jsx(r.SZ,{children:"Schedule summary by grade"})]}),t.jsx(r.aY,{children:t.jsx("div",{className:"space-y-3",children:[{grade:"Grade 9",sections:4,periods:35,teachers:8,utilization:92},{grade:"Grade 10",sections:4,periods:35,teachers:9,utilization:88},{grade:"Grade 11",sections:3,periods:30,teachers:7,utilization:85},{grade:"Grade 12",sections:3,periods:30,teachers:7,utilization:90}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[t.jsx("h4",{className:"font-medium",children:e.grade}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[e.utilization,"% utilized"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-sm text-gray-600",children:[(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Sections:"})," ",e.sections]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Periods:"})," ",e.periods,"/week"]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Teachers:"})," ",e.teachers]})]}),t.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:t.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.utilization}%`}})})]},s))})})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{children:[t.jsx(r.ll,{children:"Teacher Schedule"}),t.jsx(r.SZ,{children:"Teaching load distribution"})]}),t.jsx(r.aY,{children:t.jsx("div",{className:"space-y-3",children:[{teacher:"Dr. Priya Patel",subject:"Mathematics",periods:18,grades:["9","10"],load:90},{teacher:"Mr. Rajesh Kumar",subject:"Science",periods:16,grades:["9","11"],load:80},{teacher:"Ms. Sneha Gupta",subject:"English",periods:20,grades:["9","10","11"],load:100},{teacher:"Dr. Amit Verma",subject:"Social Studies",periods:14,grades:["9","10"],load:70}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium text-sm",children:e.teacher}),t.jsx("p",{className:"text-xs text-blue-600",children:e.subject})]}),(0,t.jsxs)("span",{className:`text-xs px-2 py-1 rounded-full ${100===e.load?"bg-red-100 text-red-800":e.load>=80?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}`,children:[e.load,"% load"]})]}),t.jsx("div",{className:"text-xs text-gray-600",children:(0,t.jsxs)("span",{children:[e.periods," periods/week • Grades: ",e.grades.join(", ")]})}),t.jsx("div",{className:"w-full bg-gray-200 rounded-full h-1 mt-2",children:t.jsx("div",{className:`h-1 rounded-full ${100===e.load?"bg-red-600":e.load>=80?"bg-yellow-600":"bg-green-600"}`,style:{width:`${e.load}%`}})})]},s))})})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{children:[t.jsx(r.ll,{children:"Academic Calendar"}),t.jsx(r.SZ,{children:"Important academic dates and events"})]}),t.jsx(r.aY,{children:t.jsx("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[{event:"Mid-term Examinations",date:"March 15-25, 2024",type:"Exam",status:"Upcoming",grades:"All Grades"},{event:"Parent-Teacher Meeting",date:"March 30, 2024",type:"Meeting",status:"Scheduled",grades:"All Grades"},{event:"Science Fair",date:"April 10, 2024",type:"Event",status:"Planning",grades:"Grades 6-12"},{event:"Annual Sports Day",date:"April 20, 2024",type:"Event",status:"Planning",grades:"All Grades"},{event:"Final Examinations",date:"May 1-15, 2024",type:"Exam",status:"Scheduled",grades:"All Grades"},{event:"Summer Vacation",date:"May 20 - June 15, 2024",type:"Holiday",status:"Scheduled",grades:"All Grades"}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[t.jsx("h4",{className:"font-medium text-sm",children:e.event}),t.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${"Exam"===e.type?"bg-red-100 text-red-800":"Meeting"===e.type?"bg-blue-100 text-blue-800":"Event"===e.type?"bg-green-100 text-green-800":"bg-purple-100 text-purple-800"}`,children:e.type})]}),t.jsx("p",{className:"text-sm text-gray-600 mb-1",children:e.date}),t.jsx("p",{className:"text-xs text-gray-500 mb-2",children:e.grades}),t.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${"Upcoming"===e.status?"bg-yellow-100 text-yellow-800":"Scheduled"===e.status?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:e.status})]},s))})})]})]})}},18791:(e,s,a)=>{"use strict";a.d(s,{Zb:()=>c,aY:()=>x,SZ:()=>m,Ol:()=>n,ll:()=>o});var t=a(95344),r=a(3729),d=a(56815),l=a(79377);function i(...e){return(0,l.m6)((0,d.W)(e))}let c=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));c.displayName="Card";let n=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:i("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let o=r.forwardRef(({className:e,...s},a)=>t.jsx("h3",{ref:a,className:i("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let m=r.forwardRef(({className:e,...s},a)=>t.jsx("p",{ref:a,className:i("text-sm text-muted-foreground",e),...s}));m.displayName="CardDescription";let x=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:i("p-6 pt-0",e),...s}));x.displayName="CardContent",r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:i("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},86602:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>d,__esModule:()=>r,default:()=>l});let t=(0,a(86843).createProxy)(String.raw`/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/schedule/page.tsx`),{__esModule:r,$$typeof:d}=t,l=t.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,3883,3945,1476,8510],()=>a(43451));module.exports=t})();