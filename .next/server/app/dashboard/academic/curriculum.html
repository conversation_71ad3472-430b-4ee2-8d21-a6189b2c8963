<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/academic/curriculum/page-2522dbc002c6a51a.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Curriculum Management</h1><p class="text-muted-foreground">Manage course curriculum, syllabus, and learning objectives</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Import Curriculum</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Add Subject</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Total Subjects</h3><span class="text-2xl">📚</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">45</div><p class="text-xs text-muted-foreground">Across all grades</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Learning Objectives</h3><span class="text-2xl">🎯</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">1,234</div><p class="text-xs text-muted-foreground">Defined objectives</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Curriculum Updates</h3><span class="text-2xl">🔄</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">12</div><p class="text-xs text-muted-foreground">This semester</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Completion Rate</h3><span class="text-2xl">📈</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">87%</div><p class="text-xs text-muted-foreground">Average progress</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Curriculum by Grade Level</h3><p class="text-sm text-muted-foreground">Subject-wise curriculum overview</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4"><h3 class="text-lg font-semibold mb-3">Grade 9</h3><div class="grid gap-3 md:grid-cols-2"><div class="border rounded p-3 hover:shadow-md transition-shadow"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Mathematics</h4><span class="text-sm text-gray-600">12<!-- -->/<!-- -->15<!-- --> chapters</span></div><p class="text-sm text-blue-600 mb-2">Dr. Priya Patel</p><div class="w-full bg-gray-200 rounded-full h-2 mb-2"><div class="bg-blue-600 h-2 rounded-full" style="width:80%"></div></div><div class="flex justify-between text-xs text-gray-500"><span>80<!-- -->% complete</span><span>3<!-- --> remaining</span></div><div class="flex space-x-2 mt-2"><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700">View Syllabus</button><button class="border border-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-50">Edit</button></div></div><div class="border rounded p-3 hover:shadow-md transition-shadow"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Science</h4><span class="text-sm text-gray-600">14<!-- -->/<!-- -->18<!-- --> chapters</span></div><p class="text-sm text-blue-600 mb-2">Mr. Rajesh Kumar</p><div class="w-full bg-gray-200 rounded-full h-2 mb-2"><div class="bg-blue-600 h-2 rounded-full" style="width:77.77777777777779%"></div></div><div class="flex justify-between text-xs text-gray-500"><span>78<!-- -->% complete</span><span>4<!-- --> remaining</span></div><div class="flex space-x-2 mt-2"><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700">View Syllabus</button><button class="border border-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-50">Edit</button></div></div><div class="border rounded p-3 hover:shadow-md transition-shadow"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">English</h4><span class="text-sm text-gray-600">10<!-- -->/<!-- -->12<!-- --> chapters</span></div><p class="text-sm text-blue-600 mb-2">Ms. Sneha Gupta</p><div class="w-full bg-gray-200 rounded-full h-2 mb-2"><div class="bg-blue-600 h-2 rounded-full" style="width:83.33333333333334%"></div></div><div class="flex justify-between text-xs text-gray-500"><span>83<!-- -->% complete</span><span>2<!-- --> remaining</span></div><div class="flex space-x-2 mt-2"><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700">View Syllabus</button><button class="border border-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-50">Edit</button></div></div><div class="border rounded p-3 hover:shadow-md transition-shadow"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Social Studies</h4><span class="text-sm text-gray-600">13<!-- -->/<!-- -->16<!-- --> chapters</span></div><p class="text-sm text-blue-600 mb-2">Dr. Amit Verma</p><div class="w-full bg-gray-200 rounded-full h-2 mb-2"><div class="bg-blue-600 h-2 rounded-full" style="width:81.25%"></div></div><div class="flex justify-between text-xs text-gray-500"><span>81<!-- -->% complete</span><span>3<!-- --> remaining</span></div><div class="flex space-x-2 mt-2"><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700">View Syllabus</button><button class="border border-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-50">Edit</button></div></div></div></div><div class="border rounded-lg p-4"><h3 class="text-lg font-semibold mb-3">Grade 10</h3><div class="grid gap-3 md:grid-cols-2"><div class="border rounded p-3 hover:shadow-md transition-shadow"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Mathematics</h4><span class="text-sm text-gray-600">11<!-- -->/<!-- -->16<!-- --> chapters</span></div><p class="text-sm text-blue-600 mb-2">Dr. Priya Patel</p><div class="w-full bg-gray-200 rounded-full h-2 mb-2"><div class="bg-blue-600 h-2 rounded-full" style="width:68.75%"></div></div><div class="flex justify-between text-xs text-gray-500"><span>69<!-- -->% complete</span><span>5<!-- --> remaining</span></div><div class="flex space-x-2 mt-2"><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700">View Syllabus</button><button class="border border-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-50">Edit</button></div></div><div class="border rounded p-3 hover:shadow-md transition-shadow"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Physics</h4><span class="text-sm text-gray-600">9<!-- -->/<!-- -->14<!-- --> chapters</span></div><p class="text-sm text-blue-600 mb-2">Mr. Rajesh Kumar</p><div class="w-full bg-gray-200 rounded-full h-2 mb-2"><div class="bg-blue-600 h-2 rounded-full" style="width:64.28571428571429%"></div></div><div class="flex justify-between text-xs text-gray-500"><span>64<!-- -->% complete</span><span>5<!-- --> remaining</span></div><div class="flex space-x-2 mt-2"><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700">View Syllabus</button><button class="border border-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-50">Edit</button></div></div><div class="border rounded p-3 hover:shadow-md transition-shadow"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Chemistry</h4><span class="text-sm text-gray-600">8<!-- -->/<!-- -->12<!-- --> chapters</span></div><p class="text-sm text-blue-600 mb-2">Dr. Kavya Singh</p><div class="w-full bg-gray-200 rounded-full h-2 mb-2"><div class="bg-blue-600 h-2 rounded-full" style="width:66.66666666666666%"></div></div><div class="flex justify-between text-xs text-gray-500"><span>67<!-- -->% complete</span><span>4<!-- --> remaining</span></div><div class="flex space-x-2 mt-2"><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700">View Syllabus</button><button class="border border-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-50">Edit</button></div></div><div class="border rounded p-3 hover:shadow-md transition-shadow"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Biology</h4><span class="text-sm text-gray-600">10<!-- -->/<!-- -->15<!-- --> chapters</span></div><p class="text-sm text-blue-600 mb-2">Ms. Riya Sharma</p><div class="w-full bg-gray-200 rounded-full h-2 mb-2"><div class="bg-blue-600 h-2 rounded-full" style="width:66.66666666666666%"></div></div><div class="flex justify-between text-xs text-gray-500"><span>67<!-- -->% complete</span><span>5<!-- --> remaining</span></div><div class="flex space-x-2 mt-2"><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700">View Syllabus</button><button class="border border-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-50">Edit</button></div></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Subject Curriculum Details</h3><p class="text-sm text-muted-foreground">Detailed curriculum breakdown for Mathematics - Grade 9</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><h4 class="text-lg font-semibold">Unit 1: Number Systems</h4><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Completed</span></div><div class="grid gap-4 md:grid-cols-2"><div><h5 class="font-medium mb-2">Chapters</h5><ul class="text-sm text-gray-600 space-y-1"><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-blue-600 rounded-full"></span><span>Real Numbers</span></li><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-blue-600 rounded-full"></span><span>Polynomials</span></li></ul></div><div><h5 class="font-medium mb-2">Learning Objectives</h5><ul class="text-sm text-gray-600 space-y-1"><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-green-600 rounded-full"></span><span>Understand rational and irrational numbers</span></li><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-green-600 rounded-full"></span><span>Perform operations on polynomials</span></li></ul></div></div><div class="flex items-center justify-between mt-4 pt-3 border-t"><div class="flex space-x-4 text-sm text-gray-600"><span><span class="font-medium">Duration:</span> <!-- -->3 weeks</span><span><span class="font-medium">Assessments:</span> <!-- -->2</span></div><div class="flex space-x-2"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View Details</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">Edit Unit</button></div></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><h4 class="text-lg font-semibold">Unit 2: Algebra</h4><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">In Progress</span></div><div class="grid gap-4 md:grid-cols-2"><div><h5 class="font-medium mb-2">Chapters</h5><ul class="text-sm text-gray-600 space-y-1"><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-blue-600 rounded-full"></span><span>Linear Equations</span></li><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-blue-600 rounded-full"></span><span>Coordinate Geometry</span></li></ul></div><div><h5 class="font-medium mb-2">Learning Objectives</h5><ul class="text-sm text-gray-600 space-y-1"><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-green-600 rounded-full"></span><span>Solve linear equations in two variables</span></li><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-green-600 rounded-full"></span><span>Plot points on coordinate plane</span></li></ul></div></div><div class="flex items-center justify-between mt-4 pt-3 border-t"><div class="flex space-x-4 text-sm text-gray-600"><span><span class="font-medium">Duration:</span> <!-- -->4 weeks</span><span><span class="font-medium">Assessments:</span> <!-- -->1</span></div><div class="flex space-x-2"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View Details</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">Edit Unit</button></div></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><h4 class="text-lg font-semibold">Unit 3: Geometry</h4><span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Upcoming</span></div><div class="grid gap-4 md:grid-cols-2"><div><h5 class="font-medium mb-2">Chapters</h5><ul class="text-sm text-gray-600 space-y-1"><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-blue-600 rounded-full"></span><span>Triangles</span></li><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-blue-600 rounded-full"></span><span>Quadrilaterals</span></li><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-blue-600 rounded-full"></span><span>Circles</span></li></ul></div><div><h5 class="font-medium mb-2">Learning Objectives</h5><ul class="text-sm text-gray-600 space-y-1"><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-green-600 rounded-full"></span><span>Prove triangle congruence</span></li><li class="flex items-center space-x-2"><span class="w-2 h-2 bg-green-600 rounded-full"></span><span>Understand properties of quadrilaterals</span></li></ul></div></div><div class="flex items-center justify-between mt-4 pt-3 border-t"><div class="flex space-x-4 text-sm text-gray-600"><span><span class="font-medium">Duration:</span> <!-- -->5 weeks</span><span><span class="font-medium">Assessments:</span> <!-- -->3</span></div><div class="flex space-x-2"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View Details</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">Edit Unit</button></div></div></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Curriculum Tools</h3><p class="text-sm text-muted-foreground">Manage curriculum resources and tools</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">📝</span><div><p class="font-medium text-sm">Lesson Plan Generator</p><p class="text-xs text-gray-600">Create structured lesson plans</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">🎯</span><div><p class="font-medium text-sm">Learning Objective Mapper</p><p class="text-xs text-gray-600">Map objectives to assessments</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">📊</span><div><p class="font-medium text-sm">Progress Tracker</p><p class="text-xs text-gray-600">Track curriculum completion</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">📚</span><div><p class="font-medium text-sm">Resource Library</p><p class="text-xs text-gray-600">Access teaching resources</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">✅</span><div><p class="font-medium text-sm">Standards Alignment</p><p class="text-xs text-gray-600">Align with education standards</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Updates</h3><p class="text-sm text-muted-foreground">Latest curriculum changes and updates</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="border-b pb-2"><div class="flex items-start justify-between"><div><p class="font-medium text-sm">Mathematics Grade 10</p><p class="text-xs text-gray-600">Added new chapter on Statistics</p><p class="text-xs text-blue-600">Dr. Priya Patel</p></div><span class="text-xs text-gray-500">2024-02-15</span></div></div><div class="border-b pb-2"><div class="flex items-start justify-between"><div><p class="font-medium text-sm">Science Grade 9</p><p class="text-xs text-gray-600">Updated lab experiments list</p><p class="text-xs text-blue-600">Mr. Rajesh Kumar</p></div><span class="text-xs text-gray-500">2024-02-14</span></div></div><div class="border-b pb-2"><div class="flex items-start justify-between"><div><p class="font-medium text-sm">English Grade 11</p><p class="text-xs text-gray-600">Revised literature syllabus</p><p class="text-xs text-blue-600">Ms. Sneha Gupta</p></div><span class="text-xs text-gray-500">2024-02-13</span></div></div><div class="border-b pb-2"><div class="flex items-start justify-between"><div><p class="font-medium text-sm">Social Studies Grade 8</p><p class="text-xs text-gray-600">Added current affairs section</p><p class="text-xs text-blue-600">Dr. Amit Verma</p></div><span class="text-xs text-gray-500">2024-02-12</span></div></div></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[8640,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"3887\",\"static/chunks/app/dashboard/academic/curriculum/page-2522dbc002c6a51a.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"DEc8SW0ph9C_XxX68bz_T\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/academic/curriculum\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"academic\",{\"children\":[\"curriculum\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"academic\",{\"children\":[\"curriculum\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"academic\",\"children\",\"curriculum\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"academic\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>