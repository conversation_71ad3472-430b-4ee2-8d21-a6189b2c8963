<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/academic/assessments/page-08b6bc7191fa4221.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Assessments &amp; Examinations</h1><p class="text-muted-foreground">Create, schedule, and manage student assessments and examinations</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Question Bank</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Create Assessment</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Upcoming Exams</h3><span class="text-2xl">📝</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">12</div><p class="text-xs text-muted-foreground">Next 2 weeks</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Pending Evaluations</h3><span class="text-2xl">📊</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">156</div><p class="text-xs text-muted-foreground">Answer sheets</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Completed Assessments</h3><span class="text-2xl">✅</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">89</div><p class="text-xs text-muted-foreground">This month</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Average Score</h3><span class="text-2xl">🎯</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">78%</div><p class="text-xs text-muted-foreground">Class average</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Upcoming Assessments</h3><p class="text-sm text-muted-foreground">Scheduled exams and tests for the next two weeks</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Mathematics</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9</span><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Unit Test</span></div><p class="text-blue-600 font-medium">Dr. Priya Patel</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-20</p><p><span class="font-medium">⏰ Time:</span> <!-- -->10:00 AM - 12:00 PM</p><p><span class="font-medium">⏱️ Duration:</span> <!-- -->2 hours</p></div><div><p><span class="font-medium">📊 Total Marks:</span> <!-- -->100</p><p><span class="font-medium">👥 Students:</span> <!-- -->120</p></div></div><div class="mt-3"><p class="text-sm font-medium text-gray-700 mb-1">Topics Covered:</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Polynomials</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Linear Equations</span></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Details</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Edit Assessment</button><button class="text-green-600 hover:text-green-800 text-sm">Generate Paper</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Science</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 10</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Practical Exam</span></div><p class="text-blue-600 font-medium">Mr. Rajesh Kumar</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-22</p><p><span class="font-medium">⏰ Time:</span> <!-- -->2:00 PM - 4:00 PM</p><p><span class="font-medium">⏱️ Duration:</span> <!-- -->2 hours</p></div><div><p><span class="font-medium">📊 Total Marks:</span> <!-- -->50</p><p><span class="font-medium">👥 Students:</span> <!-- -->115</p></div></div><div class="mt-3"><p class="text-sm font-medium text-gray-700 mb-1">Topics Covered:</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Light</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Electricity</span></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Details</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Edit Assessment</button><button class="text-green-600 hover:text-green-800 text-sm">Generate Paper</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">English</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 11</span><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Mid-term Exam</span></div><p class="text-blue-600 font-medium">Ms. Sneha Gupta</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-25</p><p><span class="font-medium">⏰ Time:</span> <!-- -->9:00 AM - 12:00 PM</p><p><span class="font-medium">⏱️ Duration:</span> <!-- -->3 hours</p></div><div><p><span class="font-medium">📊 Total Marks:</span> <!-- -->150</p><p><span class="font-medium">👥 Students:</span> <!-- -->95</p></div></div><div class="mt-3"><p class="text-sm font-medium text-gray-700 mb-1">Topics Covered:</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Literature</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Grammar</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Composition</span></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Details</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Edit Assessment</button><button class="text-green-600 hover:text-green-800 text-sm">Generate Paper</button></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Create New Assessment</h3><p class="text-sm text-muted-foreground">Set up a new exam or test</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-2"><div><label class="text-sm font-medium">Subject</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Subject</option><option value="mathematics">Mathematics</option><option value="science">Science</option><option value="english">English</option><option value="social-studies">Social Studies</option><option value="hindi">Hindi</option></select></div><div><label class="text-sm font-medium">Grade</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Grade</option><option value="grade-9">Grade 9</option><option value="grade-10">Grade 10</option><option value="grade-11">Grade 11</option><option value="grade-12">Grade 12</option></select></div><div><label class="text-sm font-medium">Assessment Type</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Type</option><option value="unit-test">Unit Test</option><option value="mid-term">Mid-term Exam</option><option value="final-exam">Final Exam</option><option value="practical">Practical Exam</option><option value="assignment">Assignment</option></select></div><div><label class="text-sm font-medium">Total Marks</label><input type="number" placeholder="100" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"/></div><div><label class="text-sm font-medium">Date</label><input type="date" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"/></div><div><label class="text-sm font-medium">Duration (minutes)</label><input type="number" placeholder="120" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"/></div></div><div class="mt-4"><label class="text-sm font-medium">Topics to Cover</label><textarea rows="3" placeholder="Enter topics separated by commas..." class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"></textarea></div><div class="mt-4"><label class="text-sm font-medium">Instructions</label><textarea rows="4" placeholder="Enter exam instructions..." class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"></textarea></div><div class="mt-4 flex space-x-2"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Create Assessment</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Save as Draft</button><button class="text-blue-600 hover:text-blue-800">Use Template</button></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Assessment Results</h3><p class="text-sm text-muted-foreground">Performance overview of completed assessments</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Mathematics</h4><p class="text-xs text-blue-600">Grade 9</p></div><span class="text-sm font-medium">78<!-- -->% avg</span></div><div class="grid grid-cols-3 gap-2 text-xs text-gray-600"><div>Highest: <!-- -->95<!-- -->%</div><div>Lowest: <!-- -->45<!-- -->%</div><div>Students: <!-- -->120</div></div><div class="w-full bg-gray-200 rounded-full h-2 mt-2"><div class="bg-blue-600 h-2 rounded-full" style="width:78%"></div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Science</h4><p class="text-xs text-blue-600">Grade 10</p></div><span class="text-sm font-medium">82<!-- -->% avg</span></div><div class="grid grid-cols-3 gap-2 text-xs text-gray-600"><div>Highest: <!-- -->98<!-- -->%</div><div>Lowest: <!-- -->52<!-- -->%</div><div>Students: <!-- -->115</div></div><div class="w-full bg-gray-200 rounded-full h-2 mt-2"><div class="bg-blue-600 h-2 rounded-full" style="width:82%"></div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">English</h4><p class="text-xs text-blue-600">Grade 11</p></div><span class="text-sm font-medium">75<!-- -->% avg</span></div><div class="grid grid-cols-3 gap-2 text-xs text-gray-600"><div>Highest: <!-- -->92<!-- -->%</div><div>Lowest: <!-- -->48<!-- -->%</div><div>Students: <!-- -->95</div></div><div class="w-full bg-gray-200 rounded-full h-2 mt-2"><div class="bg-blue-600 h-2 rounded-full" style="width:75%"></div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Social Studies</h4><p class="text-xs text-blue-600">Grade 9</p></div><span class="text-sm font-medium">80<!-- -->% avg</span></div><div class="grid grid-cols-3 gap-2 text-xs text-gray-600"><div>Highest: <!-- -->94<!-- -->%</div><div>Lowest: <!-- -->58<!-- -->%</div><div>Students: <!-- -->118</div></div><div class="w-full bg-gray-200 rounded-full h-2 mt-2"><div class="bg-blue-600 h-2 rounded-full" style="width:80%"></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Assessment Tools</h3><p class="text-sm text-muted-foreground">Tools for creating and managing assessments</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">❓</span><div><p class="font-medium text-sm">Question Bank</p><p class="text-xs text-gray-600">Access pre-made questions</p></div></div><div class="text-right"><p class="text-xs text-gray-500">2,456 questions</p><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700 mt-1">Use</button></div></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">🤖</span><div><p class="font-medium text-sm">Auto Paper Generator</p><p class="text-xs text-gray-600">Generate papers automatically</p></div></div><div class="text-right"><p class="text-xs text-gray-500">15 templates</p><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700 mt-1">Use</button></div></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">🔑</span><div><p class="font-medium text-sm">Answer Key Creator</p><p class="text-xs text-gray-600">Create answer keys</p></div></div><div class="text-right"><p class="text-xs text-gray-500">89 keys</p><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700 mt-1">Use</button></div></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">🧮</span><div><p class="font-medium text-sm">Grade Calculator</p><p class="text-xs text-gray-600">Calculate grades and statistics</p></div></div><div class="text-right"><p class="text-xs text-gray-500">Active</p><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700 mt-1">Use</button></div></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">📈</span><div><p class="font-medium text-sm">Result Analyzer</p><p class="text-xs text-gray-600">Analyze assessment results</p></div></div><div class="text-right"><p class="text-xs text-gray-500">12 reports</p><button class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700 mt-1">Use</button></div></div></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[9176,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"4973\",\"static/chunks/app/dashboard/academic/assessments/page-08b6bc7191fa4221.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"DEc8SW0ph9C_XxX68bz_T\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/academic/assessments\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"academic\",{\"children\":[\"assessments\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"academic\",{\"children\":[\"assessments\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"academic\",\"children\",\"assessments\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"academic\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>