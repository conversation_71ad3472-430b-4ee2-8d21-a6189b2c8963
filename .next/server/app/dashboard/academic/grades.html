<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/academic/grades/page-5c532ca30dbb7b90.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Grade Management</h1><p class="text-muted-foreground">Record, manage, and analyze student grades and academic performance</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Export Grades</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Enter Grades</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Pending Grades</h3><span class="text-2xl">📝</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">156</div><p class="text-xs text-muted-foreground">Awaiting entry</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Class Average</h3><span class="text-2xl">📊</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">78%</div><p class="text-xs text-muted-foreground">Overall performance</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Top Performers</h3><span class="text-2xl">🏆</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">23</div><p class="text-xs text-muted-foreground">Above 90%</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Need Support</h3><span class="text-2xl">⚠️</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">12</div><p class="text-xs text-muted-foreground">Below 60%</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Grade Entry - Mathematics Grade 9A</h3><p class="text-sm text-muted-foreground">Enter grades for Unit Test 2 - Polynomials</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="grid gap-4 md:grid-cols-4"><div><label class="text-sm font-medium">Assessment</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Assessment</option><option value="unit-test-2">Unit Test 2 - Polynomials</option><option value="mid-term">Mid-term Examination</option><option value="assignment-1">Assignment 1</option></select></div><div><label class="text-sm font-medium">Total Marks</label><input type="number" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md" value="100"/></div><div><label class="text-sm font-medium">Date</label><input type="date" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md" value="2024-02-15"/></div><div><label class="text-sm font-medium">Weightage</label><input type="number" placeholder="20%" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md" value="20"/></div></div><div class="overflow-x-auto"><table class="w-full border-collapse"><thead><tr class="border-b"><th class="text-left p-2 font-medium">Roll No.</th><th class="text-left p-2 font-medium">Student Name</th><th class="text-left p-2 font-medium">Marks Obtained</th><th class="text-left p-2 font-medium">Percentage</th><th class="text-left p-2 font-medium">Grade</th><th class="text-left p-2 font-medium">Remarks</th></tr></thead><tbody><tr class="border-b hover:bg-gray-50"><td class="p-2 font-medium">9A001</td><td class="p-2">Aarav Sharma</td><td class="p-2"><input type="number" class="w-20 px-2 py-1 border border-gray-300 rounded text-sm" max="100" min="0" value="85"/></td><td class="p-2"><span class="font-medium text-blue-600">85<!-- -->%</span></td><td class="p-2"><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">A</span></td><td class="p-2"><input type="text" class="w-32 px-2 py-1 border border-gray-300 rounded text-sm" placeholder="Remarks" value="Excellent"/></td></tr><tr class="border-b hover:bg-gray-50"><td class="p-2 font-medium">9A002</td><td class="p-2">Kavya Gupta</td><td class="p-2"><input type="number" class="w-20 px-2 py-1 border border-gray-300 rounded text-sm" max="100" min="0" value="78"/></td><td class="p-2"><span class="font-medium text-blue-600">78<!-- -->%</span></td><td class="p-2"><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">B+</span></td><td class="p-2"><input type="text" class="w-32 px-2 py-1 border border-gray-300 rounded text-sm" placeholder="Remarks" value="Good"/></td></tr><tr class="border-b hover:bg-gray-50"><td class="p-2 font-medium">9A003</td><td class="p-2">Arjun Singh</td><td class="p-2"><input type="number" class="w-20 px-2 py-1 border border-gray-300 rounded text-sm" max="100" min="0" value="92"/></td><td class="p-2"><span class="font-medium text-green-600">92<!-- -->%</span></td><td class="p-2"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">A+</span></td><td class="p-2"><input type="text" class="w-32 px-2 py-1 border border-gray-300 rounded text-sm" placeholder="Remarks" value="Outstanding"/></td></tr><tr class="border-b hover:bg-gray-50"><td class="p-2 font-medium">9A004</td><td class="p-2">Riya Patel</td><td class="p-2"><input type="number" class="w-20 px-2 py-1 border border-gray-300 rounded text-sm" max="100" min="0" value="65"/></td><td class="p-2"><span class="font-medium text-yellow-600">65<!-- -->%</span></td><td class="p-2"><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">B</span></td><td class="p-2"><input type="text" class="w-32 px-2 py-1 border border-gray-300 rounded text-sm" placeholder="Remarks" value="Satisfactory"/></td></tr><tr class="border-b hover:bg-gray-50"><td class="p-2 font-medium">9A005</td><td class="p-2">Vikram Kumar</td><td class="p-2"><input type="number" class="w-20 px-2 py-1 border border-gray-300 rounded text-sm" max="100" min="0" value="58"/></td><td class="p-2"><span class="font-medium text-red-600">58<!-- -->%</span></td><td class="p-2"><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">C+</span></td><td class="p-2"><input type="text" class="w-32 px-2 py-1 border border-gray-300 rounded text-sm" placeholder="Remarks" value="Needs improvement"/></td></tr></tbody></table></div><div class="flex space-x-2"><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">Save Grades</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Save as Draft</button><button class="text-blue-600 hover:text-blue-800">Auto Calculate</button></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Grade Distribution</h3><p class="text-sm text-muted-foreground">Performance distribution across grades</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><div class="w-4 h-4 rounded bg-green-600"></div><span class="text-sm font-medium">A+ (90-100%)</span></div><div class="flex items-center space-x-2"><span class="text-sm text-gray-600">23<!-- --> students</span><span class="text-sm font-medium">19<!-- -->%</span></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><div class="w-4 h-4 rounded bg-blue-600"></div><span class="text-sm font-medium">A (80-89%)</span></div><div class="flex items-center space-x-2"><span class="text-sm text-gray-600">45<!-- --> students</span><span class="text-sm font-medium">37<!-- -->%</span></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><div class="w-4 h-4 rounded bg-yellow-600"></div><span class="text-sm font-medium">B+ (70-79%)</span></div><div class="flex items-center space-x-2"><span class="text-sm text-gray-600">32<!-- --> students</span><span class="text-sm font-medium">26<!-- -->%</span></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><div class="w-4 h-4 rounded bg-orange-600"></div><span class="text-sm font-medium">B (60-69%)</span></div><div class="flex items-center space-x-2"><span class="text-sm text-gray-600">15<!-- --> students</span><span class="text-sm font-medium">12<!-- -->%</span></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><div class="w-4 h-4 rounded bg-red-600"></div><span class="text-sm font-medium">C+ (50-59%)</span></div><div class="flex items-center space-x-2"><span class="text-sm text-gray-600">6<!-- --> students</span><span class="text-sm font-medium">5<!-- -->%</span></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><div class="w-4 h-4 rounded bg-gray-600"></div><span class="text-sm font-medium">Below 50%</span></div><div class="flex items-center space-x-2"><span class="text-sm text-gray-600">1<!-- --> students</span><span class="text-sm font-medium">1<!-- -->%</span></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Subject Performance</h3><p class="text-sm text-muted-foreground">Average performance by subject</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">Mathematics</h4><div class="flex items-center space-x-2"><span class="text-sm font-medium">78<!-- -->%</span><span class="text-xs text-green-600">+2%</span></div></div><div class="w-full bg-gray-200 rounded-full h-2 mb-1"><div class="bg-blue-600 h-2 rounded-full" style="width:78%"></div></div><p class="text-xs text-gray-500">120<!-- --> students</p></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">Science</h4><div class="flex items-center space-x-2"><span class="text-sm font-medium">82<!-- -->%</span><span class="text-xs text-green-600">+5%</span></div></div><div class="w-full bg-gray-200 rounded-full h-2 mb-1"><div class="bg-blue-600 h-2 rounded-full" style="width:82%"></div></div><p class="text-xs text-gray-500">118<!-- --> students</p></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">English</h4><div class="flex items-center space-x-2"><span class="text-sm font-medium">75<!-- -->%</span><span class="text-xs text-red-600">-1%</span></div></div><div class="w-full bg-gray-200 rounded-full h-2 mb-1"><div class="bg-blue-600 h-2 rounded-full" style="width:75%"></div></div><p class="text-xs text-gray-500">122<!-- --> students</p></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">Social Studies</h4><div class="flex items-center space-x-2"><span class="text-sm font-medium">80<!-- -->%</span><span class="text-xs text-green-600">+3%</span></div></div><div class="w-full bg-gray-200 rounded-full h-2 mb-1"><div class="bg-blue-600 h-2 rounded-full" style="width:80%"></div></div><p class="text-xs text-gray-500">119<!-- --> students</p></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">Hindi</h4><div class="flex items-center space-x-2"><span class="text-sm font-medium">77<!-- -->%</span><span class="text-xs text-gray-600">0%</span></div></div><div class="w-full bg-gray-200 rounded-full h-2 mb-1"><div class="bg-blue-600 h-2 rounded-full" style="width:77%"></div></div><p class="text-xs text-gray-500">121<!-- --> students</p></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Individual Student Performance</h3><p class="text-sm text-muted-foreground">Detailed performance tracking for Grade 9A</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><div><h4 class="font-semibold">Aarav Sharma</h4><p class="text-sm text-gray-600">9A001<!-- --> • Rank: <!-- -->3</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">85<!-- -->%</div><span class="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800">Improving</span></div></div><div class="grid grid-cols-5 gap-2 text-sm"><div class="text-center"><p class="text-xs text-gray-600 capitalize">mathematics</p><p class="font-medium text-blue-600">85<!-- -->%</p></div><div class="text-center"><p class="text-xs text-gray-600 capitalize">science</p><p class="font-medium text-blue-600">88<!-- -->%</p></div><div class="text-center"><p class="text-xs text-gray-600 capitalize">english</p><p class="font-medium text-blue-600">82<!-- -->%</p></div><div class="text-center"><p class="text-xs text-gray-600 capitalize">social</p><p class="font-medium text-blue-600">87<!-- -->%</p></div><div class="text-center"><p class="text-xs text-gray-600 capitalize">hindi</p><p class="font-medium text-blue-600">83<!-- -->%</p></div></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><div><h4 class="font-semibold">Kavya Gupta</h4><p class="text-sm text-gray-600">9A002<!-- --> • Rank: <!-- -->8</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">78<!-- -->%</div><span class="text-xs px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">Stable</span></div></div><div class="grid grid-cols-5 gap-2 text-sm"><div class="text-center"><p class="text-xs text-gray-600 capitalize">mathematics</p><p class="font-medium text-yellow-600">78<!-- -->%</p></div><div class="text-center"><p class="text-xs text-gray-600 capitalize">science</p><p class="font-medium text-blue-600">80<!-- -->%</p></div><div class="text-center"><p class="text-xs text-gray-600 capitalize">english</p><p class="font-medium text-yellow-600">75<!-- -->%</p></div><div class="text-center"><p class="text-xs text-gray-600 capitalize">social</p><p class="font-medium text-yellow-600">79<!-- -->%</p></div><div class="text-center"><p class="text-xs text-gray-600 capitalize">hindi</p><p class="font-medium text-yellow-600">78<!-- -->%</p></div></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-3"><div><h4 class="font-semibold">Arjun Singh</h4><p class="text-sm text-gray-600">9A003<!-- --> • Rank: <!-- -->1</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">92<!-- -->%</div><span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">Excellent</span></div></div><div class="grid grid-cols-5 gap-2 text-sm"><div class="text-center"><p class="text-xs text-gray-600 capitalize">mathematics</p><p class="font-medium text-green-600">92<!-- -->%</p></div><div class="text-center"><p class="text-xs text-gray-600 capitalize">science</p><p class="font-medium text-green-600">94<!-- -->%</p></div><div class="text-center"><p class="text-xs text-gray-600 capitalize">english</p><p class="font-medium text-blue-600">89<!-- -->%</p></div><div class="text-center"><p class="text-xs text-gray-600 capitalize">social</p><p class="font-medium text-green-600">93<!-- -->%</p></div><div class="text-center"><p class="text-xs text-gray-600 capitalize">hindi</p><p class="font-medium text-green-600">90<!-- -->%</p></div></div></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[1120,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"2992\",\"static/chunks/app/dashboard/academic/grades/page-5c532ca30dbb7b90.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"DEc8SW0ph9C_XxX68bz_T\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/academic/grades\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"academic\",{\"children\":[\"grades\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"academic\",{\"children\":[\"grades\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"academic\",\"children\",\"grades\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"academic\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>