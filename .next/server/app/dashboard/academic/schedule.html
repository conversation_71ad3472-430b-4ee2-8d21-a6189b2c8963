<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/academic/schedule/page-53557b9d790c1b45.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Academic Schedule</h1><p class="text-muted-foreground">Manage class schedules, timetables, and academic calendar</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Generate Timetable</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Add Schedule</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Total Classes</h3><span class="text-2xl">🏫</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">156</div><p class="text-xs text-muted-foreground">Per week</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Active Teachers</h3><span class="text-2xl">👨‍🏫</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">24</div><p class="text-xs text-muted-foreground">Teaching this week</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Classrooms</h3><span class="text-2xl">🚪</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">18</div><p class="text-xs text-muted-foreground">Available rooms</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Utilization</h3><span class="text-2xl">📊</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">87%</div><p class="text-xs text-muted-foreground">Room utilization</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Weekly Timetable - Grade 9A</h3><p class="text-sm text-muted-foreground">Current week schedule for Grade 9A</p></div><div class="p-6 pt-0"><div class="overflow-x-auto"><table class="w-full border-collapse"><thead><tr class="border-b"><th class="text-left p-2 font-medium">Time</th><th class="text-left p-2 font-medium">Monday</th><th class="text-left p-2 font-medium">Tuesday</th><th class="text-left p-2 font-medium">Wednesday</th><th class="text-left p-2 font-medium">Thursday</th><th class="text-left p-2 font-medium">Friday</th></tr></thead><tbody><tr class="border-b hover:bg-gray-50"><td class="p-2 font-medium text-sm">8:00-8:45</td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Mathematics</div><div class="text-gray-600">Dr. Priya</div><div class="text-gray-500 text-xs">Room 101</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">English</div><div class="text-gray-600">Ms. Sneha</div><div class="text-gray-500 text-xs">Room 102</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Science</div><div class="text-gray-600">Mr. Rajesh</div><div class="text-gray-500 text-xs">Lab 1</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Mathematics</div><div class="text-gray-600">Dr. Priya</div><div class="text-gray-500 text-xs">Room 101</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Social Studies</div><div class="text-gray-600">Dr. Amit</div><div class="text-gray-500 text-xs">Room 103</div></div></td></tr><tr class="border-b hover:bg-gray-50"><td class="p-2 font-medium text-sm">8:45-9:30</td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">English</div><div class="text-gray-600">Ms. Sneha</div><div class="text-gray-500 text-xs">Room 102</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Science</div><div class="text-gray-600">Mr. Rajesh</div><div class="text-gray-500 text-xs">Lab 1</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Mathematics</div><div class="text-gray-600">Dr. Priya</div><div class="text-gray-500 text-xs">Room 101</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Hindi</div><div class="text-gray-600">Mrs. Kavya</div><div class="text-gray-500 text-xs">Room 104</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">English</div><div class="text-gray-600">Ms. Sneha</div><div class="text-gray-500 text-xs">Room 102</div></div></td></tr><tr class="border-b hover:bg-gray-50"><td class="p-2 font-medium text-sm">9:30-9:45</td><td class="p-2"><div class="text-center text-gray-500 text-sm">Break</div></td><td class="p-2"><div class="text-center text-gray-500 text-sm">Break</div></td><td class="p-2"><div class="text-center text-gray-500 text-sm">Break</div></td><td class="p-2"><div class="text-center text-gray-500 text-sm">Break</div></td><td class="p-2"><div class="text-center text-gray-500 text-sm">Break</div></td></tr><tr class="border-b hover:bg-gray-50"><td class="p-2 font-medium text-sm">9:45-10:30</td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Science</div><div class="text-gray-600">Mr. Rajesh</div><div class="text-gray-500 text-xs">Lab 1</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Mathematics</div><div class="text-gray-600">Dr. Priya</div><div class="text-gray-500 text-xs">Room 101</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Hindi</div><div class="text-gray-600">Mrs. Kavya</div><div class="text-gray-500 text-xs">Room 104</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">English</div><div class="text-gray-600">Ms. Sneha</div><div class="text-gray-500 text-xs">Room 102</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Science</div><div class="text-gray-600">Mr. Rajesh</div><div class="text-gray-500 text-xs">Lab 1</div></div></td></tr><tr class="border-b hover:bg-gray-50"><td class="p-2 font-medium text-sm">10:30-11:15</td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Social Studies</div><div class="text-gray-600">Dr. Amit</div><div class="text-gray-500 text-xs">Room 103</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Hindi</div><div class="text-gray-600">Mrs. Kavya</div><div class="text-gray-500 text-xs">Room 104</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">PE</div><div class="text-gray-600">Mr. Vikram</div><div class="text-gray-500 text-xs">Playground</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Science</div><div class="text-gray-600">Mr. Rajesh</div><div class="text-gray-500 text-xs">Lab 1</div></div></td><td class="p-2"><div class="text-sm"><div class="font-medium text-blue-600">Mathematics</div><div class="text-gray-600">Dr. Priya</div><div class="text-gray-500 text-xs">Room 101</div></div></td></tr></tbody></table></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Class Schedule Overview</h3><p class="text-sm text-muted-foreground">Schedule summary by grade</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Grade 9</h4><span class="text-sm text-gray-600">92<!-- -->% utilized</span></div><div class="grid grid-cols-3 gap-2 text-sm text-gray-600"><div><span class="font-medium">Sections:</span> <!-- -->4</div><div><span class="font-medium">Periods:</span> <!-- -->35<!-- -->/week</div><div><span class="font-medium">Teachers:</span> <!-- -->8</div></div><div class="w-full bg-gray-200 rounded-full h-2 mt-2"><div class="bg-blue-600 h-2 rounded-full" style="width:92%"></div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Grade 10</h4><span class="text-sm text-gray-600">88<!-- -->% utilized</span></div><div class="grid grid-cols-3 gap-2 text-sm text-gray-600"><div><span class="font-medium">Sections:</span> <!-- -->4</div><div><span class="font-medium">Periods:</span> <!-- -->35<!-- -->/week</div><div><span class="font-medium">Teachers:</span> <!-- -->9</div></div><div class="w-full bg-gray-200 rounded-full h-2 mt-2"><div class="bg-blue-600 h-2 rounded-full" style="width:88%"></div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Grade 11</h4><span class="text-sm text-gray-600">85<!-- -->% utilized</span></div><div class="grid grid-cols-3 gap-2 text-sm text-gray-600"><div><span class="font-medium">Sections:</span> <!-- -->3</div><div><span class="font-medium">Periods:</span> <!-- -->30<!-- -->/week</div><div><span class="font-medium">Teachers:</span> <!-- -->7</div></div><div class="w-full bg-gray-200 rounded-full h-2 mt-2"><div class="bg-blue-600 h-2 rounded-full" style="width:85%"></div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium">Grade 12</h4><span class="text-sm text-gray-600">90<!-- -->% utilized</span></div><div class="grid grid-cols-3 gap-2 text-sm text-gray-600"><div><span class="font-medium">Sections:</span> <!-- -->3</div><div><span class="font-medium">Periods:</span> <!-- -->30<!-- -->/week</div><div><span class="font-medium">Teachers:</span> <!-- -->7</div></div><div class="w-full bg-gray-200 rounded-full h-2 mt-2"><div class="bg-blue-600 h-2 rounded-full" style="width:90%"></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Teacher Schedule</h3><p class="text-sm text-muted-foreground">Teaching load distribution</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Dr. Priya Patel</h4><p class="text-xs text-blue-600">Mathematics</p></div><span class="text-xs px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">90<!-- -->% load</span></div><div class="text-xs text-gray-600"><span>18<!-- --> periods/week • Grades: <!-- -->9, 10</span></div><div class="w-full bg-gray-200 rounded-full h-1 mt-2"><div class="h-1 rounded-full bg-yellow-600" style="width:90%"></div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Mr. Rajesh Kumar</h4><p class="text-xs text-blue-600">Science</p></div><span class="text-xs px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">80<!-- -->% load</span></div><div class="text-xs text-gray-600"><span>16<!-- --> periods/week • Grades: <!-- -->9, 11</span></div><div class="w-full bg-gray-200 rounded-full h-1 mt-2"><div class="h-1 rounded-full bg-yellow-600" style="width:80%"></div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Ms. Sneha Gupta</h4><p class="text-xs text-blue-600">English</p></div><span class="text-xs px-2 py-1 rounded-full bg-red-100 text-red-800">100<!-- -->% load</span></div><div class="text-xs text-gray-600"><span>20<!-- --> periods/week • Grades: <!-- -->9, 10, 11</span></div><div class="w-full bg-gray-200 rounded-full h-1 mt-2"><div class="h-1 rounded-full bg-red-600" style="width:100%"></div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><div><h4 class="font-medium text-sm">Dr. Amit Verma</h4><p class="text-xs text-blue-600">Social Studies</p></div><span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">70<!-- -->% load</span></div><div class="text-xs text-gray-600"><span>14<!-- --> periods/week • Grades: <!-- -->9, 10</span></div><div class="w-full bg-gray-200 rounded-full h-1 mt-2"><div class="h-1 rounded-full bg-green-600" style="width:70%"></div></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Academic Calendar</h3><p class="text-sm text-muted-foreground">Important academic dates and events</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">Mid-term Examinations</h4><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Exam</span></div><p class="text-sm text-gray-600 mb-1">March 15-25, 2024</p><p class="text-xs text-gray-500 mb-2">All Grades</p><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Upcoming</span></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">Parent-Teacher Meeting</h4><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Meeting</span></div><p class="text-sm text-gray-600 mb-1">March 30, 2024</p><p class="text-xs text-gray-500 mb-2">All Grades</p><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Scheduled</span></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">Science Fair</h4><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Event</span></div><p class="text-sm text-gray-600 mb-1">April 10, 2024</p><p class="text-xs text-gray-500 mb-2">Grades 6-12</p><span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Planning</span></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">Annual Sports Day</h4><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Event</span></div><p class="text-sm text-gray-600 mb-1">April 20, 2024</p><p class="text-xs text-gray-500 mb-2">All Grades</p><span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Planning</span></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">Final Examinations</h4><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Exam</span></div><p class="text-sm text-gray-600 mb-1">May 1-15, 2024</p><p class="text-xs text-gray-500 mb-2">All Grades</p><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Scheduled</span></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">Summer Vacation</h4><span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">Holiday</span></div><p class="text-sm text-gray-600 mb-1">May 20 - June 15, 2024</p><p class="text-xs text-gray-500 mb-2">All Grades</p><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Scheduled</span></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[4394,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"2990\",\"static/chunks/app/dashboard/academic/schedule/page-53557b9d790c1b45.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"Tn7xWRdXVWNLs4FpbaknK\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/academic/schedule\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"academic\",{\"children\":[\"schedule\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"academic\",{\"children\":[\"schedule\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"academic\",\"children\",\"schedule\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"academic\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>