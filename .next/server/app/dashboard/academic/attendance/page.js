(()=>{var e={};e.id=6408,e.ids=[6408],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},76550:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>i});var a=t(50482),r=t(69108),n=t(62563),l=t.n(n),d=t(68300),c={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);t.d(s,c);let i=["",{children:["dashboard",{children:["academic",{children:["attendance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5146)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/attendance/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/attendance/page.tsx"],x="/dashboard/academic/attendance/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/academic/attendance/page",pathname:"/dashboard/academic/attendance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},63536:(e,s,t)=>{Promise.resolve().then(t.bind(t,76821))},76821:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(95344);t(3729);var r=t(18791);function n(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Attendance Management"}),a.jsx("p",{className:"text-muted-foreground",children:"Track and manage student attendance across all classes"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Attendance Report"}),a.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Mark Attendance"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(r.ll,{className:"text-sm font-medium",children:"Today's Attendance"}),a.jsx("span",{className:"text-2xl",children:"\uD83D\uDCC5"})]}),(0,a.jsxs)(r.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:"92%"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"456/495 present"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(r.ll,{className:"text-sm font-medium",children:"Monthly Average"}),a.jsx("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,a.jsxs)(r.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:"89%"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(r.ll,{className:"text-sm font-medium",children:"Chronic Absentees"}),a.jsx("span",{className:"text-2xl",children:"⚠️"})]}),(0,a.jsxs)(r.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:"12"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Below 75%"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(r.ll,{className:"text-sm font-medium",children:"Perfect Attendance"}),a.jsx("span",{className:"text-2xl",children:"\uD83C\uDFC6"})]}),(0,a.jsxs)(r.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:"23"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"100% this month"})]})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[a.jsx(r.ll,{children:"Mark Attendance - Grade 9A"}),a.jsx(r.SZ,{children:"Today's attendance for Mathematics - Period 1"})]}),a.jsx(r.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium",children:"Date"}),a.jsx("input",{type:"date",value:"2024-02-16",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium",children:"Subject"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[a.jsx("option",{value:"mathematics",children:"Mathematics"}),a.jsx("option",{value:"science",children:"Science"}),a.jsx("option",{value:"english",children:"English"}),a.jsx("option",{value:"social",children:"Social Studies"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium",children:"Period"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[a.jsx("option",{value:"1",children:"Period 1 (8:00-8:45)"}),a.jsx("option",{value:"2",children:"Period 2 (8:45-9:30)"}),a.jsx("option",{value:"3",children:"Period 3 (9:45-10:30)"}),a.jsx("option",{value:"4",children:"Period 4 (10:30-11:15)"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium",children:"Teacher"}),a.jsx("input",{type:"text",value:"Dr. Priya Patel",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",disabled:!0})]})]}),(0,a.jsxs)("div",{className:"flex space-x-4 mb-4",children:[a.jsx("button",{className:"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700",children:"Mark All Present"}),a.jsx("button",{className:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700",children:"Mark All Absent"}),a.jsx("button",{className:"border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50",children:"Copy from Yesterday"})]}),a.jsx("div",{className:"grid gap-2 md:grid-cols-2 lg:grid-cols-3",children:[{rollNo:"9A001",name:"Aarav Sharma",status:"present",lastAbsent:"2024-02-10"},{rollNo:"9A002",name:"Kavya Gupta",status:"present",lastAbsent:"2024-02-08"},{rollNo:"9A003",name:"Arjun Singh",status:"absent",lastAbsent:"2024-02-16"},{rollNo:"9A004",name:"Riya Patel",status:"present",lastAbsent:"2024-02-05"},{rollNo:"9A005",name:"Vikram Kumar",status:"late",lastAbsent:"2024-02-12"},{rollNo:"9A006",name:"Sneha Sharma",status:"present",lastAbsent:"2024-02-01"},{rollNo:"9A007",name:"Rohit Singh",status:"present",lastAbsent:"2024-02-03"},{rollNo:"9A008",name:"Priya Gupta",status:"absent",lastAbsent:"2024-02-16"},{rollNo:"9A009",name:"Amit Patel",status:"present",lastAbsent:"2024-01-28"}].map((e,s)=>(0,a.jsxs)("div",{className:`border rounded p-3 ${"absent"===e.status?"border-red-200 bg-red-50":"late"===e.status?"border-yellow-200 bg-yellow-50":"border-green-200 bg-green-50"}`,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium text-sm",children:e.name}),a.jsx("p",{className:"text-xs text-gray-600",children:e.rollNo})]}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[a.jsx("button",{className:`w-8 h-8 rounded text-xs font-medium ${"present"===e.status?"bg-green-600 text-white":"bg-gray-200 text-gray-600"}`,children:"P"}),a.jsx("button",{className:`w-8 h-8 rounded text-xs font-medium ${"absent"===e.status?"bg-red-600 text-white":"bg-gray-200 text-gray-600"}`,children:"A"}),a.jsx("button",{className:`w-8 h-8 rounded text-xs font-medium ${"late"===e.status?"bg-yellow-600 text-white":"bg-gray-200 text-gray-600"}`,children:"L"})]})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Last absent: ",e.lastAbsent]})]},s))}),(0,a.jsxs)("div",{className:"flex space-x-2 pt-4 border-t",children:[a.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Save Attendance"}),a.jsx("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Save as Draft"}),a.jsx("button",{className:"text-blue-600 hover:text-blue-800",children:"Send Notifications"})]})]})})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[a.jsx(r.ll,{children:"Class-wise Attendance"}),a.jsx(r.SZ,{children:"Today's attendance by class"})]}),a.jsx(r.aY,{children:a.jsx("div",{className:"space-y-3",children:[{class:"Grade 9A",present:28,total:30,percentage:93},{class:"Grade 9B",present:27,total:30,percentage:90},{class:"Grade 10A",present:29,total:32,percentage:91},{class:"Grade 10B",present:30,total:32,percentage:94},{class:"Grade 11A",present:25,total:28,percentage:89},{class:"Grade 12A",present:26,total:28,percentage:93}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between border rounded p-3",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium text-sm",children:e.class}),(0,a.jsxs)("p",{className:"text-xs text-gray-600",children:[e.present,"/",e.total," present"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:`text-lg font-bold ${e.percentage>=95?"text-green-600":e.percentage>=85?"text-blue-600":e.percentage>=75?"text-yellow-600":"text-red-600"}`,children:[e.percentage,"%"]}),a.jsx("div",{className:"w-20 bg-gray-200 rounded-full h-2 mt-1",children:a.jsx("div",{className:`h-2 rounded-full ${e.percentage>=95?"bg-green-600":e.percentage>=85?"bg-blue-600":e.percentage>=75?"bg-yellow-600":"bg-red-600"}`,style:{width:`${e.percentage}%`}})})]})]},s))})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[a.jsx(r.ll,{children:"Attendance Trends"}),a.jsx(r.SZ,{children:"Weekly attendance patterns"})]}),a.jsx(r.aY,{children:a.jsx("div",{className:"space-y-3",children:[{day:"Monday",percentage:94,trend:"+2%"},{day:"Tuesday",percentage:92,trend:"0%"},{day:"Wednesday",percentage:89,trend:"-3%"},{day:"Thursday",percentage:91,trend:"+2%"},{day:"Friday",percentage:87,trend:"-4%"},{day:"Saturday",percentage:85,trend:"-2%"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("span",{className:"text-sm font-medium w-20",children:e.day}),a.jsx("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.percentage}%`}})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"text-sm font-medium",children:[e.percentage,"%"]}),a.jsx("span",{className:`text-xs ${e.trend.startsWith("+")?"text-green-600":e.trend.startsWith("-")?"text-red-600":"text-gray-600"}`,children:e.trend})]})]},s))})})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[a.jsx(r.ll,{children:"Attendance Alerts"}),a.jsx(r.SZ,{children:"Students requiring attention"})]}),a.jsx(r.aY,{children:a.jsx("div",{className:"space-y-3",children:[{name:"Arjun Singh",rollNo:"9A003",class:"Grade 9A",attendance:68,daysAbsent:8,lastPresent:"2024-02-12"},{name:"Priya Gupta",rollNo:"9A008",class:"Grade 9A",attendance:72,daysAbsent:6,lastPresent:"2024-02-14"},{name:"Rohit Kumar",rollNo:"10B015",class:"Grade 10B",attendance:65,daysAbsent:9,lastPresent:"2024-02-10"},{name:"Sneha Patel",rollNo:"11A007",class:"Grade 11A",attendance:70,daysAbsent:7,lastPresent:"2024-02-13"}].map((e,s)=>a.jsx("div",{className:"border rounded-lg p-4 border-red-200 bg-red-50",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx("h4",{className:"font-semibold",children:e.name}),a.jsx("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e.class}),(0,a.jsxs)("span",{className:"px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full",children:[e.attendance,"% attendance"]})]}),a.jsx("p",{className:"text-sm text-gray-600",children:e.rollNo}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[a.jsx("div",{children:(0,a.jsxs)("p",{children:[a.jsx("span",{className:"font-medium",children:"Days Absent:"})," ",e.daysAbsent," this month"]})}),a.jsx("div",{children:(0,a.jsxs)("p",{children:[a.jsx("span",{className:"font-medium",children:"Last Present:"})," ",e.lastPresent]})})]})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[a.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"Contact Parent"}),a.jsx("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"View Details"})]})]})},s))})})]})]})}},18791:(e,s,t)=>{"use strict";t.d(s,{Zb:()=>c,aY:()=>m,SZ:()=>x,Ol:()=>i,ll:()=>o});var a=t(95344),r=t(3729),n=t(56815),l=t(79377);function d(...e){return(0,l.m6)((0,n.W)(e))}let c=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));c.displayName="Card";let i=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:d("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let o=r.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:d("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let x=r.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:d("text-sm text-muted-foreground",e),...s}));x.displayName="CardDescription";let m=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:d("p-6 pt-0",e),...s}));m.displayName="CardContent",r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:d("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},5146:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>r,default:()=>l});let a=(0,t(86843).createProxy)(String.raw`/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/attendance/page.tsx`),{__esModule:r,$$typeof:n}=a,l=a.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,3883,3945,1476,8510],()=>t(76550));module.exports=a})();