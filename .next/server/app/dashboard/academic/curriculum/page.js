(()=>{var e={};e.id=3887,e.ids=[3887],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},72238:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>d});var t=a(50482),r=a(69108),l=a(62563),i=a.n(l),c=a(68300),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);a.d(s,n);let d=["",{children:["dashboard",{children:["academic",{children:["curriculum",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,89242)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/curriculum/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94699)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,21342)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/curriculum/page.tsx"],m="/dashboard/academic/curriculum/page",x={require:a,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/academic/curriculum/page",pathname:"/dashboard/academic/curriculum",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},85870:(e,s,a)=>{Promise.resolve().then(a.bind(a,77462))},77462:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});var t=a(95344);a(3729);var r=a(18791);function l(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Curriculum Management"}),t.jsx("p",{className:"text-muted-foreground",children:"Manage course curriculum, syllabus, and learning objectives"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[t.jsx("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Import Curriculum"}),t.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Add Subject"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(r.ll,{className:"text-sm font-medium",children:"Total Subjects"}),t.jsx("span",{className:"text-2xl",children:"\uD83D\uDCDA"})]}),(0,t.jsxs)(r.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:"45"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Across all grades"})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(r.ll,{className:"text-sm font-medium",children:"Learning Objectives"}),t.jsx("span",{className:"text-2xl",children:"\uD83C\uDFAF"})]}),(0,t.jsxs)(r.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:"1,234"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Defined objectives"})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(r.ll,{className:"text-sm font-medium",children:"Curriculum Updates"}),t.jsx("span",{className:"text-2xl",children:"\uD83D\uDD04"})]}),(0,t.jsxs)(r.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:"12"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"This semester"})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(r.ll,{className:"text-sm font-medium",children:"Completion Rate"}),t.jsx("span",{className:"text-2xl",children:"\uD83D\uDCC8"})]}),(0,t.jsxs)(r.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:"87%"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Average progress"})]})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{children:[t.jsx(r.ll,{children:"Curriculum by Grade Level"}),t.jsx(r.SZ,{children:"Subject-wise curriculum overview"})]}),t.jsx(r.aY,{children:t.jsx("div",{className:"space-y-4",children:[{grade:"Grade 9",subjects:[{name:"Mathematics",chapters:15,completed:12,teacher:"Dr. Priya Patel"},{name:"Science",chapters:18,completed:14,teacher:"Mr. Rajesh Kumar"},{name:"English",chapters:12,completed:10,teacher:"Ms. Sneha Gupta"},{name:"Social Studies",chapters:16,completed:13,teacher:"Dr. Amit Verma"}]},{grade:"Grade 10",subjects:[{name:"Mathematics",chapters:16,completed:11,teacher:"Dr. Priya Patel"},{name:"Physics",chapters:14,completed:9,teacher:"Mr. Rajesh Kumar"},{name:"Chemistry",chapters:12,completed:8,teacher:"Dr. Kavya Singh"},{name:"Biology",chapters:15,completed:10,teacher:"Ms. Riya Sharma"}]}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded-lg p-4",children:[t.jsx("h3",{className:"text-lg font-semibold mb-3",children:e.grade}),t.jsx("div",{className:"grid gap-3 md:grid-cols-2",children:e.subjects.map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3 hover:shadow-md transition-shadow",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[t.jsx("h4",{className:"font-medium",children:e.name}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[e.completed,"/",e.chapters," chapters"]})]}),t.jsx("p",{className:"text-sm text-blue-600 mb-2",children:e.teacher}),t.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-2",children:t.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.completed/e.chapters*100}%`}})}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,t.jsxs)("span",{children:[Math.round(e.completed/e.chapters*100),"% complete"]}),(0,t.jsxs)("span",{children:[e.chapters-e.completed," remaining"]})]}),(0,t.jsxs)("div",{className:"flex space-x-2 mt-2",children:[t.jsx("button",{className:"bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700",children:"View Syllabus"}),t.jsx("button",{className:"border border-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-50",children:"Edit"})]})]},s))})]},s))})})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{children:[t.jsx(r.ll,{children:"Subject Curriculum Details"}),t.jsx(r.SZ,{children:"Detailed curriculum breakdown for Mathematics - Grade 9"})]}),t.jsx(r.aY,{children:t.jsx("div",{className:"space-y-4",children:[{unit:"Unit 1: Number Systems",chapters:["Real Numbers","Polynomials"],objectives:["Understand rational and irrational numbers","Perform operations on polynomials"],duration:"3 weeks",status:"Completed",assessments:2},{unit:"Unit 2: Algebra",chapters:["Linear Equations","Coordinate Geometry"],objectives:["Solve linear equations in two variables","Plot points on coordinate plane"],duration:"4 weeks",status:"In Progress",assessments:1},{unit:"Unit 3: Geometry",chapters:["Triangles","Quadrilaterals","Circles"],objectives:["Prove triangle congruence","Understand properties of quadrilaterals"],duration:"5 weeks",status:"Upcoming",assessments:3}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[t.jsx("h4",{className:"text-lg font-semibold",children:e.unit}),t.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${"Completed"===e.status?"bg-green-100 text-green-800":"In Progress"===e.status?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:e.status})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{children:[t.jsx("h5",{className:"font-medium mb-2",children:"Chapters"}),t.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:e.chapters.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[t.jsx("span",{className:"w-2 h-2 bg-blue-600 rounded-full"}),t.jsx("span",{children:e})]},s))})]}),(0,t.jsxs)("div",{children:[t.jsx("h5",{className:"font-medium mb-2",children:"Learning Objectives"}),t.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:e.objectives.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[t.jsx("span",{className:"w-2 h-2 bg-green-600 rounded-full"}),t.jsx("span",{children:e})]},s))})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between mt-4 pt-3 border-t",children:[(0,t.jsxs)("div",{className:"flex space-x-4 text-sm text-gray-600",children:[(0,t.jsxs)("span",{children:[t.jsx("span",{className:"font-medium",children:"Duration:"})," ",e.duration]}),(0,t.jsxs)("span",{children:[t.jsx("span",{className:"font-medium",children:"Assessments:"})," ",e.assessments]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[t.jsx("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"View Details"}),t.jsx("button",{className:"border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50",children:"Edit Unit"})]})]})]},s))})})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{children:[t.jsx(r.ll,{children:"Curriculum Tools"}),t.jsx(r.SZ,{children:"Manage curriculum resources and tools"})]}),t.jsx(r.aY,{children:t.jsx("div",{className:"space-y-3",children:[{tool:"Lesson Plan Generator",description:"Create structured lesson plans",icon:"\uD83D\uDCDD"},{tool:"Learning Objective Mapper",description:"Map objectives to assessments",icon:"\uD83C\uDFAF"},{tool:"Progress Tracker",description:"Track curriculum completion",icon:"\uD83D\uDCCA"},{tool:"Resource Library",description:"Access teaching resources",icon:"\uD83D\uDCDA"},{tool:"Standards Alignment",description:"Align with education standards",icon:"✅"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("span",{className:"text-2xl",children:e.icon}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium text-sm",children:e.tool}),t.jsx("p",{className:"text-xs text-gray-600",children:e.description})]})]}),t.jsx("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Use Tool"})]},s))})})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{children:[t.jsx(r.ll,{children:"Recent Updates"}),t.jsx(r.SZ,{children:"Latest curriculum changes and updates"})]}),t.jsx(r.aY,{children:t.jsx("div",{className:"space-y-3",children:[{subject:"Mathematics Grade 10",update:"Added new chapter on Statistics",date:"2024-02-15",teacher:"Dr. Priya Patel"},{subject:"Science Grade 9",update:"Updated lab experiments list",date:"2024-02-14",teacher:"Mr. Rajesh Kumar"},{subject:"English Grade 11",update:"Revised literature syllabus",date:"2024-02-13",teacher:"Ms. Sneha Gupta"},{subject:"Social Studies Grade 8",update:"Added current affairs section",date:"2024-02-12",teacher:"Dr. Amit Verma"}].map((e,s)=>t.jsx("div",{className:"border-b pb-2",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium text-sm",children:e.subject}),t.jsx("p",{className:"text-xs text-gray-600",children:e.update}),t.jsx("p",{className:"text-xs text-blue-600",children:e.teacher})]}),t.jsx("span",{className:"text-xs text-gray-500",children:e.date})]})},s))})})]})]})]})}},18791:(e,s,a)=>{"use strict";a.d(s,{Zb:()=>n,aY:()=>x,SZ:()=>m,Ol:()=>d,ll:()=>o});var t=a(95344),r=a(3729),l=a(56815),i=a(79377);function c(...e){return(0,i.m6)((0,l.W)(e))}let n=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:c("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));n.displayName="Card";let d=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:c("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let o=r.forwardRef(({className:e,...s},a)=>t.jsx("h3",{ref:a,className:c("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let m=r.forwardRef(({className:e,...s},a)=>t.jsx("p",{ref:a,className:c("text-sm text-muted-foreground",e),...s}));m.displayName="CardDescription";let x=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:c("p-6 pt-0",e),...s}));x.displayName="CardContent",r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:c("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},89242:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>i});let t=(0,a(86843).createProxy)(String.raw`/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/curriculum/page.tsx`),{__esModule:r,$$typeof:l}=t,i=t.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,3883,3945,1476,8510],()=>a(72238));module.exports=t})();