(()=>{var e={};e.id=2992,e.ids=[2992],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55823:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>i});var r=a(50482),t=a(69108),l=a(62563),n=a.n(l),d=a(68300),c={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);a.d(s,c);let i=["",{children:["dashboard",{children:["academic",{children:["grades",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,49509)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/grades/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94699)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,21342)),"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/grades/page.tsx"],x="/dashboard/academic/grades/page",m={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/dashboard/academic/grades/page",pathname:"/dashboard/academic/grades",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},98006:(e,s,a)=>{Promise.resolve().then(a.bind(a,59071))},59071:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});var r=a(95344);a(3729);var t=a(18791);function l(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Grade Management"}),r.jsx("p",{className:"text-muted-foreground",children:"Record, manage, and analyze student grades and academic performance"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Export Grades"}),r.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Enter Grades"})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(t.ll,{className:"text-sm font-medium",children:"Pending Grades"}),r.jsx("span",{className:"text-2xl",children:"\uD83D\uDCDD"})]}),(0,r.jsxs)(t.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:"156"}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Awaiting entry"})]})]}),(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(t.ll,{className:"text-sm font-medium",children:"Class Average"}),r.jsx("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,r.jsxs)(t.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:"78%"}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Overall performance"})]})]}),(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(t.ll,{className:"text-sm font-medium",children:"Top Performers"}),r.jsx("span",{className:"text-2xl",children:"\uD83C\uDFC6"})]}),(0,r.jsxs)(t.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:"23"}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Above 90%"})]})]}),(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(t.ll,{className:"text-sm font-medium",children:"Need Support"}),r.jsx("span",{className:"text-2xl",children:"⚠️"})]}),(0,r.jsxs)(t.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:"12"}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Below 60%"})]})]})]}),(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[r.jsx(t.ll,{children:"Grade Entry - Mathematics Grade 9A"}),r.jsx(t.SZ,{children:"Enter grades for Unit Test 2 - Polynomials"})]}),r.jsx(t.aY,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"text-sm font-medium",children:"Assessment"}),(0,r.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[r.jsx("option",{value:"",children:"Select Assessment"}),r.jsx("option",{value:"unit-test-2",children:"Unit Test 2 - Polynomials"}),r.jsx("option",{value:"mid-term",children:"Mid-term Examination"}),r.jsx("option",{value:"assignment-1",children:"Assignment 1"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"text-sm font-medium",children:"Total Marks"}),r.jsx("input",{type:"number",value:"100",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"text-sm font-medium",children:"Date"}),r.jsx("input",{type:"date",value:"2024-02-15",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"text-sm font-medium",children:"Weightage"}),r.jsx("input",{type:"number",value:"20",placeholder:"20%",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]})]}),r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full border-collapse",children:[r.jsx("thead",{children:(0,r.jsxs)("tr",{className:"border-b",children:[r.jsx("th",{className:"text-left p-2 font-medium",children:"Roll No."}),r.jsx("th",{className:"text-left p-2 font-medium",children:"Student Name"}),r.jsx("th",{className:"text-left p-2 font-medium",children:"Marks Obtained"}),r.jsx("th",{className:"text-left p-2 font-medium",children:"Percentage"}),r.jsx("th",{className:"text-left p-2 font-medium",children:"Grade"}),r.jsx("th",{className:"text-left p-2 font-medium",children:"Remarks"})]})}),r.jsx("tbody",{children:[{rollNo:"9A001",name:"Aarav Sharma",marks:85,percentage:85,grade:"A",remarks:"Excellent"},{rollNo:"9A002",name:"Kavya Gupta",marks:78,percentage:78,grade:"B+",remarks:"Good"},{rollNo:"9A003",name:"Arjun Singh",marks:92,percentage:92,grade:"A+",remarks:"Outstanding"},{rollNo:"9A004",name:"Riya Patel",marks:65,percentage:65,grade:"B",remarks:"Satisfactory"},{rollNo:"9A005",name:"Vikram Kumar",marks:58,percentage:58,grade:"C+",remarks:"Needs improvement"}].map((e,s)=>(0,r.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[r.jsx("td",{className:"p-2 font-medium",children:e.rollNo}),r.jsx("td",{className:"p-2",children:e.name}),r.jsx("td",{className:"p-2",children:r.jsx("input",{type:"number",value:e.marks,className:"w-20 px-2 py-1 border border-gray-300 rounded text-sm",max:"100",min:"0"})}),r.jsx("td",{className:"p-2",children:(0,r.jsxs)("span",{className:`font-medium ${e.percentage>=90?"text-green-600":e.percentage>=75?"text-blue-600":e.percentage>=60?"text-yellow-600":"text-red-600"}`,children:[e.percentage,"%"]})}),r.jsx("td",{className:"p-2",children:r.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${"A+"===e.grade?"bg-green-100 text-green-800":"A"===e.grade?"bg-blue-100 text-blue-800":e.grade.startsWith("B")?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:e.grade})}),r.jsx("td",{className:"p-2",children:r.jsx("input",{type:"text",value:e.remarks,className:"w-32 px-2 py-1 border border-gray-300 rounded text-sm",placeholder:"Remarks"})})]},s))})]})}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Save Grades"}),r.jsx("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Save as Draft"}),r.jsx("button",{className:"text-blue-600 hover:text-blue-800",children:"Auto Calculate"})]})]})})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[r.jsx(t.ll,{children:"Grade Distribution"}),r.jsx(t.SZ,{children:"Performance distribution across grades"})]}),r.jsx(t.aY,{children:r.jsx("div",{className:"space-y-3",children:[{grade:"A+ (90-100%)",count:23,percentage:19,color:"bg-green-600"},{grade:"A (80-89%)",count:45,percentage:37,color:"bg-blue-600"},{grade:"B+ (70-79%)",count:32,percentage:26,color:"bg-yellow-600"},{grade:"B (60-69%)",count:15,percentage:12,color:"bg-orange-600"},{grade:"C+ (50-59%)",count:6,percentage:5,color:"bg-red-600"},{grade:"Below 50%",count:1,percentage:1,color:"bg-gray-600"}].map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:`w-4 h-4 rounded ${e.color}`}),r.jsx("span",{className:"text-sm font-medium",children:e.grade})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.count," students"]}),(0,r.jsxs)("span",{className:"text-sm font-medium",children:[e.percentage,"%"]})]})]},s))})})]}),(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[r.jsx(t.ll,{children:"Subject Performance"}),r.jsx(t.SZ,{children:"Average performance by subject"})]}),r.jsx(t.aY,{children:r.jsx("div",{className:"space-y-3",children:[{subject:"Mathematics",average:78,trend:"+2%",students:120},{subject:"Science",average:82,trend:"+5%",students:118},{subject:"English",average:75,trend:"-1%",students:122},{subject:"Social Studies",average:80,trend:"+3%",students:119},{subject:"Hindi",average:77,trend:"0%",students:121}].map((e,s)=>(0,r.jsxs)("div",{className:"border rounded p-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[r.jsx("h4",{className:"font-medium text-sm",children:e.subject}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-sm font-medium",children:[e.average,"%"]}),r.jsx("span",{className:`text-xs ${e.trend.startsWith("+")?"text-green-600":e.trend.startsWith("-")?"text-red-600":"text-gray-600"}`,children:e.trend})]})]}),r.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-1",children:r.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.average}%`}})}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[e.students," students"]})]},s))})})]})]}),(0,r.jsxs)(t.Zb,{children:[(0,r.jsxs)(t.Ol,{children:[r.jsx(t.ll,{children:"Individual Student Performance"}),r.jsx(t.SZ,{children:"Detailed performance tracking for Grade 9A"})]}),r.jsx(t.aY,{children:r.jsx("div",{className:"space-y-4",children:[{name:"Aarav Sharma",rollNo:"9A001",overall:85,subjects:{mathematics:85,science:88,english:82,social:87,hindi:83},trend:"Improving",rank:3},{name:"Kavya Gupta",rollNo:"9A002",overall:78,subjects:{mathematics:78,science:80,english:75,social:79,hindi:78},trend:"Stable",rank:8},{name:"Arjun Singh",rollNo:"9A003",overall:92,subjects:{mathematics:92,science:94,english:89,social:93,hindi:90},trend:"Excellent",rank:1}].map((e,s)=>(0,r.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.rollNo," • Rank: ",e.rank]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"text-lg font-bold text-blue-600",children:[e.overall,"%"]}),r.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${"Excellent"===e.trend?"bg-green-100 text-green-800":"Improving"===e.trend?"bg-blue-100 text-blue-800":"Stable"===e.trend?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:e.trend})]})]}),r.jsx("div",{className:"grid grid-cols-5 gap-2 text-sm",children:Object.entries(e.subjects).map(([e,s])=>(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("p",{className:"text-xs text-gray-600 capitalize",children:e}),(0,r.jsxs)("p",{className:`font-medium ${s>=90?"text-green-600":s>=80?"text-blue-600":s>=70?"text-yellow-600":"text-red-600"}`,children:[s,"%"]})]},e))})]},s))})})]})]})}},18791:(e,s,a)=>{"use strict";a.d(s,{Zb:()=>c,aY:()=>m,SZ:()=>x,Ol:()=>i,ll:()=>o});var r=a(95344),t=a(3729),l=a(56815),n=a(79377);function d(...e){return(0,n.m6)((0,l.W)(e))}let c=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));c.displayName="Card";let i=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:d("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let o=t.forwardRef(({className:e,...s},a)=>r.jsx("h3",{ref:a,className:d("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let x=t.forwardRef(({className:e,...s},a)=>r.jsx("p",{ref:a,className:d("text-sm text-muted-foreground",e),...s}));x.displayName="CardDescription";let m=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:d("p-6 pt-0",e),...s}));m.displayName="CardContent",t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:d("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},49509:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>l,__esModule:()=>t,default:()=>n});let r=(0,a(86843).createProxy)(String.raw`/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/dashboard/academic/grades/page.tsx`),{__esModule:t,$$typeof:l}=r,n=r.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[1638,3883,3945,1476,8510],()=>a(55823));module.exports=r})();