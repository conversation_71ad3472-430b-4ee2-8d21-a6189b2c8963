<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/library/digital/page-d6b93e28b1eb0780.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Digital Library</h1><p class="text-muted-foreground">Access e-books, digital resources, and online databases</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Usage Report</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Add Resource</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">E-Books</h3><span class="text-2xl">📱</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">2,456</div><p class="text-xs text-muted-foreground">Digital books</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Online Databases</h3><span class="text-2xl">🗄️</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">15</div><p class="text-xs text-muted-foreground">Active subscriptions</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Digital Downloads</h3><span class="text-2xl">⬇️</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">1,234</div><p class="text-xs text-muted-foreground">This month</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Active Users</h3><span class="text-2xl">👥</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">456</div><p class="text-xs text-muted-foreground">Monthly active</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Quick Access</h3><p class="text-sm text-muted-foreground">Popular digital resources and databases</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"><div class="flex items-start justify-between mb-3"><div class="flex items-center space-x-2"><span class="text-2xl">🎓</span><div><h3 class="font-semibold">JSTOR Academic</h3><p class="text-sm text-blue-600">Research Database</p></div></div><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Active</span></div><p class="text-sm text-gray-600 mb-3">Academic journals and research papers</p><div class="flex items-center justify-between"><span class="text-sm text-gray-500">234<!-- --> users</span><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Access</button></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"><div class="flex items-start justify-between mb-3"><div class="flex items-center space-x-2"><span class="text-2xl">⚡</span><div><h3 class="font-semibold">IEEE Xplore</h3><p class="text-sm text-blue-600">Technical Database</p></div></div><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Active</span></div><p class="text-sm text-gray-600 mb-3">Engineering and technology papers</p><div class="flex items-center justify-between"><span class="text-sm text-gray-500">156<!-- --> users</span><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Access</button></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"><div class="flex items-start justify-between mb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📚</span><div><h3 class="font-semibold">Oxford Reference</h3><p class="text-sm text-blue-600">Reference Database</p></div></div><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Active</span></div><p class="text-sm text-gray-600 mb-3">Dictionaries and reference materials</p><div class="flex items-center justify-between"><span class="text-sm text-gray-500">89<!-- --> users</span><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Access</button></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"><div class="flex items-start justify-between mb-3"><div class="flex items-center space-x-2"><span class="text-2xl">🌍</span><div><h3 class="font-semibold">Britannica Online</h3><p class="text-sm text-blue-600">Encyclopedia</p></div></div><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Active</span></div><p class="text-sm text-gray-600 mb-3">Comprehensive encyclopedia access</p><div class="flex items-center justify-between"><span class="text-sm text-gray-500">345<!-- --> users</span><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Access</button></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"><div class="flex items-start justify-between mb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📖</span><div><h3 class="font-semibold">Project Gutenberg</h3><p class="text-sm text-blue-600">E-Book Collection</p></div></div><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Active</span></div><p class="text-sm text-gray-600 mb-3">Free classic literature e-books</p><div class="flex items-center justify-between"><span class="text-sm text-gray-500">567<!-- --> users</span><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Access</button></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"><div class="flex items-start justify-between mb-3"><div class="flex items-center space-x-2"><span class="text-2xl">🎯</span><div><h3 class="font-semibold">Khan Academy</h3><p class="text-sm text-blue-600">Educational Platform</p></div></div><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Active</span></div><p class="text-sm text-gray-600 mb-3">Interactive learning resources</p><div class="flex items-center justify-between"><span class="text-sm text-gray-500">789<!-- --> users</span><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Access</button></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">E-Books Collection</h3><p class="text-sm text-muted-foreground">Browse and access digital books</p></div><div class="p-6 pt-0"><div class="mb-4 flex space-x-4"><input type="text" placeholder="Search e-books..." class="flex-1 px-3 py-2 border border-gray-300 rounded-md"/><select class="px-3 py-2 border border-gray-300 rounded-md"><option value="">All Categories</option><option value="textbooks">Textbooks</option><option value="fiction">Fiction</option><option value="science">Science</option><option value="literature">Literature</option></select><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Search</button></div><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex items-start space-x-4"><div class="w-16 h-20 bg-gray-100 rounded flex items-center justify-center text-2xl">💻</div><div class="flex-1"><h3 class="text-lg font-semibold">Introduction to Computer Science</h3><p class="text-blue-600 font-medium">by <!-- -->Dr. John Smith</p><div class="flex items-center space-x-4 mt-2 text-sm text-gray-600"><span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Textbook</span><span>📄 <!-- -->PDF</span><span>💾 <!-- -->15.2 MB</span><span>⬇️ <!-- -->234<!-- --> downloads</span></div><div class="flex items-center mt-2"><div class="flex items-center"><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-gray-300">⭐</span><span class="ml-2 text-sm text-gray-600">(<!-- -->4.5<!-- -->)</span></div></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm">Download</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Preview</button><button class="text-blue-600 hover:text-blue-800 text-sm">Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex items-start space-x-4"><div class="w-16 h-20 bg-gray-100 rounded flex items-center justify-center text-2xl">📐</div><div class="flex-1"><h3 class="text-lg font-semibold">Advanced Mathematics</h3><p class="text-blue-600 font-medium">by <!-- -->Prof. Sarah Johnson</p><div class="flex items-center space-x-4 mt-2 text-sm text-gray-600"><span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Textbook</span><span>📄 <!-- -->EPUB</span><span>💾 <!-- -->8.7 MB</span><span>⬇️ <!-- -->189<!-- --> downloads</span></div><div class="flex items-center mt-2"><div class="flex items-center"><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-gray-300">⭐</span><span class="ml-2 text-sm text-gray-600">(<!-- -->4.3<!-- -->)</span></div></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm">Download</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Preview</button><button class="text-blue-600 hover:text-blue-800 text-sm">Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex items-start space-x-4"><div class="w-16 h-20 bg-gray-100 rounded flex items-center justify-center text-2xl">🏛️</div><div class="flex-1"><h3 class="text-lg font-semibold">World History Chronicles</h3><p class="text-blue-600 font-medium">by <!-- -->Dr. Michael Brown</p><div class="flex items-center space-x-4 mt-2 text-sm text-gray-600"><span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Reference</span><span>📄 <!-- -->PDF</span><span>💾 <!-- -->22.1 MB</span><span>⬇️ <!-- -->156<!-- --> downloads</span></div><div class="flex items-center mt-2"><div class="flex items-center"><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-gray-300">⭐</span><span class="ml-2 text-sm text-gray-600">(<!-- -->4.7<!-- -->)</span></div></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm">Download</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Preview</button><button class="text-blue-600 hover:text-blue-800 text-sm">Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex items-start space-x-4"><div class="w-16 h-20 bg-gray-100 rounded flex items-center justify-center text-2xl">⚛️</div><div class="flex-1"><h3 class="text-lg font-semibold">Physics Fundamentals</h3><p class="text-blue-600 font-medium">by <!-- -->Dr. Emily Davis</p><div class="flex items-center space-x-4 mt-2 text-sm text-gray-600"><span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Textbook</span><span>📄 <!-- -->PDF</span><span>💾 <!-- -->18.5 MB</span><span>⬇️ <!-- -->298<!-- --> downloads</span></div><div class="flex items-center mt-2"><div class="flex items-center"><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-yellow-400">⭐</span><span class="text-sm text-gray-300">⭐</span><span class="ml-2 text-sm text-gray-600">(<!-- -->4.6<!-- -->)</span></div></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-gray-400 text-white px-4 py-2 rounded-md cursor-not-allowed text-sm">Unavailable</button><button class="text-blue-600 hover:text-blue-800 text-sm">Details</button></div></div></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Popular Downloads</h3><p class="text-sm text-muted-foreground">Most downloaded resources this month</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Introduction to Computer Science</p><p class="text-xs text-gray-500">Textbook</p></div><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">234<!-- --> downloads</span></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Physics Fundamentals</p><p class="text-xs text-gray-500">Textbook</p></div><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">198<!-- --> downloads</span></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Advanced Mathematics</p><p class="text-xs text-gray-500">Textbook</p></div><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">189<!-- --> downloads</span></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">World History Chronicles</p><p class="text-xs text-gray-500">Reference</p></div><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">156<!-- --> downloads</span></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Chemistry Lab Manual</p><p class="text-xs text-gray-500">Textbook</p></div><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">134<!-- --> downloads</span></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Database Usage</h3><p class="text-sm text-muted-foreground">Access statistics for online databases</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Khan Academy</p><p class="text-xs text-gray-500">1234<!-- --> hours used</p></div><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">789<!-- --> sessions</span></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Project Gutenberg</p><p class="text-xs text-gray-500">890<!-- --> hours used</p></div><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">567<!-- --> sessions</span></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Britannica Online</p><p class="text-xs text-gray-500">567<!-- --> hours used</p></div><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">345<!-- --> sessions</span></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">JSTOR Academic</p><p class="text-xs text-gray-500">456<!-- --> hours used</p></div><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">234<!-- --> sessions</span></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">IEEE Xplore</p><p class="text-xs text-gray-500">234<!-- --> hours used</p></div><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">156<!-- --> sessions</span></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Digital Library Features</h3><p class="text-sm text-muted-foreground">Available tools and services</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"><div class="text-3xl mb-2">📝</div><h3 class="font-semibold mb-1">Citation Generator</h3><p class="text-sm text-gray-600 mb-3">Generate academic citations</p><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div><div class="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"><div class="text-3xl mb-2">🤖</div><h3 class="font-semibold mb-1">Research Assistant</h3><p class="text-sm text-gray-600 mb-3">AI-powered research help</p><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Get Help</button></div><div class="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"><div class="text-3xl mb-2">📷</div><h3 class="font-semibold mb-1">Document Scanner</h3><p class="text-sm text-gray-600 mb-3">Scan and digitize documents</p><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Scan Now</button></div><div class="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"><div class="text-3xl mb-2">📋</div><h3 class="font-semibold mb-1">Reading List</h3><p class="text-sm text-gray-600 mb-3">Manage your reading list</p><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View List</button></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[2640,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"53\",\"static/chunks/app/dashboard/library/digital/page-d6b93e28b1eb0780.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"az92YWeqjFDK2ONnGo-1U\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/library/digital\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"library\",{\"children\":[\"digital\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"library\",{\"children\":[\"digital\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"library\",\"children\",\"digital\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"library\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>