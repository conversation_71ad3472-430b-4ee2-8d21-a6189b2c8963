<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/financials/fee-structure/page-63e0f857ed87b75c.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Fee Structure Management</h1><p class="text-muted-foreground">Configure and manage fee structures for different grades and categories</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Import Structure</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Add Fee Category</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Fee Categories</h3><span class="text-2xl">📋</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">12</div><p class="text-xs text-muted-foreground">Active categories</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Total Annual Fees</h3><span class="text-2xl">💰</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">₹2.4M</div><p class="text-xs text-muted-foreground">Per student average</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Grade Levels</h3><span class="text-2xl">🎓</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">8</div><p class="text-xs text-muted-foreground">Different structures</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Last Updated</h3><span class="text-2xl">🔄</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">15</div><p class="text-xs text-muted-foreground">Days ago</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Fee Structure by Grade Level</h3><p class="text-sm text-muted-foreground">Annual fee breakdown for academic year 2024-25</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold">Nursery - Grade 2</h3><div class="text-right"><div class="text-xl font-bold text-blue-600">₹<!-- -->76,000</div><p class="text-sm text-gray-600">Total Annual Fee</p></div></div><div class="grid gap-3 md:grid-cols-3 lg:grid-cols-4"><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-green-600">₹<!-- -->45,000</div><p class="text-sm text-gray-600">Tuition Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-blue-600">₹<!-- -->5,000</div><p class="text-sm text-gray-600">Admission Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-purple-600">₹<!-- -->8,000</div><p class="text-sm text-gray-600">Development Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-orange-600">₹<!-- -->2,000</div><p class="text-sm text-gray-600">Exam Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-red-600">₹<!-- -->1,500</div><p class="text-sm text-gray-600">Library Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-yellow-600">₹<!-- -->2,500</div><p class="text-sm text-gray-600">Sports Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-indigo-600">₹<!-- -->12,000</div><p class="text-sm text-gray-600">Transport Fee</p><p class="text-xs text-gray-500">(Optional)</p></div></div><div class="flex space-x-2 mt-4"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Edit Structure</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">View Details</button><button class="text-green-600 hover:text-green-800 text-sm">Apply to Students</button></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold">Grade 3 - Grade 5</h3><div class="text-right"><div class="text-xl font-bold text-blue-600">₹<!-- -->89,500</div><p class="text-sm text-gray-600">Total Annual Fee</p></div></div><div class="grid gap-3 md:grid-cols-3 lg:grid-cols-4"><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-green-600">₹<!-- -->55,000</div><p class="text-sm text-gray-600">Tuition Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-blue-600">₹<!-- -->5,000</div><p class="text-sm text-gray-600">Admission Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-purple-600">₹<!-- -->10,000</div><p class="text-sm text-gray-600">Development Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-orange-600">₹<!-- -->2,500</div><p class="text-sm text-gray-600">Exam Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-red-600">₹<!-- -->2,000</div><p class="text-sm text-gray-600">Library Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-yellow-600">₹<!-- -->3,000</div><p class="text-sm text-gray-600">Sports Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-indigo-600">₹<!-- -->12,000</div><p class="text-sm text-gray-600">Transport Fee</p><p class="text-xs text-gray-500">(Optional)</p></div></div><div class="flex space-x-2 mt-4"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Edit Structure</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">View Details</button><button class="text-green-600 hover:text-green-800 text-sm">Apply to Students</button></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold">Grade 6 - Grade 8</h3><div class="text-right"><div class="text-xl font-bold text-blue-600">₹<!-- -->103,000</div><p class="text-sm text-gray-600">Total Annual Fee</p></div></div><div class="grid gap-3 md:grid-cols-3 lg:grid-cols-4"><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-green-600">₹<!-- -->65,000</div><p class="text-sm text-gray-600">Tuition Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-blue-600">₹<!-- -->5,000</div><p class="text-sm text-gray-600">Admission Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-purple-600">₹<!-- -->12,000</div><p class="text-sm text-gray-600">Development Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-orange-600">₹<!-- -->3,000</div><p class="text-sm text-gray-600">Exam Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-red-600">₹<!-- -->2,500</div><p class="text-sm text-gray-600">Library Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-yellow-600">₹<!-- -->3,500</div><p class="text-sm text-gray-600">Sports Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-indigo-600">₹<!-- -->12,000</div><p class="text-sm text-gray-600">Transport Fee</p><p class="text-xs text-gray-500">(Optional)</p></div></div><div class="flex space-x-2 mt-4"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Edit Structure</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">View Details</button><button class="text-green-600 hover:text-green-800 text-sm">Apply to Students</button></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold">Grade 9 - Grade 10</h3><div class="text-right"><div class="text-xl font-bold text-blue-600">₹<!-- -->118,000</div><p class="text-sm text-gray-600">Total Annual Fee</p></div></div><div class="grid gap-3 md:grid-cols-3 lg:grid-cols-4"><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-green-600">₹<!-- -->75,000</div><p class="text-sm text-gray-600">Tuition Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-blue-600">₹<!-- -->5,000</div><p class="text-sm text-gray-600">Admission Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-purple-600">₹<!-- -->15,000</div><p class="text-sm text-gray-600">Development Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-orange-600">₹<!-- -->4,000</div><p class="text-sm text-gray-600">Exam Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-red-600">₹<!-- -->3,000</div><p class="text-sm text-gray-600">Library Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-yellow-600">₹<!-- -->4,000</div><p class="text-sm text-gray-600">Sports Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-indigo-600">₹<!-- -->12,000</div><p class="text-sm text-gray-600">Transport Fee</p><p class="text-xs text-gray-500">(Optional)</p></div></div><div class="flex space-x-2 mt-4"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Edit Structure</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">View Details</button><button class="text-green-600 hover:text-green-800 text-sm">Apply to Students</button></div></div><div class="border rounded-lg p-4"><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold">Grade 11 - Grade 12</h3><div class="text-right"><div class="text-xl font-bold text-blue-600">₹<!-- -->133,000</div><p class="text-sm text-gray-600">Total Annual Fee</p></div></div><div class="grid gap-3 md:grid-cols-3 lg:grid-cols-4"><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-green-600">₹<!-- -->85,000</div><p class="text-sm text-gray-600">Tuition Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-blue-600">₹<!-- -->5,000</div><p class="text-sm text-gray-600">Admission Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-purple-600">₹<!-- -->18,000</div><p class="text-sm text-gray-600">Development Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-orange-600">₹<!-- -->5,000</div><p class="text-sm text-gray-600">Exam Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-red-600">₹<!-- -->3,500</div><p class="text-sm text-gray-600">Library Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-yellow-600">₹<!-- -->4,500</div><p class="text-sm text-gray-600">Sports Fee</p></div><div class="text-center p-3 border rounded"><div class="text-lg font-semibold text-indigo-600">₹<!-- -->12,000</div><p class="text-sm text-gray-600">Transport Fee</p><p class="text-xs text-gray-500">(Optional)</p></div></div><div class="flex space-x-2 mt-4"><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Edit Structure</button><button class="border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50">View Details</button><button class="text-green-600 hover:text-green-800 text-sm">Apply to Students</button></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Fee Categories</h3><p class="text-sm text-muted-foreground">Manage individual fee categories and their configurations</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Tuition Fee</h3><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Mandatory</span><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Taxable</span></div><p class="text-gray-600 mb-2">Core academic instruction fee</p><div class="grid grid-cols-2 gap-4 text-sm text-gray-600"><div><p><span class="font-medium">Frequency:</span> <!-- -->Annual</p><p><span class="font-medium">Applicable to:</span> <!-- -->All Grades</p></div><div><p><span class="font-medium">Amount Range:</span> <!-- -->₹45,000 - ₹85,000</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Edit Category</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">View Usage</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Development Fee</h3><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Mandatory</span></div><p class="text-gray-600 mb-2">Infrastructure and facility development</p><div class="grid grid-cols-2 gap-4 text-sm text-gray-600"><div><p><span class="font-medium">Frequency:</span> <!-- -->Annual</p><p><span class="font-medium">Applicable to:</span> <!-- -->All Grades</p></div><div><p><span class="font-medium">Amount Range:</span> <!-- -->₹8,000 - ₹18,000</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Edit Category</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">View Usage</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Transport Fee</h3><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Optional</span><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Taxable</span></div><p class="text-gray-600 mb-2">School bus transportation service</p><div class="grid grid-cols-2 gap-4 text-sm text-gray-600"><div><p><span class="font-medium">Frequency:</span> <!-- -->Annual</p><p><span class="font-medium">Applicable to:</span> <!-- -->All Grades</p></div><div><p><span class="font-medium">Amount Range:</span> <!-- -->₹12,000</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Edit Category</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">View Usage</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Late Fee</h3><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Penalty</span></div><p class="text-gray-600 mb-2">Penalty for delayed fee payment</p><div class="grid grid-cols-2 gap-4 text-sm text-gray-600"><div><p><span class="font-medium">Frequency:</span> <!-- -->As applicable</p><p><span class="font-medium">Applicable to:</span> <!-- -->All Grades</p></div><div><p><span class="font-medium">Amount Range:</span> <!-- -->₹500 per month</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">Edit Category</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">View Usage</button></div></div></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Fee Structure Tools</h3><p class="text-sm text-muted-foreground">Tools for managing fee structures</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">🔄</span><div><p class="font-medium text-sm">Bulk Fee Update</p><p class="text-xs text-gray-600">Update fees across multiple grades</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">🧮</span><div><p class="font-medium text-sm">Fee Calculator</p><p class="text-xs text-gray-600">Calculate total fees for students</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">💸</span><div><p class="font-medium text-sm">Discount Manager</p><p class="text-xs text-gray-600">Manage fee discounts and scholarships</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">📊</span><div><p class="font-medium text-sm">Fee Comparison</p><p class="text-xs text-gray-600">Compare fees across academic years</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">✅</span><div><p class="font-medium text-sm">Structure Validator</p><p class="text-xs text-gray-600">Validate fee structure consistency</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Fee Structure History</h3><p class="text-sm text-muted-foreground">Recent changes to fee structures</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="border-b pb-2"><div class="flex items-start justify-between"><div><p class="font-medium text-sm">Updated Grade 11-12 tuition fee</p><p class="text-xs text-gray-600">Finance Admin<!-- --> • <!-- -->2024-02-01</p></div><span class="text-sm font-medium text-green-600">₹85,000</span></div></div><div class="border-b pb-2"><div class="flex items-start justify-between"><div><p class="font-medium text-sm">Added new sports fee category</p><p class="text-xs text-gray-600">Principal<!-- --> • <!-- -->2024-01-28</p></div><span class="text-sm font-medium text-green-600">₹4,500</span></div></div><div class="border-b pb-2"><div class="flex items-start justify-between"><div><p class="font-medium text-sm">Modified transport fee structure</p><p class="text-xs text-gray-600">Finance Admin<!-- --> • <!-- -->2024-01-25</p></div><span class="text-sm font-medium text-green-600">₹12,000</span></div></div><div class="border-b pb-2"><div class="flex items-start justify-between"><div><p class="font-medium text-sm">Updated development fee for Grade 9-10</p><p class="text-xs text-gray-600">Finance Admin<!-- --> • <!-- -->2024-01-20</p></div><span class="text-sm font-medium text-green-600">₹15,000</span></div></div></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[5267,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"9396\",\"static/chunks/app/dashboard/financials/fee-structure/page-63e0f857ed87b75c.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"az92YWeqjFDK2ONnGo-1U\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/financials/fee-structure\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"financials\",{\"children\":[\"fee-structure\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"financials\",{\"children\":[\"fee-structure\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"financials\",\"children\",\"fee-structure\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"financials\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>