<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/financials/history/page-3a21f55123c2751b.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Payment History</h1><p class="text-muted-foreground">Complete payment transaction history and audit trail</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Export History</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Advanced Search</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Total Transactions</h3><span class="text-2xl">📊</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">2,456</div><p class="text-xs text-muted-foreground">This academic year</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Total Amount</h3><span class="text-2xl">💰</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">₹39.2M</div><p class="text-xs text-muted-foreground">Collected to date</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">This Month</h3><span class="text-2xl">📅</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">₹5.9M</div><p class="text-xs text-muted-foreground">234 transactions</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Average Transaction</h3><span class="text-2xl">📈</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">₹15,960</div><p class="text-xs text-muted-foreground">Per transaction</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Search Payment History</h3><p class="text-sm text-muted-foreground">Filter and search through payment transactions</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-4"><div><label class="text-sm font-medium">Search</label><input type="text" placeholder="Student name, receipt no..." class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"/></div><div><label class="text-sm font-medium">Date Range</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">All Dates</option><option value="today">Today</option><option value="this-week">This Week</option><option value="this-month">This Month</option><option value="last-month">Last Month</option><option value="custom">Custom Range</option></select></div><div><label class="text-sm font-medium">Payment Method</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">All Methods</option><option value="cash">Cash</option><option value="cheque">Cheque</option><option value="online">Online Banking</option><option value="card">Credit/Debit Card</option><option value="upi">UPI</option></select></div><div><label class="text-sm font-medium">Amount Range</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">All Amounts</option><option value="0-10000">₹0 - ₹10,000</option><option value="10000-25000">₹10,000 - ₹25,000</option><option value="25000-50000">₹25,000 - ₹50,000</option><option value="50000+">₹50,000+</option></select></div></div><div class="mt-4 flex space-x-2"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Search</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Clear Filters</button><button class="text-blue-600 hover:text-blue-800">Export Results</button></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Payment Transactions</h3><p class="text-sm text-muted-foreground">Complete payment history with transaction details</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Aarav Sharma</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Confirmed</span></div><p class="text-blue-600 font-medium">RCP-2024-001234</p><p class="text-gray-600 text-sm">9A001<!-- --> • Bill: <!-- -->BILL-2024-001234</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->29,500</p><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-16<!-- --> at <!-- -->10:30 AM</p><p><span class="font-medium">💳 Method:</span> <!-- -->Online Banking</p></div><div><p><span class="font-medium">🔢 Transaction ID:</span> <!-- -->TXN789456123</p><p><span class="font-medium">👤 Processed By:</span> <!-- -->Finance Admin</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Receipt</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Print Receipt</button><button class="text-blue-600 hover:text-blue-800 text-sm">Transaction Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Kavya Gupta</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Confirmed</span></div><p class="text-blue-600 font-medium">RCP-2024-001235</p><p class="text-gray-600 text-sm">9A002<!-- --> • Bill: <!-- -->BILL-2024-001235</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->41,500</p><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-16<!-- --> at <!-- -->11:15 AM</p><p><span class="font-medium">💳 Method:</span> <!-- -->Credit Card</p></div><div><p><span class="font-medium">🔢 Transaction ID:</span> <!-- -->TXN789456124</p><p><span class="font-medium">👤 Processed By:</span> <!-- -->System Auto</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Receipt</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Print Receipt</button><button class="text-blue-600 hover:text-blue-800 text-sm">Transaction Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Riya Patel</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 6</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Confirmed</span></div><p class="text-blue-600 font-medium">RCP-2024-001236</p><p class="text-gray-600 text-sm">6B004<!-- --> • Bill: <!-- -->BILL-2024-001237</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->25,750</p><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-15<!-- --> at <!-- -->02:45 PM</p><p><span class="font-medium">💳 Method:</span> <!-- -->Cash</p></div><div><p><span class="font-medium">🔢 Transaction ID:</span> <!-- -->CASH-001236</p><p><span class="font-medium">👤 Processed By:</span> <!-- -->Cashier</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Receipt</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Print Receipt</button><button class="text-blue-600 hover:text-blue-800 text-sm">Transaction Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Vikram Singh</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 11</span><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Cleared</span></div><p class="text-blue-600 font-medium">RCP-2024-001237</p><p class="text-gray-600 text-sm">11A005<!-- --> • Bill: <!-- -->BILL-2024-001238</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->33,250</p><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-15<!-- --> at <!-- -->09:20 AM</p><p><span class="font-medium">💳 Method:</span> <!-- -->Cheque</p></div><div><p><span class="font-medium">🔢 Transaction ID:</span> <!-- -->CHQ-456789</p><p><span class="font-medium">👤 Processed By:</span> <!-- -->Finance Admin</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Receipt</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Print Receipt</button><button class="text-blue-600 hover:text-blue-800 text-sm">Transaction Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Ananya Gupta</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 10</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Confirmed</span></div><p class="text-blue-600 font-medium">RCP-2024-001238</p><p class="text-gray-600 text-sm">10B012<!-- --> • Bill: <!-- -->BILL-2024-001239</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->31,200</p><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-14<!-- --> at <!-- -->04:10 PM</p><p><span class="font-medium">💳 Method:</span> <!-- -->UPI</p></div><div><p><span class="font-medium">🔢 Transaction ID:</span> <!-- -->UPI789123456</p><p><span class="font-medium">👤 Processed By:</span> <!-- -->System Auto</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Receipt</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Print Receipt</button><button class="text-blue-600 hover:text-blue-800 text-sm">Transaction Details</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Rohit Kumar</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 12</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Confirmed</span></div><p class="text-blue-600 font-medium">RCP-2024-001239</p><p class="text-gray-600 text-sm">12A008<!-- --> • Bill: <!-- -->BILL-2024-001240</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->35,800</p><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-14<!-- --> at <!-- -->01:30 PM</p><p><span class="font-medium">💳 Method:</span> <!-- -->Online Banking</p></div><div><p><span class="font-medium">🔢 Transaction ID:</span> <!-- -->TXN789456125</p><p><span class="font-medium">👤 Processed By:</span> <!-- -->System Auto</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Receipt</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Print Receipt</button><button class="text-blue-600 hover:text-blue-800 text-sm">Transaction Details</button></div></div></div></div><div class="mt-6 flex items-center justify-between"><p class="text-sm text-gray-600">Showing 1-6 of 2,456 transactions</p><div class="flex space-x-2"><button class="px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50">Previous</button><button class="px-3 py-1 bg-blue-600 text-white rounded-md">1</button><button class="px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50">2</button><button class="px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50">3</button><button class="px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50">Next</button></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Payment Trends</h3><p class="text-sm text-muted-foreground">Monthly payment volume and trends</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">October 2023</h4><span class="text-sm font-medium text-green-600">₹3.2M</span></div><div class="grid grid-cols-2 gap-2 text-xs text-gray-600"><div>Transactions: <!-- -->189</div><div>Avg: <!-- -->₹16,931</div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">November 2023</h4><span class="text-sm font-medium text-green-600">₹4.1M</span></div><div class="grid grid-cols-2 gap-2 text-xs text-gray-600"><div>Transactions: <!-- -->234</div><div>Avg: <!-- -->₹17,521</div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">December 2023</h4><span class="text-sm font-medium text-green-600">₹5.8M</span></div><div class="grid grid-cols-2 gap-2 text-xs text-gray-600"><div>Transactions: <!-- -->298</div><div>Avg: <!-- -->₹19,463</div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">January 2024</h4><span class="text-sm font-medium text-green-600">₹6.5M</span></div><div class="grid grid-cols-2 gap-2 text-xs text-gray-600"><div>Transactions: <!-- -->312</div><div>Avg: <!-- -->₹20,833</div></div></div><div class="border rounded p-3"><div class="flex items-center justify-between mb-2"><h4 class="font-medium text-sm">February 2024</h4><span class="text-sm font-medium text-green-600">₹5.9M</span></div><div class="grid grid-cols-2 gap-2 text-xs text-gray-600"><div>Transactions: <!-- -->289</div><div>Avg: <!-- -->₹20,415</div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Payment Method Distribution</h3><p class="text-sm text-muted-foreground">Historical payment method usage</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Online Banking</span><p class="text-xs text-gray-500">1234<!-- --> transactions</p></div><div class="text-right"><span class="text-sm font-medium">₹19.6M</span><div class="flex items-center space-x-2 mt-1"><div class="w-16 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:50%"></div></div><span class="text-xs text-gray-500">50<!-- -->%</span></div></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Credit/Debit Card</span><p class="text-xs text-gray-500">678<!-- --> transactions</p></div><div class="text-right"><span class="text-sm font-medium">₹11.0M</span><div class="flex items-center space-x-2 mt-1"><div class="w-16 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:28%"></div></div><span class="text-xs text-gray-500">28<!-- -->%</span></div></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">UPI</span><p class="text-xs text-gray-500">345<!-- --> transactions</p></div><div class="text-right"><span class="text-sm font-medium">₹5.5M</span><div class="flex items-center space-x-2 mt-1"><div class="w-16 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:14%"></div></div><span class="text-xs text-gray-500">14<!-- -->%</span></div></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Cash</span><p class="text-xs text-gray-500">156<!-- --> transactions</p></div><div class="text-right"><span class="text-sm font-medium">₹2.4M</span><div class="flex items-center space-x-2 mt-1"><div class="w-16 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:6%"></div></div><span class="text-xs text-gray-500">6<!-- -->%</span></div></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Cheque</span><p class="text-xs text-gray-500">43<!-- --> transactions</p></div><div class="text-right"><span class="text-sm font-medium">₹0.7M</span><div class="flex items-center space-x-2 mt-1"><div class="w-16 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:2%"></div></div><span class="text-xs text-gray-500">2<!-- -->%</span></div></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Quick Actions</h3><p class="text-sm text-muted-foreground">Common payment history operations</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"><div class="text-3xl mb-2">📥</div><h3 class="font-semibold mb-1">Export All Data</h3><p class="text-sm text-gray-600 mb-3">Download complete payment history</p><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Execute</button></div><div class="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"><div class="text-3xl mb-2">📊</div><h3 class="font-semibold mb-1">Generate Report</h3><p class="text-sm text-gray-600 mb-3">Create payment summary report</p><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Execute</button></div><div class="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"><div class="text-3xl mb-2">🔍</div><h3 class="font-semibold mb-1">Audit Trail</h3><p class="text-sm text-gray-600 mb-3">View detailed audit information</p><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Execute</button></div><div class="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"><div class="text-3xl mb-2">⚖️</div><h3 class="font-semibold mb-1">Reconciliation</h3><p class="text-sm text-gray-600 mb-3">Match payments with bank records</p><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Execute</button></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[1293,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"4857\",\"static/chunks/app/dashboard/financials/history/page-3a21f55123c2751b.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"WyP9Wc4exBySD9T7YRTKE\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/financials/history\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"financials\",{\"children\":[\"history\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"financials\",{\"children\":[\"history\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"financials\",\"children\",\"history\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"financials\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>