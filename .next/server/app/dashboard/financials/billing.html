<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/financials/billing/page-6a7bdb7889f1ba23.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Billing Management</h1><p class="text-muted-foreground">Generate and manage student fee bills and invoices</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Bulk Generate</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Create Bill</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Bills Generated</h3><span class="text-2xl">📄</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">1,234</div><p class="text-xs text-muted-foreground">This month</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Total Billed</h3><span class="text-2xl">💰</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">₹45.6M</div><p class="text-xs text-muted-foreground">Current term</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Pending Bills</h3><span class="text-2xl">⏳</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">156</div><p class="text-xs text-muted-foreground">Awaiting generation</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Overdue Bills</h3><span class="text-2xl">⚠️</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">23</div><p class="text-xs text-muted-foreground">Past due date</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Bills</h3><p class="text-sm text-muted-foreground">Latest generated bills and their status</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Aarav Sharma</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9</span><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Sent</span></div><p class="text-blue-600 font-medium">BILL-2024-001234</p><p class="text-gray-600 text-sm">9A001</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📅 Bill Date:</span> <!-- -->2024-02-15</p><p><span class="font-medium">⏰ Due Date:</span> <!-- -->2024-03-15</p></div><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->29,500</p></div></div><div class="mt-3"><p class="text-sm font-medium text-gray-700 mb-1">Bill Items:</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Tuition Fee</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Development Fee</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Library Fee</span></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Bill</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Resend</button><button class="text-blue-600 hover:text-blue-800 text-sm">Download PDF</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Kavya Gupta</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Paid</span></div><p class="text-blue-600 font-medium">BILL-2024-001235</p><p class="text-gray-600 text-sm">9A002</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📅 Bill Date:</span> <!-- -->2024-02-15</p><p><span class="font-medium">⏰ Due Date:</span> <!-- -->2024-03-15</p></div><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->41,500</p></div></div><div class="mt-3"><p class="text-sm font-medium text-gray-700 mb-1">Bill Items:</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Tuition Fee</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Development Fee</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Transport Fee</span></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Bill</button><button class="text-blue-600 hover:text-blue-800 text-sm">Download PDF</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Arjun Singh</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 11</span><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Overdue</span></div><p class="text-blue-600 font-medium">BILL-2024-001236</p><p class="text-gray-600 text-sm">11A003</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📅 Bill Date:</span> <!-- -->2024-02-14</p><p><span class="font-medium">⏰ Due Date:</span> <!-- -->2024-03-14</p></div><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->33,250</p></div></div><div class="mt-3"><p class="text-sm font-medium text-gray-700 mb-1">Bill Items:</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Tuition Fee</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Exam Fee</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Sports Fee</span></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Bill</button><button class="text-blue-600 hover:text-blue-800 text-sm">Download PDF</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Riya Patel</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 6</span><span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Draft</span></div><p class="text-blue-600 font-medium">BILL-2024-001237</p><p class="text-gray-600 text-sm">6B004</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">📅 Bill Date:</span> <!-- -->2024-02-14</p><p><span class="font-medium">⏰ Due Date:</span> <!-- -->2024-03-14</p></div><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->25,750</p></div></div><div class="mt-3"><p class="text-sm font-medium text-gray-700 mb-1">Bill Items:</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Tuition Fee</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Development Fee</span></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Bill</button><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm">Send Bill</button><button class="text-blue-600 hover:text-blue-800 text-sm">Download PDF</button></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Generate New Bill</h3><p class="text-sm text-muted-foreground">Create individual or bulk bills for students</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="grid gap-4 md:grid-cols-3"><div><label class="text-sm font-medium">Student Selection</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Option</option><option value="individual">Individual Student</option><option value="class">Entire Class</option><option value="grade">Entire Grade</option><option value="all">All Students</option></select></div><div><label class="text-sm font-medium">Bill Type</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Type</option><option value="term">Term Fee Bill</option><option value="monthly">Monthly Fee Bill</option><option value="annual">Annual Fee Bill</option><option value="custom">Custom Bill</option></select></div><div><label class="text-sm font-medium">Due Date</label><input type="date" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"/></div></div><div><label class="text-sm font-medium">Fee Categories to Include</label><div class="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2"><div class="flex items-center space-x-2"><input type="checkbox" class="rounded" checked=""/><label class="text-sm">Tuition Fee</label></div><div class="flex items-center space-x-2"><input type="checkbox" class="rounded" checked=""/><label class="text-sm">Development Fee</label></div><div class="flex items-center space-x-2"><input type="checkbox" class="rounded" checked=""/><label class="text-sm">Exam Fee</label></div><div class="flex items-center space-x-2"><input type="checkbox" class="rounded" checked=""/><label class="text-sm">Library Fee</label></div><div class="flex items-center space-x-2"><input type="checkbox" class="rounded"/><label class="text-sm">Sports Fee</label></div><div class="flex items-center space-x-2"><input type="checkbox" class="rounded"/><label class="text-sm">Transport Fee</label></div><div class="flex items-center space-x-2"><input type="checkbox" class="rounded"/><label class="text-sm">Late Fee</label></div><div class="flex items-center space-x-2"><input type="checkbox" class="rounded"/><label class="text-sm">Other Charges</label></div></div></div><div class="grid gap-4 md:grid-cols-2"><div><label class="text-sm font-medium">Bill Template</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Template</option><option value="standard">Standard Bill Template</option><option value="detailed">Detailed Bill Template</option><option value="summary">Summary Bill Template</option><option value="custom">Custom Template</option></select></div><div><label class="text-sm font-medium">Delivery Method</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Method</option><option value="email">Email to Parents</option><option value="sms">SMS Notification</option><option value="portal">Student Portal</option><option value="print">Print for Distribution</option></select></div></div><div><label class="text-sm font-medium">Additional Notes</label><textarea rows="3" placeholder="Add any special instructions or notes for this bill..." class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"></textarea></div><div class="flex space-x-2"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Generate Bills</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Preview Template</button><button class="text-blue-600 hover:text-blue-800">Save as Draft</button></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Billing Summary</h3><p class="text-sm text-muted-foreground">Current month billing overview</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Bills Sent</span><p class="text-xs text-gray-500">1089<!-- --> bills</p></div><div class="text-right"><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">₹42.3M</span></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Bills Paid</span><p class="text-xs text-gray-500">934<!-- --> bills</p></div><div class="text-right"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">₹36.8M</span></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Bills Overdue</span><p class="text-xs text-gray-500">23<!-- --> bills</p></div><div class="text-right"><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">₹1.2M</span></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Bills Draft</span><p class="text-xs text-gray-500">156<!-- --> bills</p></div><div class="text-right"><span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">₹6.1M</span></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Billing Tools</h3><p class="text-sm text-muted-foreground">Quick actions and utilities</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">📄</span><div><p class="font-medium text-sm">Bulk Bill Generator</p><p class="text-xs text-gray-600">Generate multiple bills at once</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use</button></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">📧</span><div><p class="font-medium text-sm">Payment Reminder</p><p class="text-xs text-gray-600">Send payment reminders</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use</button></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">📋</span><div><p class="font-medium text-sm">Bill Templates</p><p class="text-xs text-gray-600">Manage bill templates</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use</button></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">🧮</span><div><p class="font-medium text-sm">Late Fee Calculator</p><p class="text-xs text-gray-600">Calculate late fees automatically</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use</button></div><div class="flex items-center justify-between border rounded p-3"><div class="flex items-center space-x-3"><span class="text-2xl">✅</span><div><p class="font-medium text-sm">Bill Reconciliation</p><p class="text-xs text-gray-600">Reconcile bills with payments</p></div></div><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use</button></div></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[6184,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"4656\",\"static/chunks/app/dashboard/financials/billing/page-6a7bdb7889f1ba23.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"az92YWeqjFDK2ONnGo-1U\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/financials/billing\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"financials\",{\"children\":[\"billing\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"financials\",{\"children\":[\"billing\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"financials\",\"children\",\"billing\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"financials\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>