<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/dashboard/financials/payments/page-3ca3cdce161f5ea3.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Payment Management</h1><p class="text-muted-foreground">Track and manage student fee payments and transactions</p></div><div class="flex space-x-2"><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Payment Report</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Record Payment</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Today&#x27;s Collections</h3><span class="text-2xl">💰</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">₹2.4M</div><p class="text-xs text-muted-foreground">156 transactions</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Monthly Collections</h3><span class="text-2xl">📊</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">₹36.8M</div><p class="text-xs text-muted-foreground">+12% from last month</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Pending Payments</h3><span class="text-2xl">⏳</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">₹8.2M</div><p class="text-xs text-muted-foreground">234 students</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Collection Rate</h3><span class="text-2xl">📈</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">87%</div><p class="text-xs text-muted-foreground">This term</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Payments</h3><p class="text-sm text-muted-foreground">Latest fee payments received</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Aarav Sharma</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Confirmed</span></div><p class="text-blue-600 font-medium">RCP-2024-001234</p><p class="text-gray-600 text-sm">9A001<!-- --> • Bill: <!-- -->BILL-2024-001234</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->29,500</p><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-16</p></div><div><p><span class="font-medium">💳 Method:</span> <!-- -->Online Banking</p><p><span class="font-medium">🔢 Transaction ID:</span> <!-- -->TXN789456123</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Receipt</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Print Receipt</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Kavya Gupta</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 9</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Confirmed</span></div><p class="text-blue-600 font-medium">RCP-2024-001235</p><p class="text-gray-600 text-sm">9A002<!-- --> • Bill: <!-- -->BILL-2024-001235</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->41,500</p><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-16</p></div><div><p><span class="font-medium">💳 Method:</span> <!-- -->Credit Card</p><p><span class="font-medium">🔢 Transaction ID:</span> <!-- -->TXN789456124</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Receipt</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Print Receipt</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Riya Patel</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 6</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Confirmed</span></div><p class="text-blue-600 font-medium">RCP-2024-001236</p><p class="text-gray-600 text-sm">6B004<!-- --> • Bill: <!-- -->BILL-2024-001237</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->25,750</p><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-15</p></div><div><p><span class="font-medium">💳 Method:</span> <!-- -->Cash</p><p><span class="font-medium">🔢 Transaction ID:</span> <!-- -->CASH-001236</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Receipt</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Print Receipt</button></div></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-lg font-semibold">Vikram Singh</h3><span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Grade 11</span><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Pending Clearance</span></div><p class="text-blue-600 font-medium">RCP-2024-001237</p><p class="text-gray-600 text-sm">11A005<!-- --> • Bill: <!-- -->BILL-2024-001238</p><div class="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600"><div><p><span class="font-medium">💰 Amount:</span> ₹<!-- -->33,250</p><p><span class="font-medium">📅 Date:</span> <!-- -->2024-02-15</p></div><div><p><span class="font-medium">💳 Method:</span> <!-- -->Cheque</p><p><span class="font-medium">🔢 Transaction ID:</span> <!-- -->CHQ-456789</p></div></div></div><div class="flex flex-col space-y-2 ml-4"><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">View Receipt</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm">Print Receipt</button><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm">Confirm Payment</button></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Record New Payment</h3><p class="text-sm text-muted-foreground">Manually record a payment transaction</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="grid gap-4 md:grid-cols-3"><div><label class="text-sm font-medium">Student</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Student</option><option value="aarav">Aarav Sharma (9A001)</option><option value="kavya">Kavya Gupta (9A002)</option><option value="arjun">Arjun Singh (11A003)</option><option value="riya">Riya Patel (6B004)</option></select></div><div><label class="text-sm font-medium">Outstanding Bill</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Bill</option><option value="bill1">BILL-2024-001234 (₹29,500)</option><option value="bill2">BILL-2024-001235 (₹41,500)</option><option value="bill3">BILL-2024-001236 (₹25,750)</option></select></div><div><label class="text-sm font-medium">Payment Amount</label><input type="number" placeholder="29500" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"/></div></div><div class="grid gap-4 md:grid-cols-3"><div><label class="text-sm font-medium">Payment Method</label><select class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"><option value="">Select Method</option><option value="cash">Cash</option><option value="cheque">Cheque</option><option value="online">Online Banking</option><option value="card">Credit/Debit Card</option><option value="upi">UPI</option></select></div><div><label class="text-sm font-medium">Payment Date</label><input type="date" class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"/></div><div><label class="text-sm font-medium">Transaction Reference</label><input type="text" placeholder="Transaction ID / Cheque No." class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"/></div></div><div><label class="text-sm font-medium">Payment Notes</label><textarea rows="3" placeholder="Add any additional notes about this payment..." class="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"></textarea></div><div class="flex space-x-2"><button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">Record Payment</button><button class="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50">Save as Draft</button><button class="text-blue-600 hover:text-blue-800">Generate Receipt</button></div></div></div></div><div class="grid gap-4 md:grid-cols-2"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Payment Methods</h3><p class="text-sm text-muted-foreground">Distribution of payment methods</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Online Banking</span><p class="text-xs text-gray-500">456<!-- --> transactions</p></div><div class="text-right"><span class="text-sm font-medium">₹18.2M</span><div class="flex items-center space-x-2 mt-1"><div class="w-16 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:49%"></div></div><span class="text-xs text-gray-500">49<!-- -->%</span></div></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Credit/Debit Card</span><p class="text-xs text-gray-500">234<!-- --> transactions</p></div><div class="text-right"><span class="text-sm font-medium">₹9.8M</span><div class="flex items-center space-x-2 mt-1"><div class="w-16 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:27%"></div></div><span class="text-xs text-gray-500">27<!-- -->%</span></div></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">UPI</span><p class="text-xs text-gray-500">189<!-- --> transactions</p></div><div class="text-right"><span class="text-sm font-medium">₹6.1M</span><div class="flex items-center space-x-2 mt-1"><div class="w-16 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:16%"></div></div><span class="text-xs text-gray-500">16<!-- -->%</span></div></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Cash</span><p class="text-xs text-gray-500">89<!-- --> transactions</p></div><div class="text-right"><span class="text-sm font-medium">₹2.3M</span><div class="flex items-center space-x-2 mt-1"><div class="w-16 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:6%"></div></div><span class="text-xs text-gray-500">6<!-- -->%</span></div></div></div><div class="flex items-center justify-between"><div><span class="text-sm font-medium">Cheque</span><p class="text-xs text-gray-500">23<!-- --> transactions</p></div><div class="text-right"><span class="text-sm font-medium">₹0.8M</span><div class="flex items-center space-x-2 mt-1"><div class="w-16 bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" style="width:2%"></div></div><span class="text-xs text-gray-500">2<!-- -->%</span></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Payment Status</h3><p class="text-sm text-muted-foreground">Current payment status overview</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="text-center"><div class="text-3xl font-bold text-green-600 mb-1">87%</div><p class="text-sm text-gray-600">Collection Rate</p><div class="w-full bg-gray-200 rounded-full h-3 mt-2"><div class="bg-green-600 h-3 rounded-full" style="width:87%"></div></div></div><div class="space-y-2"><div class="flex justify-between text-sm"><span>Payments Received</span><span class="font-medium text-green-600">₹36.8M</span></div><div class="flex justify-between text-sm"><span>Pending Payments</span><span class="font-medium text-yellow-600">₹5.4M</span></div><div class="flex justify-between text-sm"><span>Overdue Payments</span><span class="font-medium text-red-600">₹2.8M</span></div></div><div class="pt-3 border-t"><div class="flex justify-between font-medium"><span>Total Expected</span><span class="text-blue-600">₹45.0M</span></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Payment Tools</h3><p class="text-sm text-muted-foreground">Quick actions and utilities for payment management</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"><div class="text-3xl mb-2">📥</div><h3 class="font-semibold mb-1">Bulk Payment Import</h3><p class="text-sm text-gray-600 mb-3">Import multiple payments</p><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div><div class="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"><div class="text-3xl mb-2">🔄</div><h3 class="font-semibold mb-1">Payment Reconciliation</h3><p class="text-sm text-gray-600 mb-3">Match payments with bills</p><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div><div class="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"><div class="text-3xl mb-2">↩️</div><h3 class="font-semibold mb-1">Refund Management</h3><p class="text-sm text-gray-600 mb-3">Process payment refunds</p><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div><div class="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"><div class="text-3xl mb-2">🌐</div><h3 class="font-semibold mb-1">Payment Gateway</h3><p class="text-sm text-gray-600 mb-3">Online payment integration</p><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Use Tool</button></div></div></div></div></div></main></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[8586,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"7669\",\"static/chunks/app/dashboard/financials/payments/page-3ca3cdce161f5ea3.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"WyP9Wc4exBySD9T7YRTKE\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/financials/payments\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"financials\",{\"children\":[\"payments\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"financials\",{\"children\":[\"payments\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"financials\",\"children\",\"payments\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"financials\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>