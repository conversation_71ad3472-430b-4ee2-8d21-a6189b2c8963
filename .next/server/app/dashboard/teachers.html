<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/dashboard/teachers/page-8ddee609dffc32b8.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="flex h-screen bg-gray-100"><div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col"><div class="flex items-center justify-between p-4 border-b border-gray-200"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">EMS</span></div><div><h1 class="font-semibold text-gray-900">EMS</h1><p class="text-xs text-gray-500">Demo School</p></div></div></div><nav class="flex-1 px-2 py-4 space-y-1"><div><a class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900" href="/dashboard"><span class="mr-3 text-lg">🏠</span><span>Dashboard</span></a></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">👥</span><span>Student Admissions</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📚</span><span>Academic Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">💰</span><span>Student Financials</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">📖</span><span>Library Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🎓</span><span>Alumni Engagement</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🏠</span><span>Hostel Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white"><div class="flex items-center"><span class="mr-3 text-lg">👨‍🏫</span><span>Teacher Management</span></div><span class="transition-transform ">▶</span></button></div><div><button class="group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900"><div class="flex items-center"><span class="mr-3 text-lg">🚌</span><span>Transport Management</span></div><span class="transition-transform ">▶</span></button></div></nav><div class="border-t border-gray-200 p-4"><div class="flex items-center"><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div><div class="ml-3"><p class="text-sm font-medium text-gray-900">Demo User</p><p class="text-xs text-gray-500">Admin</p></div></div></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white shadow-sm border-b border-gray-200"><div class="flex items-center justify-between px-6 py-4"><div class="flex-1 max-w-lg"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><span class="text-gray-400">🔍</span></div><input type="text" placeholder="Search..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/></div></div><div class="flex items-center space-x-4"><button class="p-2 text-gray-400 hover:text-gray-500"><span class="text-xl">🔔</span></button><div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-gray-700">DU</span></div></div></div></header><main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Teacher Management</h1><p class="text-muted-foreground">Comprehensive teacher profiling and performance management</p></div><div class="flex items-center space-x-2"><button class="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90">Generate Report</button><button class="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90">Add Teacher</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Total Teachers</h3><span class="text-2xl">👨‍🏫</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">85</div><p class="text-xs text-muted-foreground">Faculty members</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Active</h3><span class="text-2xl">✅</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">82</div><p class="text-xs text-muted-foreground">Currently teaching</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">On Leave</h3><span class="text-2xl">🏖️</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">3</div><p class="text-xs text-muted-foreground">Temporary absence</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">New Hires</h3><span class="text-2xl">🆕</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">5</div><p class="text-xs text-muted-foreground">This semester</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Avg Experience</h3><span class="text-2xl">📈</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">8.5<!-- --> yrs</div><p class="text-xs text-muted-foreground">Teaching experience</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Cert. Due</h3><span class="text-2xl">📜</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">12</div><p class="text-xs text-muted-foreground">Renewal needed</p></div></div></div><div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"><div class="rounded-lg border bg-card text-card-foreground shadow-sm lg:col-span-2"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Activity</h3><p class="text-sm text-muted-foreground">Latest teacher management activities</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm">👋</span></div><div><p class="font-medium">Dr. Sarah Wilson</p><p class="text-sm text-muted-foreground">Mathematics</p></div></div><div class="text-right"><p class="text-sm font-medium capitalize">hire</p><p class="text-xs text-muted-foreground">2024-01-15</p></div></div><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm">📜</span></div><div><p class="font-medium">Prof. John Smith</p><p class="text-sm text-muted-foreground">Advanced Physics</p></div></div><div class="text-right"><p class="text-sm font-medium capitalize">certification</p><p class="text-xs text-muted-foreground">2024-01-14</p></div></div><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm">⭐</span></div><div><p class="font-medium">Ms. Emily Davis</p><p class="text-sm text-muted-foreground">Excellent</p></div></div><div class="text-right"><p class="text-sm font-medium capitalize">evaluation</p><p class="text-xs text-muted-foreground">2024-01-13</p></div></div><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm">📚</span></div><div><p class="font-medium">Mr. Michael Brown</p><p class="text-sm text-muted-foreground">Digital Teaching</p></div></div><div class="text-right"><p class="text-sm font-medium capitalize">training</p><p class="text-xs text-muted-foreground">2024-01-12</p></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Top Performers</h3><p class="text-sm text-muted-foreground">Highest rated teachers</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Dr. Sarah Wilson</p><p class="text-xs text-muted-foreground">Mathematics</p><p class="text-xs text-muted-foreground">120<!-- --> students</p></div><div class="text-right"><div class="flex items-center space-x-1"><span class="text-sm font-medium">4.9</span><span class="text-yellow-500">⭐</span></div></div></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Prof. John Smith</p><p class="text-xs text-muted-foreground">Physics</p><p class="text-xs text-muted-foreground">95<!-- --> students</p></div><div class="text-right"><div class="flex items-center space-x-1"><span class="text-sm font-medium">4.8</span><span class="text-yellow-500">⭐</span></div></div></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Ms. Emily Davis</p><p class="text-xs text-muted-foreground">English</p><p class="text-xs text-muted-foreground">110<!-- --> students</p></div><div class="text-right"><div class="flex items-center space-x-1"><span class="text-sm font-medium">4.7</span><span class="text-yellow-500">⭐</span></div></div></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Mr. Robert Johnson</p><p class="text-xs text-muted-foreground">Chemistry</p><p class="text-xs text-muted-foreground">88<!-- --> students</p></div><div class="text-right"><div class="flex items-center space-x-1"><span class="text-sm font-medium">4.6</span><span class="text-yellow-500">⭐</span></div></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Upcoming Evaluations</h3><p class="text-sm text-muted-foreground">Scheduled teacher performance reviews</p></div><div class="p-6 pt-0"><div class="overflow-x-auto"><table class="w-full table-auto"><thead><tr class="border-b border-gray-200"><th class="text-left py-3 px-4 font-medium text-gray-900">Teacher</th><th class="text-left py-3 px-4 font-medium text-gray-900">Department</th><th class="text-left py-3 px-4 font-medium text-gray-900">Type</th><th class="text-left py-3 px-4 font-medium text-gray-900">Due Date</th><th class="text-left py-3 px-4 font-medium text-gray-900">Actions</th></tr></thead><tbody><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-gray-900">Ms. Lisa Garcia</span></td><td class="py-3 px-4"><span class="text-gray-900">Biology</span></td><td class="py-3 px-4"><span class="text-gray-900">Annual Review</span></td><td class="py-3 px-4"><span class="text-gray-900">2024-01-20</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">Schedule</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">View Profile</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-gray-900">Mr. David Miller</span></td><td class="py-3 px-4"><span class="text-gray-900">History</span></td><td class="py-3 px-4"><span class="text-gray-900">Mid-Year Review</span></td><td class="py-3 px-4"><span class="text-gray-900">2024-01-22</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">Schedule</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">View Profile</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-gray-900">Dr. Maria Rodriguez</span></td><td class="py-3 px-4"><span class="text-gray-900">Chemistry</span></td><td class="py-3 px-4"><span class="text-gray-900">Probation Review</span></td><td class="py-3 px-4"><span class="text-gray-900">2024-01-25</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">Schedule</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">View Profile</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-gray-900">Prof. James Wilson</span></td><td class="py-3 px-4"><span class="text-gray-900">Mathematics</span></td><td class="py-3 px-4"><span class="text-gray-900">Annual Review</span></td><td class="py-3 px-4"><span class="text-gray-900">2024-01-28</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">Schedule</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">View Profile</button></div></td></tr></tbody></table></div></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">👥</span><h3 class="font-semibold tracking-tight text-lg">Teacher Profiles</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Manage teacher information</p><button class="text-sm font-medium text-primary hover:text-primary/80">View Profiles →</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">⭐</span><h3 class="font-semibold tracking-tight text-lg">Evaluations</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Performance evaluations</p><button class="text-sm font-medium text-primary hover:text-primary/80">Manage Evaluations →</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📚</span><h3 class="font-semibold tracking-tight text-lg">Training</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Professional development</p><button class="text-sm font-medium text-primary hover:text-primary/80">View Training →</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📊</span><h3 class="font-semibold tracking-tight text-lg">Reports</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Teacher analytics and reports</p><button class="text-sm font-medium text-primary hover:text-primary/80">View Reports →</button></div></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><a href="/dashboard/teachers/profiles"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">👨‍🏫</span><h3 class="font-semibold tracking-tight text-lg">Teacher Profiles</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Faculty information &amp; credentials</p><span class="text-sm font-medium text-primary hover:text-primary/80">Manage Profiles →</span></div></div></a><a href="/dashboard/teachers/evaluations"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📊</span><h3 class="font-semibold tracking-tight text-lg">Performance</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Teacher assessments &amp; reviews</p><span class="text-sm font-medium text-primary hover:text-primary/80">View Evaluations →</span></div></div></a><a href="/dashboard/teachers/training"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">🎓</span><h3 class="font-semibold tracking-tight text-lg">Training</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Professional development</p><span class="text-sm font-medium text-primary hover:text-primary/80">View Programs →</span></div></div></a><a href="/dashboard/teachers/schedules"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📅</span><h3 class="font-semibold tracking-tight text-lg">Schedules</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Teaching assignments &amp; timetables</p><span class="text-sm font-medium text-primary hover:text-primary/80">Manage Schedules →</span></div></div></a></div></div></main></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[4236,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"4367\",\"static/chunks/app/dashboard/teachers/page-8ddee609dffc32b8.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\na:I[7108,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7663\",\"static/chunks/app/dashboard/layout-03c8f7a49c7100d8.js\"],\"\"]\nd:I[8955,[],\"\"]\nb:{}\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"WyP9Wc4exBySD9T7YRTKE\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/dashboard/teachers\",\"initialTree\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"teachers\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"dashboard\",{\"children\":[\"teachers\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\",\"teachers\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"$La\",null,{\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"dashboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],\"params\":\"$b\"}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$Lc\"],\"globalErrorComponent\":\"$d\"}]]\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>