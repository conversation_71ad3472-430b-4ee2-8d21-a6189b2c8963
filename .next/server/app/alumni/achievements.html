<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/alumni/achievements/page-0e8286d0ccbe5e07.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900">Achievement Showcase</h1><p class="text-gray-600">Celebrate and recognize alumni success stories</p></div><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">+ Submit Achievement</button></div><div class="grid gap-4 md:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Total Achievements</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-blue-600">3</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Featured</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-purple-600">1</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">This Month</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-green-600">5</div><p class="text-xs text-gray-500">New submissions</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Total Engagement</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-orange-600">703</div></div></div></div><div class="border-b border-gray-200"><nav class="-mb-px flex space-x-8"><button class="py-2 px-1 border-b-2 font-medium text-sm border-blue-500 text-blue-600">🏆 Achievements (<!-- -->3<!-- -->)</button><button class="py-2 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700">🥇 Awards (<!-- -->2<!-- -->)</button></nav></div><div><div class="space-y-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 pt-6"><div class="grid grid-cols-1 md:grid-cols-4 gap-4"><div><label class="block text-sm font-medium text-gray-700 mb-2">Search</label><input type="text" placeholder="Search achievements, alumni, or tags" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value=""/></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Category</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Categories</option><option value="professional">Professional</option><option value="academic">Academic</option><option value="entrepreneurship">Entrepreneurship</option><option value="social_impact">Social Impact</option><option value="innovation">Innovation</option><option value="leadership">Leadership</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Recognition Level</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Levels</option><option value="local">Local</option><option value="national">National</option><option value="international">International</option><option value="global">Global</option></select></div><div class="flex items-end"><button class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200">Export Achievements</button></div></div></div></div><div><h2 class="text-lg font-semibold mb-4">Featured Achievements</h2><div class="grid gap-6 md:grid-cols-2"><div class="rounded-lg bg-card text-card-foreground shadow-sm border-2 border-purple-200 hover:shadow-md transition-shadow"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><span class="text-purple-600">⭐</span><h3 class="font-semibold tracking-tight text-lg">Forbes 30 Under 30 in Technology</h3></div><span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">FEATURED</span></div><p class="text-sm text-muted-foreground">John Smith<!-- --> • Class of <!-- -->2020</p></div><div class="p-6 pt-0"><p class="text-sm text-gray-600 mb-4">Recognized for innovative contributions to artificial intelligence and machine learning applications in healthcare.</p><div class="flex items-center justify-between"><div class="flex space-x-4 text-sm text-gray-500"><span>👍 <!-- -->245</span><span>💬 <!-- -->32</span><span>📤 <!-- -->18</span></div><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div></div><div><h2 class="text-lg font-semibold mb-4">All Achievements</h2><div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center justify-between"><h3 class="font-semibold tracking-tight text-lg">Forbes 30 Under 30 in Technology</h3><span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">FEATURED</span></div><p class="text-sm text-muted-foreground">John Smith<!-- --> • Class of <!-- -->2020</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center space-x-2"><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">PROFESSIONAL</span><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">NATIONAL</span></div><p class="text-sm text-gray-600">Recognized for innovative contributions to artificial intelligence and machine learning applications<!-- -->...</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">AI</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Healthcare</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Innovation</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->1</span></div><div class="flex items-center justify-between"><div class="flex space-x-3 text-sm text-gray-500"><span>👍 <!-- -->245</span><span>💬 <!-- -->32</span></div><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center justify-between"><h3 class="font-semibold tracking-tight text-lg">Breakthrough Medical Device Patent</h3><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">APPROVED</span></div><p class="text-sm text-muted-foreground">Sarah Johnson<!-- --> • Class of <!-- -->2019</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center space-x-2"><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">INNOVATION</span><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">NATIONAL</span></div><p class="text-sm text-gray-600">Developed and patented a revolutionary medical device that improves patient outcomes in cardiac surg<!-- -->...</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Medical Device</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Patent</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Innovation</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->1</span></div><div class="flex items-center justify-between"><div class="flex space-x-3 text-sm text-gray-500"><span>👍 <!-- -->189</span><span>💬 <!-- -->24</span></div><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center justify-between"><h3 class="font-semibold tracking-tight text-lg">Successful Series A Funding Round</h3><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">APPROVED</span></div><p class="text-sm text-muted-foreground">Michael Brown<!-- --> • Class of <!-- -->2021</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center space-x-2"><span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">ENTREPRENEURSHIP</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">LOCAL</span></div><p class="text-sm text-gray-600">Led company to raise $10M in Series A funding, scaling the team from 5 to 50 employees.<!-- -->...</p><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Startup</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Funding</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Entrepreneurship</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->1</span></div><div class="flex items-center justify-between"><div class="flex space-x-3 text-sm text-gray-500"><span>👍 <!-- -->156</span><span>💬 <!-- -->18</span></div><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div></div></div></div></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[4090,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"87\",\"static/chunks/app/alumni/achievements/page-0e8286d0ccbe5e07.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\nb:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"Tn7xWRdXVWNLs4FpbaknK\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/alumni/achievements\",\"initialTree\":[\"\",{\"children\":[\"alumni\",{\"children\":[\"achievements\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"alumni\",{\"children\":[\"achievements\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"alumni\",\"children\",\"achievements\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"alumni\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$La\"],\"globalErrorComponent\":\"$b\"}]]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>