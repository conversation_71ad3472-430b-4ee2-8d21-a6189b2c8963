<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/alumni/jobs/page-0fb049753b45415b.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900">Job Board</h1><p class="text-gray-600">Discover career opportunities from the alumni network</p></div><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">+ Post a Job</button></div><div class="grid gap-4 md:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Active Jobs</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-blue-600">3</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Remote Jobs</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-green-600">2</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">This Week</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-purple-600">5</div><p class="text-xs text-gray-500">New postings</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Total Applications</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-orange-600">140</div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 pt-6"><div class="grid grid-cols-1 md:grid-cols-6 gap-4"><div><label class="block text-sm font-medium text-gray-700 mb-2">Search</label><input type="text" placeholder="Search jobs, companies, skills" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value=""/></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Location</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Locations</option><option value="USA">USA</option><option value="Canada">Canada</option><option value="UK">UK</option><option value="Remote">Remote</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Job Type</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Types</option><option value="full_time">Full Time</option><option value="part_time">Part Time</option><option value="contract">Contract</option><option value="internship">Internship</option><option value="freelance">Freelance</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Level</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Levels</option><option value="entry">Entry Level</option><option value="mid">Mid Level</option><option value="senior">Senior Level</option><option value="executive">Executive</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Industry</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Industries</option><option value="Technology">Technology</option><option value="Healthcare">Healthcare</option><option value="Finance">Finance</option><option value="Education">Education</option></select></div><div class="flex items-end"><label class="flex items-center"><input type="checkbox" class="mr-2"/><span class="text-sm">Remote Only</span></label></div></div></div></div><div class="space-y-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="p-6 pt-6"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-xl font-semibold">Senior Software Engineer</h3><span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">FEATURED</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">ACTIVE</span></div><div class="flex items-center space-x-4 text-gray-600 mb-3"><span class="font-medium">TechCorp Inc.</span><span>•</span><span>San Francisco<!-- -->, <!-- -->CA</span><span>•</span><span class="text-green-600">Remote</span><span>•</span><span class="text-blue-600">Hybrid</span></div><p class="text-gray-600 mb-4">We are looking for a Senior Software Engineer to join our growing team. You will be responsible for designing and implementing scalable software solutions.<!-- -->...</p><div class="flex flex-wrap gap-2 mb-4"><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">FULL TIME</span><span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">SENIOR</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">JavaScript</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">React</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Node.js</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->3<!-- --> more</span></div><div class="flex items-center justify-between"><div class="text-sm text-gray-500">Posted by <!-- -->John Smith<!-- --> • <!-- -->2024-01-20<!-- --> • <!-- -->45<!-- --> applications</div><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Apply</button></div></div></div><div class="text-right ml-6"><div class="text-lg font-bold text-green-600">$<!-- -->120,000<!-- --> - $<!-- -->180,000</div><div class="text-sm text-gray-500">Deadline: <!-- -->2024-03-15</div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="p-6 pt-6"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-xl font-semibold">Product Manager</h3><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">ACTIVE</span></div><div class="flex items-center space-x-4 text-gray-600 mb-3"><span class="font-medium">InnovateTech Startup</span><span>•</span><span>Austin<!-- -->, <!-- -->TX</span><span>•</span><span class="text-blue-600">Hybrid</span></div><p class="text-gray-600 mb-4">Join our dynamic startup as a Product Manager and help shape the future of our innovative platform.<!-- -->...</p><div class="flex flex-wrap gap-2 mb-4"><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">FULL TIME</span><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">MID</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Product Management</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Agile</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Analytics</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->2<!-- --> more</span></div><div class="flex items-center justify-between"><div class="text-sm text-gray-500">Posted by <!-- -->Michael Brown<!-- --> • <!-- -->2024-01-15<!-- --> • <!-- -->28<!-- --> applications</div><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Apply</button></div></div></div><div class="text-right ml-6"><div class="text-lg font-bold text-green-600">$<!-- -->90,000<!-- --> - $<!-- -->130,000</div><div class="text-sm text-gray-500">Deadline: <!-- -->2024-02-28</div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="p-6 pt-6"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><h3 class="text-xl font-semibold">Data Scientist Intern</h3><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">ACTIVE</span></div><div class="flex items-center space-x-4 text-gray-600 mb-3"><span class="font-medium">MedTech Solutions</span><span>•</span><span>Boston<!-- -->, <!-- -->MA</span><span>•</span><span class="text-green-600">Remote</span></div><p class="text-gray-600 mb-4">Summer internship opportunity for students interested in applying data science to healthcare challenges.<!-- -->...</p><div class="flex flex-wrap gap-2 mb-4"><span class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">INTERNSHIP</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">ENTRY</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Python</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">R</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Machine Learning</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->2<!-- --> more</span></div><div class="flex items-center justify-between"><div class="text-sm text-gray-500">Posted by <!-- -->Dr. Sarah Johnson<!-- --> • <!-- -->2024-01-10<!-- --> • <!-- -->67<!-- --> applications</div><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button><button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Apply</button></div></div></div><div class="text-right ml-6"><div class="text-lg font-bold text-green-600">$<!-- -->25<!-- --> - $<!-- -->30</div><div class="text-sm text-gray-500">Deadline: <!-- -->2024-03-01</div></div></div></div></div></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[1163,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"7308\",\"static/chunks/app/alumni/jobs/page-0fb049753b45415b.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\nb:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"Tn7xWRdXVWNLs4FpbaknK\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/alumni/jobs\",\"initialTree\":[\"\",{\"children\":[\"alumni\",{\"children\":[\"jobs\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"alumni\",{\"children\":[\"jobs\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"alumni\",\"children\",\"jobs\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"alumni\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$La\"],\"globalErrorComponent\":\"$b\"}]]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>