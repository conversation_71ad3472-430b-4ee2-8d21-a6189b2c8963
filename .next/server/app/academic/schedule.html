<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/academic/schedule/page-06ef7ac09dc918d6.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900">Class Scheduling</h1><p class="text-gray-600">Create and manage class timetables</p></div><div class="flex items-center space-x-2"><button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200">Export Schedule</button><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">+ Add Class</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 pt-6"><div class="grid grid-cols-1 md:grid-cols-5 gap-4"><div><label class="block text-sm font-medium text-gray-700 mb-2">Grade</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="Grade 9" selected="">Grade 9</option><option value="Grade 10">Grade 10</option><option value="Grade 11">Grade 11</option><option value="Grade 12">Grade 12</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Division</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="A" selected="">Division A</option><option value="B">Division B</option><option value="C">Division C</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Week</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="current" selected="">Current Week</option><option value="next">Next Week</option><option value="custom">Custom Range</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">View</label><div class="flex border border-gray-300 rounded-md"><button class="flex-1 px-3 py-2 text-sm bg-blue-600 text-white">Week</button><button class="flex-1 px-3 py-2 text-sm bg-white text-gray-700 hover:bg-gray-50">Day</button></div></div><div class="flex items-end"><button class="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">Publish Schedule</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="font-semibold tracking-tight text-lg">Class Types</h3></div><div class="p-6 pt-0"><div class="flex flex-wrap gap-4"><div class="flex items-center space-x-2"><div class="w-4 h-4 bg-blue-100 border border-blue-200 rounded"></div><span class="text-sm">Lecture</span></div><div class="flex items-center space-x-2"><div class="w-4 h-4 bg-green-100 border border-green-200 rounded"></div><span class="text-sm">Laboratory</span></div><div class="flex items-center space-x-2"><div class="w-4 h-4 bg-yellow-100 border border-yellow-200 rounded"></div><span class="text-sm">Tutorial</span></div><div class="flex items-center space-x-2"><div class="w-4 h-4 bg-red-100 border border-red-200 rounded"></div><span class="text-sm">Examination</span></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Grade 9<!-- --> - Division <!-- -->A<!-- --> Schedule</h3><p class="text-sm text-muted-foreground">Weekly<!-- --> class schedule</p></div><div class="p-6 pt-0"><div class="overflow-x-auto"><table class="w-full border-collapse border border-gray-300"><thead><tr class="bg-gray-50"><th class="border border-gray-300 p-3 text-left font-medium">Time</th><th class="border border-gray-300 p-3 text-center font-medium min-w-[150px]">Monday</th><th class="border border-gray-300 p-3 text-center font-medium min-w-[150px]">Tuesday</th><th class="border border-gray-300 p-3 text-center font-medium min-w-[150px]">Wednesday</th><th class="border border-gray-300 p-3 text-center font-medium min-w-[150px]">Thursday</th><th class="border border-gray-300 p-3 text-center font-medium min-w-[150px]">Friday</th><th class="border border-gray-300 p-3 text-center font-medium min-w-[150px]">Saturday</th></tr></thead><tbody><tr><td class="border border-gray-300 p-3 bg-gray-50 font-medium"><div class="text-sm">08:00<!-- --> - <!-- -->08:45</div><div class="text-xs text-gray-500">45<!-- --> min</div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="p-2 rounded border bg-blue-100 text-blue-800 border-blue-200 h-full cursor-pointer hover:shadow-sm"><div class="font-medium text-sm">Mathematics</div><div class="text-xs">Dr. John Smith</div><div class="text-xs">Room 101</div><div class="text-xs capitalize">lecture</div></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="p-2 rounded border bg-green-100 text-green-800 border-green-200 h-full cursor-pointer hover:shadow-sm"><div class="font-medium text-sm">Chemistry</div><div class="text-xs">Dr. Michael Brown</div><div class="text-xs">Lab 202</div><div class="text-xs capitalize">lab</div></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td></tr><tr><td class="border border-gray-300 p-3 bg-gray-50 font-medium"><div class="text-sm">08:45<!-- --> - <!-- -->09:30</div><div class="text-xs text-gray-500">45<!-- --> min</div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="p-2 rounded border bg-green-100 text-green-800 border-green-200 h-full cursor-pointer hover:shadow-sm"><div class="font-medium text-sm">Physics</div><div class="text-xs">Dr. Sarah Wilson</div><div class="text-xs">Lab 201</div><div class="text-xs capitalize">lab</div></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="p-2 rounded border bg-blue-100 text-blue-800 border-blue-200 h-full cursor-pointer hover:shadow-sm"><div class="font-medium text-sm">History</div><div class="text-xs">Prof. Lisa Garcia</div><div class="text-xs">Room 103</div><div class="text-xs capitalize">lecture</div></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td></tr><tr><td class="border border-gray-300 p-3 bg-gray-50 font-medium"><div class="text-sm">09:45<!-- --> - <!-- -->10:30</div><div class="text-xs text-gray-500">45<!-- --> min</div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="p-2 rounded border bg-yellow-100 text-yellow-800 border-yellow-200 h-full cursor-pointer hover:shadow-sm"><div class="font-medium text-sm">Mathematics</div><div class="text-xs">Dr. John Smith</div><div class="text-xs">Room 101</div><div class="text-xs capitalize">tutorial</div></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td></tr><tr><td class="border border-gray-300 p-3 bg-gray-50 font-medium"><div class="text-sm">10:30<!-- --> - <!-- -->11:15</div><div class="text-xs text-gray-500">45<!-- --> min</div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="p-2 rounded border bg-blue-100 text-blue-800 border-blue-200 h-full cursor-pointer hover:shadow-sm"><div class="font-medium text-sm">English</div><div class="text-xs">Prof. Emily Davis</div><div class="text-xs">Room 102</div><div class="text-xs capitalize">lecture</div></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td></tr><tr><td class="border border-gray-300 p-3 bg-gray-50 font-medium"><div class="text-sm">11:30<!-- --> - <!-- -->12:15</div><div class="text-xs text-gray-500">45<!-- --> min</div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td></tr><tr><td class="border border-gray-300 p-3 bg-gray-50 font-medium"><div class="text-sm">12:15<!-- --> - <!-- -->13:00</div><div class="text-xs text-gray-500">45<!-- --> min</div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td></tr><tr><td class="border border-gray-300 p-3 bg-gray-50 font-medium"><div class="text-sm">14:00<!-- --> - <!-- -->14:45</div><div class="text-xs text-gray-500">45<!-- --> min</div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td></tr><tr><td class="border border-gray-300 p-3 bg-gray-50 font-medium"><div class="text-sm">14:45<!-- --> - <!-- -->15:30</div><div class="text-xs text-gray-500">45<!-- --> min</div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td><td class="border border-gray-300 p-2 h-20 align-top"><div class="h-full flex items-center justify-center"><button class="text-gray-400 hover:text-gray-600 text-xs">+ Add Class</button></div></td></tr></tbody></table></div></div></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[9845,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"3598\",\"static/chunks/app/academic/schedule/page-06ef7ac09dc918d6.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\nb:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"Tn7xWRdXVWNLs4FpbaknK\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/academic/schedule\",\"initialTree\":[\"\",{\"children\":[\"academic\",{\"children\":[\"schedule\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"academic\",{\"children\":[\"schedule\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"academic\",\"children\",\"schedule\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"academic\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$La\"],\"globalErrorComponent\":\"$b\"}]]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>