<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/academic/progress/page-7ad6410ff1197fb2.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900">Student Progress Reports</h1><p class="text-gray-600">Generate and view comprehensive student progress reports</p></div><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Bulk Generate Reports</button></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 pt-6"><div class="grid grid-cols-1 md:grid-cols-3 gap-4"><div><label class="block text-sm font-medium text-gray-700 mb-2">Class</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="Grade 11 - A" selected="">Grade 11 - Division A</option><option value="Grade 11 - B">Grade 11 - Division B</option><option value="Grade 10 - A">Grade 10 - Division A</option><option value="Grade 10 - B">Grade 10 - Division B</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Report Type</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="comprehensive" selected="">Comprehensive Report</option><option value="academic">Academic Performance Only</option><option value="attendance">Attendance Report</option><option value="behavioral">Behavioral Assessment</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Time Period</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="current_term">Current Term</option><option value="semester">Full Semester</option><option value="academic_year">Academic Year</option><option value="custom">Custom Range</option></select></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Students - <!-- -->Grade 11 - A</h3><p class="text-sm text-muted-foreground">Select a student to view detailed progress report</p></div><div class="p-6 pt-0"><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-center justify-between mb-3"><div><h3 class="font-medium">John Smith</h3><p class="text-sm text-gray-600">11A001</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">3.7</div><div class="text-xs text-gray-500">GPA</div></div></div><div class="grid grid-cols-2 gap-2 text-sm mb-3"><div><span class="text-gray-600">Attendance:</span><span class="ml-1 font-medium text-green-600">92%</span></div><div><span class="text-gray-600">Rank:</span><span class="ml-1 font-medium">3rd</span></div></div><div class="flex space-x-2"><button class="flex-1 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View Progress</button><button class="flex-1 bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200">Generate Report</button></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-center justify-between mb-3"><div><h3 class="font-medium">Sarah Johnson</h3><p class="text-sm text-gray-600">11A002</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">3.7</div><div class="text-xs text-gray-500">GPA</div></div></div><div class="grid grid-cols-2 gap-2 text-sm mb-3"><div><span class="text-gray-600">Attendance:</span><span class="ml-1 font-medium text-green-600">92%</span></div><div><span class="text-gray-600">Rank:</span><span class="ml-1 font-medium">3rd</span></div></div><div class="flex space-x-2"><button class="flex-1 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View Progress</button><button class="flex-1 bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200">Generate Report</button></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-center justify-between mb-3"><div><h3 class="font-medium">Michael Brown</h3><p class="text-sm text-gray-600">11A003</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">3.7</div><div class="text-xs text-gray-500">GPA</div></div></div><div class="grid grid-cols-2 gap-2 text-sm mb-3"><div><span class="text-gray-600">Attendance:</span><span class="ml-1 font-medium text-green-600">92%</span></div><div><span class="text-gray-600">Rank:</span><span class="ml-1 font-medium">3rd</span></div></div><div class="flex space-x-2"><button class="flex-1 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View Progress</button><button class="flex-1 bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200">Generate Report</button></div></div><div class="border rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-center justify-between mb-3"><div><h3 class="font-medium">Emily Davis</h3><p class="text-sm text-gray-600">11A004</p></div><div class="text-right"><div class="text-lg font-bold text-blue-600">3.7</div><div class="text-xs text-gray-500">GPA</div></div></div><div class="grid grid-cols-2 gap-2 text-sm mb-3"><div><span class="text-gray-600">Attendance:</span><span class="ml-1 font-medium text-green-600">92%</span></div><div><span class="text-gray-600">Rank:</span><span class="ml-1 font-medium">3rd</span></div></div><div class="flex space-x-2"><button class="flex-1 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">View Progress</button><button class="flex-1 bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200">Generate Report</button></div></div></div></div></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[2414,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"74\",\"static/chunks/app/academic/progress/page-7ad6410ff1197fb2.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\nb:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"Tn7xWRdXVWNLs4FpbaknK\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/academic/progress\",\"initialTree\":[\"\",{\"children\":[\"academic\",{\"children\":[\"progress\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"academic\",{\"children\":[\"progress\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"academic\",\"children\",\"progress\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"academic\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$La\"],\"globalErrorComponent\":\"$b\"}]]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>