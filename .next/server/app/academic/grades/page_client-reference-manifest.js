globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/academic/grades/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"422":{"*":{"id":"21332","name":"*","chunks":[],"async":false}},"1514":{"*":{"id":"28123","name":"*","chunks":[],"async":false}},"1778":{"*":{"id":"9295","name":"*","chunks":[],"async":false}},"1902":{"*":{"id":"13225","name":"*","chunks":[],"async":false}},"5250":{"*":{"id":"61476","name":"*","chunks":[],"async":false}},"5613":{"*":{"id":"38771","name":"*","chunks":[],"async":false}},"7690":{"*":{"id":"2583","name":"*","chunks":[],"async":false}},"7831":{"*":{"id":"43982","name":"*","chunks":[],"async":false}},"8955":{"*":{"id":"26840","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/components/app-router.js":{"id":7690,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/components/app-router.js":{"id":7690,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/components/error-boundary.js":{"id":8955,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":8955,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/components/layout-router.js":{"id":5613,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/components/layout-router.js":{"id":5613,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/components/not-found-boundary.js":{"id":1902,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":1902,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/components/render-from-template-context.js":{"id":1778,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":1778,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"id":7831,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/components/static-generation-searchparams-bailout-provider.js":{"id":7831,"name":"*","chunks":[],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":9646,"name":"*","chunks":["3185","static/chunks/app/layout-42225a73217736f3.js"],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/globals.css":{"id":9103,"name":"*","chunks":["3185","static/chunks/app/layout-42225a73217736f3.js"],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/client/link.js":{"id":5250,"name":"*","chunks":["5250","static/chunks/5250-c6905a3e4a59cbd2.js","1931","static/chunks/app/page-4cda9109a41ae052.js"],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/node_modules/next/dist/esm/client/link.js":{"id":5250,"name":"*","chunks":["5250","static/chunks/5250-c6905a3e4a59cbd2.js","1931","static/chunks/app/page-4cda9109a41ae052.js"],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/modules/academic-learning/components/AcademicDashboard.tsx":{"id":1514,"name":"*","chunks":["7895","static/chunks/7895-dcf2d3b63c7719ea.js","5250","static/chunks/5250-c6905a3e4a59cbd2.js","7525","static/chunks/app/academic/page-babaa46a4202124c.js"],"async":false},"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/academic/grades/page.tsx":{"id":422,"name":"*","chunks":["7895","static/chunks/7895-dcf2d3b63c7719ea.js","112","static/chunks/app/academic/grades/page-8863981b57986b8e.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/_not-found":[],"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/layout":["static/css/1efa94dc89f20134.css"],"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/page":[],"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/academic/page":[],"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/academic/grades/page":[]}}