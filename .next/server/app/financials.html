<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/financials/page-0d07695fc2a6fe6b.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Financial Dashboard</h1><p class="text-muted-foreground">Student fee management and financial overview</p></div><div class="flex items-center space-x-2"><button class="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90">Generate Report</button><button class="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90">Record Payment</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Total Revenue</h3><span class="text-2xl">💰</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">$1,250,000.00</div><p class="text-xs text-muted-foreground">+12% from last month</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Pending Payments</h3><span class="text-2xl">⏳</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">$85,000.00</div><p class="text-xs text-muted-foreground">From <!-- -->1250<!-- --> students</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Overdue</h3><span class="text-2xl">🚨</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">$25,000.00</div><p class="text-xs text-muted-foreground">Requires attention</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Students</h3><span class="text-2xl">👥</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">1250</div><p class="text-xs text-muted-foreground">Active enrollment</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Collection Rate</h3><span class="text-2xl">📊</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">92.5<!-- -->%</div><p class="text-xs text-muted-foreground">This academic year</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Avg Fee/Student</h3><span class="text-2xl">💳</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">$1,000.00</div><p class="text-xs text-muted-foreground">Per month</p></div></div></div><div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"><div class="rounded-lg border bg-card text-card-foreground shadow-sm lg:col-span-2"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Transactions</h3><p class="text-sm text-muted-foreground">Latest payment transactions</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-primary">JS</span></div><div><p class="font-medium">John Smith</p><p class="text-sm text-muted-foreground">Online</p></div></div><div class="text-right"><p class="font-medium">$1,500.00</p><div class="flex items-center space-x-2"><span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">completed</span><span class="text-xs text-muted-foreground">2024-01-15</span></div></div></div><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-primary">SJ</span></div><div><p class="font-medium">Sarah Johnson</p><p class="text-sm text-muted-foreground">Bank Transfer</p></div></div><div class="text-right"><p class="font-medium">$2,000.00</p><div class="flex items-center space-x-2"><span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">completed</span><span class="text-xs text-muted-foreground">2024-01-15</span></div></div></div><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-primary">MB</span></div><div><p class="font-medium">Michael Brown</p><p class="text-sm text-muted-foreground">Cash</p></div></div><div class="text-right"><p class="font-medium">$1,200.00</p><div class="flex items-center space-x-2"><span class="text-xs px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">pending</span><span class="text-xs text-muted-foreground">2024-01-14</span></div></div></div><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm font-medium text-primary">ED</span></div><div><p class="font-medium">Emily Davis</p><p class="text-sm text-muted-foreground">Credit Card</p></div></div><div class="text-right"><p class="font-medium">$1,800.00</p><div class="flex items-center space-x-2"><span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">completed</span><span class="text-xs text-muted-foreground">2024-01-14</span></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Fee Distribution</h3><p class="text-sm text-muted-foreground">Revenue by fee type</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><div class="w-3 h-3 rounded-full bg-primary" style="opacity:1"></div><span class="text-sm">Tuition</span></div><div class="text-right"><p class="text-sm font-medium">$850,000.00</p><p class="text-xs text-muted-foreground">68<!-- -->%</p></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><div class="w-3 h-3 rounded-full bg-primary" style="opacity:0.85"></div><span class="text-sm">Transport</span></div><div class="text-right"><p class="text-sm font-medium">$180,000.00</p><p class="text-xs text-muted-foreground">14.4<!-- -->%</p></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><div class="w-3 h-3 rounded-full bg-primary" style="opacity:0.7"></div><span class="text-sm">Laboratory</span></div><div class="text-right"><p class="text-sm font-medium">$95,000.00</p><p class="text-xs text-muted-foreground">7.6<!-- -->%</p></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><div class="w-3 h-3 rounded-full bg-primary" style="opacity:0.55"></div><span class="text-sm">Library</span></div><div class="text-right"><p class="text-sm font-medium">$65,000.00</p><p class="text-xs text-muted-foreground">5.2<!-- -->%</p></div></div><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><div class="w-3 h-3 rounded-full bg-primary" style="opacity:0.4"></div><span class="text-sm">Sports</span></div><div class="text-right"><p class="text-sm font-medium">$60,000.00</p><p class="text-xs text-muted-foreground">4.8<!-- -->%</p></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Overdue Payments</h3><p class="text-sm text-muted-foreground">Students with overdue fee payments</p></div><div class="p-6 pt-0"><div class="overflow-x-auto"><table class="w-full table-auto"><thead><tr class="border-b border-gray-200"><th class="text-left py-3 px-4 font-medium text-gray-900">Student</th><th class="text-left py-3 px-4 font-medium text-gray-900">Class</th><th class="text-left py-3 px-4 font-medium text-gray-900">Amount</th><th class="text-left py-3 px-4 font-medium text-gray-900">Days Overdue</th><th class="text-left py-3 px-4 font-medium text-gray-900">Actions</th></tr></thead><tbody><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-gray-900">Alex Wilson</span></td><td class="py-3 px-4"><span class="text-gray-900">Grade 9A</span></td><td class="py-3 px-4"><span class="font-medium text-red-600">$2,500.00</span></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">15<!-- --> days</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">Send Reminder</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">View Details</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-gray-900">Lisa Garcia</span></td><td class="py-3 px-4"><span class="text-gray-900">Grade 10B</span></td><td class="py-3 px-4"><span class="font-medium text-red-600">$1,800.00</span></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">8<!-- --> days</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">Send Reminder</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">View Details</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-gray-900">David Miller</span></td><td class="py-3 px-4"><span class="text-gray-900">Grade 8A</span></td><td class="py-3 px-4"><span class="font-medium text-red-600">$3,200.00</span></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">22<!-- --> days</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">Send Reminder</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">View Details</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-gray-900">Maria Rodriguez</span></td><td class="py-3 px-4"><span class="text-gray-900">Grade 11A</span></td><td class="py-3 px-4"><span class="font-medium text-red-600">$1,500.00</span></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">5<!-- --> days</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">Send Reminder</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">View Details</button></div></td></tr></tbody></table></div></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">💳</span><h3 class="font-semibold tracking-tight text-lg">Fee Management</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Manage fee structures and billing</p><button class="text-sm font-medium text-primary hover:text-primary/80">Manage Fees →</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">💰</span><h3 class="font-semibold tracking-tight text-lg">Payments</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Process and track payments</p><button class="text-sm font-medium text-primary hover:text-primary/80">View Payments →</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">🎓</span><h3 class="font-semibold tracking-tight text-lg">Financial Aid</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Manage scholarships and aid</p><button class="text-sm font-medium text-primary hover:text-primary/80">Manage Aid →</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📊</span><h3 class="font-semibold tracking-tight text-lg">Reports</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Financial reports and analytics</p><button class="text-sm font-medium text-primary hover:text-primary/80">View Reports →</button></div></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6"><a href="/financials/fee-structure"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">💰</span><h3 class="font-semibold tracking-tight text-lg">Fee Structure</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Setup fee categories</p><span class="text-sm font-medium text-primary hover:text-primary/80">Manage Fees →</span></div></div></a><a href="/financials/billing"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📄</span><h3 class="font-semibold tracking-tight text-lg">Billing</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Generate student bills</p><span class="text-sm font-medium text-primary hover:text-primary/80">Create Bills →</span></div></div></a><a href="/financials/payments"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">💳</span><h3 class="font-semibold tracking-tight text-lg">Payments</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Process payments</p><span class="text-sm font-medium text-primary hover:text-primary/80">Process Payments →</span></div></div></a><a href="/financials/financial-aid"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">🎓</span><h3 class="font-semibold tracking-tight text-lg">Financial Aid</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Scholarships &amp; aid</p><span class="text-sm font-medium text-primary hover:text-primary/80">Manage Aid →</span></div></div></a><a href="/financials/reports"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📊</span><h3 class="font-semibold tracking-tight text-lg">Reports</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Financial analytics</p><span class="text-sm font-medium text-primary hover:text-primary/80">View Reports →</span></div></div></a><a href="/financials/history"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📋</span><h3 class="font-semibold tracking-tight text-lg">Payment History</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Transaction records</p><span class="text-sm font-medium text-primary hover:text-primary/80">View History →</span></div></div></a></div></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[8446,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"2478\",\"static/chunks/app/financials/page-0d07695fc2a6fe6b.js\"],\"FinancialsDashboard\"]\n7:I[5613,[],\"\"]\n8:I[1778,[],\"\"]\na:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"DEc8SW0ph9C_XxX68bz_T\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/financials\",\"initialTree\":[\"\",{\"children\":[\"financials\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"financials\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"div\",null,{\"className\":\"space-y-6\",\"children\":[\"$\",\"$L6\",null,{\"tenantId\":\"demo-tenant-uuid-1234567890\"}]}],null]]},[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"financials\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$L9\"],\"globalErrorComponent\":\"$a\"}]]\n"])</script><script>self.__next_f.push([1,"9:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>