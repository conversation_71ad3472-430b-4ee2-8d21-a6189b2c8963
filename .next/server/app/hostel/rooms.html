<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/8487-51766b147f11e249.js" async=""></script><script src="/_next/static/chunks/app/hostel/rooms/page-eae11f52647381a0.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900">Room Allocation</h1><p class="text-gray-600">Manage hostel rooms and student allocations</p></div><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">+ Add Room</button></div><div class="grid gap-4 md:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Total Rooms</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-blue-600">3</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Available Rooms</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-green-600">2</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Occupancy Rate</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-purple-600">50<!-- -->%</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Pending Requests</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-orange-600">1</div></div></div></div><div class="border-b border-gray-200"><nav class="-mb-px flex space-x-8"><button class="py-2 px-1 border-b-2 font-medium text-sm border-blue-500 text-blue-600">🏠 Rooms (<!-- -->3<!-- -->)</button><button class="py-2 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700">📋 Allocation Requests (<!-- -->3<!-- -->)</button></nav></div><div><div class="space-y-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 pt-6"><div class="grid grid-cols-1 md:grid-cols-5 gap-4"><div><label class="block text-sm font-medium text-gray-700 mb-2">Search</label><input type="text" placeholder="Search by room number, block, or resident" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value=""/></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Block</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Blocks</option><option value="Block A">Block A</option><option value="Block B">Block B</option><option value="Block C">Block C</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Status</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Status</option><option value="available">Available</option><option value="occupied">Occupied</option><option value="maintenance">Maintenance</option><option value="reserved">Reserved</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Room Type</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Types</option><option value="single">Single</option><option value="double">Double</option><option value="triple">Triple</option><option value="quad">Quad</option><option value="suite">Suite</option></select></div><div class="flex items-end"><button class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200">Export Rooms</button></div></div></div></div><div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center justify-between"><h3 class="font-semibold tracking-tight text-lg">A-101</h3><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">OCCUPIED</span></div><p class="text-sm text-muted-foreground">Block A<!-- --> • Floor <!-- -->1</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">DOUBLE</span><span class="text-sm font-medium">₹<!-- -->15,000<!-- -->/month</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Occupancy:</span><span class="font-medium">2<!-- -->/<!-- -->2</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Bathroom:</span><span class="font-medium">attached</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">AC:</span><span class="font-medium">✅</span></div><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Wi-Fi</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">AC</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Study Table</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->1</span></div><div class="flex items-center justify-between"><span class="text-xs text-gray-500">Next inspection: <!-- -->2024-02-15</span><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center justify-between"><h3 class="font-semibold tracking-tight text-lg">A-102</h3><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">AVAILABLE</span></div><p class="text-sm text-muted-foreground">Block A<!-- --> • Floor <!-- -->1</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">SINGLE</span><span class="text-sm font-medium">₹<!-- -->20,000<!-- -->/month</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Occupancy:</span><span class="font-medium">0<!-- -->/<!-- -->1</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Bathroom:</span><span class="font-medium">attached</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">AC:</span><span class="font-medium">✅</span></div><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Wi-Fi</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">AC</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Study Table</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->1</span></div><div class="flex items-center justify-between"><span class="text-xs text-gray-500">Next inspection: <!-- -->2024-02-10</span><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center justify-between"><h3 class="font-semibold tracking-tight text-lg">B-201</h3><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">OCCUPIED</span></div><p class="text-sm text-muted-foreground">Block B<!-- --> • Floor <!-- -->2</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">TRIPLE</span><span class="text-sm font-medium">₹<!-- -->12,000<!-- -->/month</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Occupancy:</span><span class="font-medium">1<!-- -->/<!-- -->3</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Bathroom:</span><span class="font-medium">shared</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">AC:</span><span class="font-medium">❌</span></div><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Wi-Fi</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Fan</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Study Table</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->1</span></div><div class="flex items-center justify-between"><span class="text-xs text-gray-500">Next inspection: <!-- -->2024-02-20</span><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div></div></div></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[6653,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"8487\",\"static/chunks/8487-51766b147f11e249.js\",\"9144\",\"static/chunks/app/hostel/rooms/page-eae11f52647381a0.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\nb:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"az92YWeqjFDK2ONnGo-1U\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/hostel/rooms\",\"initialTree\":[\"\",{\"children\":[\"hostel\",{\"children\":[\"rooms\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"hostel\",{\"children\":[\"rooms\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"hostel\",\"children\",\"rooms\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"hostel\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$La\"],\"globalErrorComponent\":\"$b\"}]]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>