<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/663-bbab49efe859e074.js" async=""></script><script src="/_next/static/chunks/app/hostel/facilities/page-a6e709309ab2a199.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900">Facility Management</h1><p class="text-gray-600">Manage hostel facilities and bookings</p></div><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">+ Add Facility</button></div><div class="grid gap-4 md:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Total Facilities</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-blue-600">4</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Available</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-green-600">4</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Active Bookings</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-purple-600">2</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Utilization Rate</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-orange-600">78%</div><p class="text-xs text-gray-500">This month</p></div></div></div><div class="border-b border-gray-200"><nav class="-mb-px flex space-x-8"><button class="py-2 px-1 border-b-2 font-medium text-sm border-blue-500 text-blue-600">🏢 Facilities (<!-- -->4<!-- -->)</button><button class="py-2 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700">📅 Bookings (<!-- -->2<!-- -->)</button></nav></div><div><div class="space-y-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 pt-6"><div class="grid grid-cols-1 md:grid-cols-4 gap-4"><div><label class="block text-sm font-medium text-gray-700 mb-2">Search</label><input type="text" placeholder="Search by name, location, or amenities" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value=""/></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Type</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Types</option><option value="common_room">Common Room</option><option value="study_hall">Study Hall</option><option value="gym">Gym</option><option value="laundry">Laundry</option><option value="kitchen">Kitchen</option><option value="parking">Parking</option><option value="garden">Garden</option><option value="sports">Sports</option><option value="other">Other</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Status</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Status</option><option value="available">Available</option><option value="occupied">Occupied</option><option value="maintenance">Maintenance</option><option value="closed">Closed</option></select></div><div class="flex items-end"><button class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200">Export Facilities</button></div></div></div></div><div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><span class="text-2xl">🛋️</span><h3 class="font-semibold tracking-tight text-lg">Main Common Room</h3></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">AVAILABLE</span></div><p class="text-sm text-muted-foreground">Ground Floor, Block A</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex justify-between text-sm"><span class="text-gray-600">Capacity:</span><span class="font-medium">50</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Current Occupancy:</span><span class="font-medium">12</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Booking Required:</span><span class="font-medium">No</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Operating Hours:</span><span class="font-medium">06:00<!-- --> - <!-- -->23:00</span></div><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">TV</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Sofa Sets</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Air Conditioning</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->2</span></div><div class="flex items-center justify-between"><span class="text-xs text-gray-500">Next maintenance: <!-- -->2024-02-15</span><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><span class="text-2xl">📚</span><h3 class="font-semibold tracking-tight text-lg">Study Hall</h3></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">AVAILABLE</span></div><p class="text-sm text-muted-foreground">First Floor, Block B</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex justify-between text-sm"><span class="text-gray-600">Capacity:</span><span class="font-medium">80</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Current Occupancy:</span><span class="font-medium">45</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Booking Required:</span><span class="font-medium">No</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Operating Hours:</span><span class="font-medium">05:00<!-- --> - <!-- -->24:00</span></div><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Individual Study Tables</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Reading Lamps</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Air Conditioning</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->2</span></div><div class="flex items-center justify-between"><span class="text-xs text-gray-500">Next maintenance: <!-- -->2024-02-20</span><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><span class="text-2xl">💪</span><h3 class="font-semibold tracking-tight text-lg">Fitness Gym</h3></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">AVAILABLE</span></div><p class="text-sm text-muted-foreground">Basement, Block A</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex justify-between text-sm"><span class="text-gray-600">Capacity:</span><span class="font-medium">25</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Current Occupancy:</span><span class="font-medium">8</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Booking Required:</span><span class="font-medium">Yes</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Operating Hours:</span><span class="font-medium">05:00<!-- --> - <!-- -->22:00</span></div><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Cardio Equipment</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Weight Training</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Yoga Mats</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->2</span></div><div class="flex items-center justify-between"><span class="text-xs text-gray-500">Next maintenance: <!-- -->2024-02-25</span><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><span class="text-2xl">👕</span><h3 class="font-semibold tracking-tight text-lg">Laundry Room</h3></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">AVAILABLE</span></div><p class="text-sm text-muted-foreground">Ground Floor, Block C</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex justify-between text-sm"><span class="text-gray-600">Capacity:</span><span class="font-medium">12</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Current Occupancy:</span><span class="font-medium">3</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Booking Required:</span><span class="font-medium">Yes</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Operating Hours:</span><span class="font-medium">06:00<!-- --> - <!-- -->22:00</span></div><div class="flex flex-wrap gap-1"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Washing Machines</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Dryers</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Ironing Boards</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+<!-- -->1</span></div><div class="flex items-center justify-between"><span class="text-xs text-gray-500">Next maintenance: <!-- -->2024-02-28</span><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div></div></div></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[1984,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"663\",\"static/chunks/663-bbab49efe859e074.js\",\"3997\",\"static/chunks/app/hostel/facilities/page-a6e709309ab2a199.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\nb:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"az92YWeqjFDK2ONnGo-1U\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/hostel/facilities\",\"initialTree\":[\"\",{\"children\":[\"hostel\",{\"children\":[\"facilities\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"hostel\",{\"children\":[\"facilities\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"hostel\",\"children\",\"facilities\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"hostel\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$La\"],\"globalErrorComponent\":\"$b\"}]]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>