<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/1939-979f7277144a5890.js" async=""></script><script src="/_next/static/chunks/app/hostel/maintenance/page-10667926c77876c9.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900">Maintenance Requests</h1><p class="text-gray-600">Manage hostel maintenance and repair requests</p></div><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">+ New Request</button></div><div class="grid gap-4 md:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Total Requests</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-blue-600">3</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Pending</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-yellow-600">2</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Urgent</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-red-600">1</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Avg Resolution</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-green-600">2.5</div><p class="text-xs text-gray-500">Days</p></div></div></div><div class="border-b border-gray-200"><nav class="-mb-px flex space-x-8"><button class="py-2 px-1 border-b-2 font-medium text-sm border-blue-500 text-blue-600">🔧 Requests (<!-- -->3<!-- -->)</button><button class="py-2 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700">👷 Staff (<!-- -->3<!-- -->)</button></nav></div><div><div class="space-y-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 pt-6"><div class="grid grid-cols-1 md:grid-cols-5 gap-4"><div><label class="block text-sm font-medium text-gray-700 mb-2">Search</label><input type="text" placeholder="Search by title, student, room, or ID" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value=""/></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Category</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Categories</option><option value="electrical">Electrical</option><option value="plumbing">Plumbing</option><option value="furniture">Furniture</option><option value="cleaning">Cleaning</option><option value="ac_heating">AC/Heating</option><option value="internet">Internet</option><option value="other">Other</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Status</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Status</option><option value="submitted">Submitted</option><option value="assigned">Assigned</option><option value="in_progress">In Progress</option><option value="completed">Completed</option><option value="cancelled">Cancelled</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Priority</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Priorities</option><option value="low">Low</option><option value="medium">Medium</option><option value="high">High</option><option value="urgent">Urgent</option></select></div><div class="flex items-end"><button class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200">Export Requests</button></div></div></div></div><div class="space-y-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="p-6 pt-6"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><span class="text-2xl">⚡</span><h3 class="text-lg font-semibold">Fan not working</h3><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">MEDIUM</span><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">ASSIGNED</span></div><div class="grid grid-cols-2 gap-4 mb-3"><div><p class="text-sm text-gray-600">Student: <!-- -->Rahul Sharma</p><p class="text-sm text-gray-600">Room: <!-- -->A-101<!-- --> (<!-- -->Block A<!-- -->)</p><p class="text-sm text-gray-600">Request ID: <!-- -->MNT-2024-001</p></div><div><p class="text-sm text-gray-600">Submitted: <!-- -->2024-02-01</p><p class="text-sm text-gray-600">Assigned to: <!-- -->Ravi Kumar (Electrician)</p><p class="text-sm text-gray-600">Est. Cost: ₹<!-- -->500</p></div></div><p class="text-sm text-gray-600 mb-4">The ceiling fan in my room has stopped working since yesterday. It makes a strange noise when switched on.<!-- -->...</p><div class="flex items-center justify-between"><div class="text-sm text-gray-500">Category: <!-- -->ELECTRICAL</div><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="p-6 pt-6"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><span class="text-2xl">🚿</span><h3 class="text-lg font-semibold">Bathroom tap leaking</h3><span class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">HIGH</span><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">COMPLETED</span></div><div class="grid grid-cols-2 gap-4 mb-3"><div><p class="text-sm text-gray-600">Student: <!-- -->Priya Singh</p><p class="text-sm text-gray-600">Room: <!-- -->B-201<!-- --> (<!-- -->Block B<!-- -->)</p><p class="text-sm text-gray-600">Request ID: <!-- -->MNT-2024-002</p></div><div><p class="text-sm text-gray-600">Submitted: <!-- -->2024-01-28</p><p class="text-sm text-gray-600">Assigned to: <!-- -->Suresh Yadav (Plumber)</p><p class="text-sm text-gray-600">Est. Cost: ₹<!-- -->200</p></div></div><p class="text-sm text-gray-600 mb-4">The bathroom tap has been leaking continuously for the past 3 days. Water is being wasted.<!-- -->...</p><div class="flex items-center justify-between"><div class="text-sm text-gray-500">Category: <!-- -->PLUMBING</div><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="p-6 pt-6"><div class="flex items-start justify-between"><div class="flex-1"><div class="flex items-center space-x-2 mb-2"><span class="text-2xl">📶</span><h3 class="text-lg font-semibold">Wi-Fi not working</h3><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">URGENT</span><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">SUBMITTED</span></div><div class="grid grid-cols-2 gap-4 mb-3"><div><p class="text-sm text-gray-600">Student: <!-- -->Amit Kumar</p><p class="text-sm text-gray-600">Room: <!-- -->A-101<!-- --> (<!-- -->Block A<!-- -->)</p><p class="text-sm text-gray-600">Request ID: <!-- -->MNT-2024-003</p></div><div><p class="text-sm text-gray-600">Submitted: <!-- -->2024-02-03</p></div></div><p class="text-sm text-gray-600 mb-4">Internet connection is very slow and frequently disconnects. Unable to attend online classes.<!-- -->...</p><div class="flex items-center justify-between"><div class="text-sm text-gray-500">Category: <!-- -->INTERNET</div><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button></div></div></div></div></div></div></div></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[9431,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"1939\",\"static/chunks/1939-979f7277144a5890.js\",\"57\",\"static/chunks/app/hostel/maintenance/page-10667926c77876c9.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\nb:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"az92YWeqjFDK2ONnGo-1U\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/hostel/maintenance\",\"initialTree\":[\"\",{\"children\":[\"hostel\",{\"children\":[\"maintenance\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"hostel\",{\"children\":[\"maintenance\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"hostel\",\"children\",\"maintenance\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"hostel\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$La\"],\"globalErrorComponent\":\"$b\"}]]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>