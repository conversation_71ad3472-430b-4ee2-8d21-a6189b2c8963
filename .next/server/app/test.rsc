2:I[5613,[],""]
3:I[1778,[],""]
0:["az92YWeqjFDK2ONnGo-1U",[[["",{"children":["test",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",{"children":["test",{"children":["__PAGE__",{},["$L1",["$","div",null,{"className":"min-h-screen flex items-center justify-center bg-gray-100","children":["$","div",null,{"className":"text-center","children":[["$","h1",null,{"className":"text-4xl font-bold text-gray-900 mb-4","children":"EMS Test Page"}],["$","p",null,{"className":"text-lg text-gray-600 mb-8","children":"If you can see this, the deployment is working!"}],["$","div",null,{"className":"space-y-4","children":[["$","div",null,{"className":"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded","children":"✅ Next.js App Router is working"}],["$","div",null,{"className":"bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded","children":"✅ Tailwind CSS is working"}],["$","div",null,{"className":"bg-purple-100 border border-purple-400 text-purple-700 px-4 py-3 rounded","children":"✅ TypeScript compilation is working"}]]}],["$","div",null,{"className":"mt-8","children":["$","a",null,{"href":"/","className":"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors","children":"Go to Home Page"}]}]]}]}],null]]},["$","$L2",null,{"parallelRouterKey":"children","segmentPath":["children","test","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}]]},[null,["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__className_e8ce0c","children":["$","$L2",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[],"styles":null}]}]}],null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/1efa94dc89f20134.css","precedence":"next","crossOrigin":""}]],"$L4"]]]]
4:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"EMS - Employee Management System"}],["$","meta","3",{"name":"description","content":"Comprehensive multi-tenant employee management system with modular architecture"}],["$","meta","4",{"name":"next-size-adjust"}]]
1:null
