<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/page-4cda9109a41ae052.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100"><div class="container mx-auto px-4 py-16"><div class="text-center"><h1 class="text-6xl font-bold text-gray-900 mb-8">EMS Platform</h1><p class="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">Employee Management System - Multi-Tenant Modular Architecture</p><div class="space-y-4 mb-12"><div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded max-w-md mx-auto">✅ Deployment Successful</div><div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded max-w-md mx-auto">✅ Next.js 14 App Router</div><div class="bg-purple-100 border border-purple-400 text-purple-700 px-4 py-3 rounded max-w-md mx-auto">✅ TypeScript &amp; Tailwind CSS</div></div><div class="space-x-4"><a class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-block" href="/dashboard">Go to Dashboard</a><a class="bg-gray-600 text-white px-8 py-3 rounded-lg hover:bg-gray-700 transition-colors inline-block" href="/test">Test Page</a></div><div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto"><div class="bg-white p-6 rounded-lg shadow-md"><h3 class="text-lg font-semibold mb-2">🏫 Multi-Tenant</h3><p class="text-gray-600">Complete data isolation between institutions</p></div><div class="bg-white p-6 rounded-lg shadow-md"><h3 class="text-lg font-semibold mb-2">🔧 Modular</h3><p class="text-gray-600">Enable/disable modules per tenant needs</p></div><div class="bg-white p-6 rounded-lg shadow-md"><h3 class="text-lg font-semibold mb-2">🔒 Secure</h3><p class="text-gray-600">Role-based access control and permissions</p></div></div></div></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[5250,[\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"1931\",\"static/chunks/app/page-4cda9109a41ae052.js\"],\"\"]\n7:I[5613,[],\"\"]\n8:I[1778,[],\"\"]\na:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"az92YWeqjFDK2ONnGo-1U\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/\",\"initialTree\":[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\"children\":[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4 py-16\",\"children\":[\"$\",\"div\",null,{\"className\":\"text-center\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-6xl font-bold text-gray-900 mb-8\",\"children\":\"EMS Platform\"}],[\"$\",\"p\",null,{\"className\":\"text-xl text-gray-600 mb-12 max-w-2xl mx-auto\",\"children\":\"Employee Management System - Multi-Tenant Modular Architecture\"}],[\"$\",\"div\",null,{\"className\":\"space-y-4 mb-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded max-w-md mx-auto\",\"children\":\"✅ Deployment Successful\"}],[\"$\",\"div\",null,{\"className\":\"bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded max-w-md mx-auto\",\"children\":\"✅ Next.js 14 App Router\"}],[\"$\",\"div\",null,{\"className\":\"bg-purple-100 border border-purple-400 text-purple-700 px-4 py-3 rounded max-w-md mx-auto\",\"children\":\"✅ TypeScript \u0026 Tailwind CSS\"}]]}],[\"$\",\"div\",null,{\"className\":\"space-x-4\",\"children\":[[\"$\",\"$L6\",null,{\"href\":\"/dashboard\",\"className\":\"bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-block\",\"children\":\"Go to Dashboard\"}],[\"$\",\"$L6\",null,{\"href\":\"/test\",\"className\":\"bg-gray-600 text-white px-8 py-3 rounded-lg hover:bg-gray-700 transition-colors inline-block\",\"children\":\"Test Page\"}]]}],[\"$\",\"div\",null,{\"className\":\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-white p-6 rounded-lg shadow-md\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-2\",\"children\":\"🏫 Multi-Tenant\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"Complete data isolation between institutions\"}]]}],[\"$\",\"div\",null,{\"className\":\"bg-white p-6 rounded-lg shadow-md\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-2\",\"children\":\"🔧 Modular\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"Enable/disable modules per tenant needs\"}]]}],[\"$\",\"div\",null,{\"className\":\"bg-white p-6 rounded-lg shadow-md\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-2\",\"children\":\"🔒 Secure\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"Role-based access control and permissions\"}]]}]]}]]}]}]}],null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$L9\"],\"globalErrorComponent\":\"$a\"}]]\n"])</script><script>self.__next_f.push([1,"9:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>