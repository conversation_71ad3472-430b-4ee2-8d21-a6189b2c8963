<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/library/inventory/page-69fc86e46e004d4f.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900">Inventory Tracking</h1><p class="text-gray-600">Track library resources and manage inventory</p></div><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Generate Full Report</button></div><div class="grid gap-4 md:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Total Items</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-blue-600">25</div><p class="text-xs text-gray-500">3<!-- --> unique titles</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Available</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-green-600">16</div><p class="text-xs text-gray-500">Ready for checkout</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Issues</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-red-600">2</div><p class="text-xs text-gray-500">Damaged or lost</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Total Value</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-purple-600">$<!-- -->2493.95</div><p class="text-xs text-gray-500">Collection value</p></div></div></div><div class="border-b border-gray-200"><nav class="-mb-px flex space-x-8"><button class="py-2 px-1 border-b-2 font-medium text-sm border-blue-500 text-blue-600">📚 Inventory (<!-- -->3<!-- -->)</button><button class="py-2 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700">🔍 Audit History (<!-- -->2<!-- -->)</button></nav></div><div><div class="space-y-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 pt-6"><div class="grid grid-cols-1 md:grid-cols-5 gap-4"><div><label class="block text-sm font-medium text-gray-700 mb-2">Search</label><input type="text" placeholder="Search by title, author, ISBN, or item ID" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value=""/></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Category</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Categories</option><option value="Mathematics">Mathematics</option><option value="Physics">Physics</option><option value="Chemistry">Chemistry</option><option value="Literature">Literature</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Condition</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Conditions</option><option value="excellent">Excellent</option><option value="good">Good</option><option value="fair">Fair</option><option value="poor">Poor</option><option value="damaged">Damaged</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Location</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Locations</option><option value="Main Library">Main Library</option><option value="Science Section">Science Section</option><option value="Periodicals Section">Periodicals Section</option><option value="Digital Library">Digital Library</option></select></div><div class="flex items-end"><button class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200">Export Report</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Inventory Items</h3><p class="text-sm text-muted-foreground">Showing <!-- -->3<!-- --> of <!-- -->3<!-- --> items</p></div><div class="p-6 pt-0"><div class="overflow-x-auto"><table class="w-full table-auto"><thead><tr class="border-b border-gray-200"><th class="text-left py-3 px-4 font-medium text-gray-900">Item</th><th class="text-left py-3 px-4 font-medium text-gray-900">Location</th><th class="text-left py-3 px-4 font-medium text-gray-900">Copies</th><th class="text-left py-3 px-4 font-medium text-gray-900">Condition</th><th class="text-left py-3 px-4 font-medium text-gray-900">Value</th><th class="text-left py-3 px-4 font-medium text-gray-900">Last Audit</th><th class="text-left py-3 px-4 font-medium text-gray-900">Actions</th></tr></thead><tbody><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><div><div class="font-medium">Advanced Mathematics for Engineers</div><div class="text-sm text-gray-600">Dr. John Smith</div><div class="text-sm text-gray-500">BOOK-001<!-- --> • <!-- -->978-0-123456-78-9</div></div></td><td class="py-3 px-4"><div><div class="font-medium">Main Library</div><div class="text-sm text-gray-600">MAT-001-A</div></div></td><td class="py-3 px-4"><div class="text-sm"><div>Total: <span class="font-medium">5</span></div><div>Available: <span class="text-green-600">3</span></div><div>Checked Out: <span class="text-blue-600">2</span></div></div></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">EXCELLENT</span></td><td class="py-3 px-4"><div><div class="font-medium">$<!-- -->449.95</div><div class="text-sm text-gray-600">$<!-- -->89.99<!-- --> each</div></div></td><td class="py-3 px-4 text-sm">2024-01-15</td><td class="py-3 px-4"><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">Update</button><button class="text-green-600 hover:text-green-800 text-sm font-medium">Audit</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><div><div class="font-medium">Physics Fundamentals</div><div class="text-sm text-gray-600">Dr. Sarah Wilson</div><div class="text-sm text-gray-500">BOOK-002<!-- --> • <!-- -->978-0-987654-32-1</div></div></td><td class="py-3 px-4"><div><div class="font-medium">Science Section</div><div class="text-sm text-gray-600">PHY-002-B</div></div></td><td class="py-3 px-4"><div class="text-sm"><div>Total: <span class="font-medium">8</span></div><div>Available: <span class="text-green-600">5</span></div><div>Checked Out: <span class="text-blue-600">2</span></div><div>Damaged: <span class="text-red-600">1</span></div></div></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">GOOD</span></td><td class="py-3 px-4"><div><div class="font-medium">$<!-- -->604.00</div><div class="text-sm text-gray-600">$<!-- -->75.50<!-- --> each</div></div></td><td class="py-3 px-4 text-sm">2024-01-10</td><td class="py-3 px-4"><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">Update</button><button class="text-green-600 hover:text-green-800 text-sm font-medium">Audit</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><div><div class="font-medium">Scientific Journal of Chemistry</div><div class="text-sm text-gray-600">Various Authors</div><div class="text-sm text-gray-500">JOUR-001<!-- --> • <!-- -->978-0-789123-45-6</div></div></td><td class="py-3 px-4"><div><div class="font-medium">Periodicals Section</div><div class="text-sm text-gray-600">JOU-CHE-001</div></div></td><td class="py-3 px-4"><div class="text-sm"><div>Total: <span class="font-medium">12</span></div><div>Available: <span class="text-green-600">8</span></div><div>Checked Out: <span class="text-blue-600">3</span></div><div>Lost: <span class="text-orange-600">1</span></div></div></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">GOOD</span></td><td class="py-3 px-4"><div><div class="font-medium">$<!-- -->1440.00</div><div class="text-sm text-gray-600">$<!-- -->120.00<!-- --> each</div></div></td><td class="py-3 px-4 text-sm">2024-01-05</td><td class="py-3 px-4"><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">Update</button><button class="text-green-600 hover:text-green-800 text-sm font-medium">Audit</button></div></td></tr></tbody></table></div></div></div></div></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[8321,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"4487\",\"static/chunks/app/library/inventory/page-69fc86e46e004d4f.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\nb:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"Tn7xWRdXVWNLs4FpbaknK\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/library/inventory\",\"initialTree\":[\"\",{\"children\":[\"library\",{\"children\":[\"inventory\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"library\",{\"children\":[\"inventory\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"library\",\"children\",\"inventory\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"library\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$La\"],\"globalErrorComponent\":\"$b\"}]]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>