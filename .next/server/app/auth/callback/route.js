(()=>{var e={};e.id=7936,e.ids=[7936],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},85477:e=>{"use strict";e.exports=require("punycode")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},57310:e=>{"use strict";e.exports=require("url")},59796:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},87602:(e,t,r)=>{"use strict";r.r(t),r.d(t,{headerHooks:()=>x,originalPathname:()=>y,patchFetch:()=>m,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>g});var s={};r.r(s),r.d(s,{GET:()=>l});var o=r(95419),a=r(69108),n=r(99678),u=r(57699),i=r(32455),c=r(78070);async function l(e){let t=new URL(e.url),r=t.searchParams.get("code"),s=t.searchParams.get("redirectTo")||"/dashboard";if(r){let t=(0,u.createRouteHandlerClient)({cookies:i.cookies});try{let{error:s}=await t.auth.exchangeCodeForSession(r);if(s)return console.error("Error exchanging code for session:",s),c.Z.redirect(new URL("/auth/login?error=auth_error",e.url))}catch(t){return console.error("Unexpected error during auth callback:",t),c.Z.redirect(new URL("/auth/login?error=auth_error",e.url))}}return c.Z.redirect(new URL(s,e.url))}let d=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/auth/callback/route",pathname:"/auth/callback",filename:"route",bundlePath:"app/auth/callback/route"},resolvedPagePath:"/Users/<USER>/Citrus-Works/Employee-Management-App/EMS-app/src/app/auth/callback/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:f,serverHooks:h,headerHooks:x,staticGenerationBailout:g}=d,y="/auth/callback/route";function m(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},48096:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_ERROR_CODE:function(){return r},DynamicServerError:function(){return s}});let r="DYNAMIC_SERVER_USAGE";class s extends Error{constructor(e){super("Dynamic server usage: "+e),this.digest=r}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},72973:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"staticGenerationBailout",{enumerable:!0,get:function(){return u}});let s=r(48096),o=r(45869);class a extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}function n(e,t){let{dynamic:r,link:s}=t||{};return"Page"+(r?' with `dynamic = "'+r+'"`':"")+" couldn't be rendered statically because it used `"+e+"`."+(s?" See more info here: "+s:"")}let u=(e,t)=>{let{dynamic:r,link:u}=void 0===t?{}:t,i=o.staticGenerationAsyncStorage.getStore();if(!i)return!1;if(i.forceStatic)return!0;if(i.dynamicShouldError)throw new a(n(e,{link:u,dynamic:null!=r?r:"error"}));let c=n(e,{dynamic:r,link:"https://nextjs.org/docs/messages/dynamic-server-error"});if(null==i.postpone||i.postpone.call(i,e),i.revalidate=0,i.isStaticGeneration){let t=new s.DynamicServerError(c);throw i.dynamicUsageDescription=e,i.dynamicUsageStack=t.stack,t}return!1};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,6349,6206],()=>r(87602));module.exports=s})();