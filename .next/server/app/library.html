<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/library/page-a15e34c07ea752bd.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Library Dashboard</h1><p class="text-muted-foreground">Digital and physical library management system</p></div><div class="flex items-center space-x-2"><button class="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90">Generate Report</button><button class="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90">Add Resource</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Total Books</h3><span class="text-2xl">📚</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">5,420</div><p class="text-xs text-muted-foreground">Physical &amp; Digital</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Members</h3><span class="text-2xl">👥</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">1,350</div><p class="text-xs text-muted-foreground">Active members</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Books Issued</h3><span class="text-2xl">📖</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">342</div><p class="text-xs text-muted-foreground">Currently checked out</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Overdue</h3><span class="text-2xl">⚠️</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">28</div><p class="text-xs text-muted-foreground">Requires attention</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Reservations</h3><span class="text-2xl">📋</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">15</div><p class="text-xs text-muted-foreground">Pending reservations</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Digital Resources</h3><span class="text-2xl">💻</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">1,250</div><p class="text-xs text-muted-foreground">E-books &amp; journals</p></div></div></div><div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"><div class="rounded-lg border bg-card text-card-foreground shadow-sm lg:col-span-2"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Activity</h3><p class="text-sm text-muted-foreground">Latest library transactions</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm">📤</span></div><div><p class="font-medium">John Smith</p><p class="text-sm text-muted-foreground">Introduction to Physics</p></div></div><div class="text-right"><p class="text-sm font-medium capitalize">checkout</p><p class="text-xs text-muted-foreground">2024-01-15</p></div></div><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm">📥</span></div><div><p class="font-medium">Sarah Johnson</p><p class="text-sm text-muted-foreground">Advanced Mathematics</p></div></div><div class="text-right"><p class="text-sm font-medium capitalize">return</p><p class="text-xs text-muted-foreground">2024-01-15</p></div></div><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm">📋</span></div><div><p class="font-medium">Michael Brown</p><p class="text-sm text-muted-foreground">World History</p></div></div><div class="text-right"><p class="text-sm font-medium capitalize">reservation</p><p class="text-xs text-muted-foreground">2024-01-14</p></div></div><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm">📤</span></div><div><p class="font-medium">Emily Davis</p><p class="text-sm text-muted-foreground">English Literature</p></div></div><div class="text-right"><p class="text-sm font-medium capitalize">checkout</p><p class="text-xs text-muted-foreground">2024-01-14</p></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Popular Books</h3><p class="text-sm text-muted-foreground">Most checked out this month</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Introduction to Computer Science</p><p class="text-xs text-muted-foreground">John Doe</p></div><span class="text-sm font-medium text-primary">45</span></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Advanced Mathematics</p><p class="text-xs text-muted-foreground">Jane Smith</p></div><span class="text-sm font-medium text-primary">38</span></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Physics Fundamentals</p><p class="text-xs text-muted-foreground">Robert Wilson</p></div><span class="text-sm font-medium text-primary">32</span></div><div class="flex items-center justify-between"><div><p class="font-medium text-sm">Chemistry Basics</p><p class="text-xs text-muted-foreground">Lisa Brown</p></div><span class="text-sm font-medium text-primary">28</span></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Overdue Items</h3><p class="text-sm text-muted-foreground">Books that need to be returned</p></div><div class="p-6 pt-0"><div class="overflow-x-auto"><table class="w-full table-auto"><thead><tr class="border-b border-gray-200"><th class="text-left py-3 px-4 font-medium text-gray-900">Member</th><th class="text-left py-3 px-4 font-medium text-gray-900">Book</th><th class="text-left py-3 px-4 font-medium text-gray-900">Days Overdue</th><th class="text-left py-3 px-4 font-medium text-gray-900">Fine</th><th class="text-left py-3 px-4 font-medium text-gray-900">Actions</th></tr></thead><tbody><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-gray-900">Alex Wilson</span></td><td class="py-3 px-4"><span class="text-gray-900">Biology Textbook</span></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">5<!-- --> days</span></td><td class="py-3 px-4"><span class="font-medium text-red-600">$<!-- -->2.50</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">Send Reminder</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Contact</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-gray-900">Maria Garcia</span></td><td class="py-3 px-4"><span class="text-gray-900">History of Art</span></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">12<!-- --> days</span></td><td class="py-3 px-4"><span class="font-medium text-red-600">$<!-- -->6.00</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">Send Reminder</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Contact</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-gray-900">David Miller</span></td><td class="py-3 px-4"><span class="text-gray-900">Programming Guide</span></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">3<!-- --> days</span></td><td class="py-3 px-4"><span class="font-medium text-red-600">$<!-- -->1.50</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">Send Reminder</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Contact</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-gray-900">Lisa Rodriguez</span></td><td class="py-3 px-4"><span class="text-gray-900">Geography Atlas</span></td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">8<!-- --> days</span></td><td class="py-3 px-4"><span class="font-medium text-red-600">$<!-- -->4.00</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">Send Reminder</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Contact</button></div></td></tr></tbody></table></div></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📚</span><h3 class="font-semibold tracking-tight text-lg">Catalog</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Manage library catalog</p><button class="text-sm font-medium text-primary hover:text-primary/80">Browse Catalog →</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">🔄</span><h3 class="font-semibold tracking-tight text-lg">Circulation</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Check-in/Check-out books</p><button class="text-sm font-medium text-primary hover:text-primary/80">Manage Circulation →</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">👥</span><h3 class="font-semibold tracking-tight text-lg">Members</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Manage library members</p><button class="text-sm font-medium text-primary hover:text-primary/80">View Members →</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📊</span><h3 class="font-semibold tracking-tight text-lg">Reports</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Library analytics and reports</p><button class="text-sm font-medium text-primary hover:text-primary/80">View Reports →</button></div></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6"><a href="/library/catalog"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📚</span><h3 class="font-semibold tracking-tight text-lg">Catalog</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Manage books &amp; resources</p><span class="text-sm font-medium text-primary hover:text-primary/80">Manage Catalog →</span></div></div></a><a href="/library/circulation"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">🔄</span><h3 class="font-semibold tracking-tight text-lg">Circulation</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Check-in &amp; check-out</p><span class="text-sm font-medium text-primary hover:text-primary/80">Manage Circulation →</span></div></div></a><a href="/library/members"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">👥</span><h3 class="font-semibold tracking-tight text-lg">Members</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Library memberships</p><span class="text-sm font-medium text-primary hover:text-primary/80">Manage Members →</span></div></div></a><a href="/library/reservations"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📋</span><h3 class="font-semibold tracking-tight text-lg">Reservations</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Book reservations</p><span class="text-sm font-medium text-primary hover:text-primary/80">Manage Reservations →</span></div></div></a><a href="/library/inventory"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📦</span><h3 class="font-semibold tracking-tight text-lg">Inventory</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Track resources</p><span class="text-sm font-medium text-primary hover:text-primary/80">Track Inventory →</span></div></div></a><a href="/library/digital"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">💻</span><h3 class="font-semibold tracking-tight text-lg">Digital Library</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">E-resources &amp; content</p><span class="text-sm font-medium text-primary hover:text-primary/80">Manage Digital →</span></div></div></a></div></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[6666,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"9103\",\"static/chunks/app/library/page-a15e34c07ea752bd.js\"],\"LibraryDashboard\"]\n7:I[5613,[],\"\"]\n8:I[1778,[],\"\"]\na:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"DEc8SW0ph9C_XxX68bz_T\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/library\",\"initialTree\":[\"\",{\"children\":[\"library\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"library\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"div\",null,{\"className\":\"space-y-6\",\"children\":[\"$\",\"$L6\",null,{\"tenantId\":\"demo-tenant-uuid-1234567890\"}]}],null]]},[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"library\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$L9\"],\"globalErrorComponent\":\"$a\"}]]\n"])</script><script>self.__next_f.push([1,"9:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>