<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/financials/billing/page-019d9d5b42c04e19.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900">Billing Generation</h1><p class="text-gray-600">Generate and manage student bills</p></div><div class="flex items-center space-x-2"><button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200">View All Bills</button><button disabled="" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed">Generate Bills (<!-- -->0<!-- -->)</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Bill Generation Settings</h3><p class="text-sm text-muted-foreground">Configure billing parameters and select students</p></div><div class="p-6 pt-0"><div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"><div><label class="block text-sm font-medium text-gray-700 mb-2">Class</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="Grade 11 - A" selected="">Grade 11 - Division A</option><option value="Grade 11 - B">Grade 11 - Division B</option><option value="Grade 10 - A">Grade 10 - Division A</option><option value="Grade 10 - B">Grade 10 - Division B</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Semester</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="Semester 1" selected="">Semester 1</option><option value="Semester 2">Semester 2</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Bill Type</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="semester" selected="">Semester Bill</option><option value="monthly">Monthly Bill</option><option value="annual">Annual Bill</option><option value="custom">Custom Bill</option></select></div><div class="flex items-end"><button class="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">Preview Bill</button></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Select Students</h3><p class="text-sm text-muted-foreground">Choose students for bill generation</p></div><div class="p-6 pt-0"><div class="mb-4"><label class="flex items-center"><input type="checkbox" class="mr-2"/><span class="font-medium">Select All Students (<!-- -->3<!-- -->)</span></label></div><div class="overflow-x-auto"><table class="w-full table-auto"><thead><tr class="border-b border-gray-200"><th class="text-left py-3 px-4 font-medium text-gray-900">Select</th><th class="text-left py-3 px-4 font-medium text-gray-900">Roll No.</th><th class="text-left py-3 px-4 font-medium text-gray-900">Student Name</th><th class="text-left py-3 px-4 font-medium text-gray-900">Grade</th><th class="text-left py-3 px-4 font-medium text-gray-900">Stream</th><th class="text-left py-3 px-4 font-medium text-gray-900">Parent Email</th><th class="text-left py-3 px-4 font-medium text-gray-900">Last Bill</th></tr></thead><tbody><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><input type="checkbox"/></td><td class="py-3 px-4 font-medium">11A001</td><td class="py-3 px-4">John Smith</td><td class="py-3 px-4">Grade 11</td><td class="py-3 px-4">Science</td><td class="py-3 px-4"><EMAIL></td><td class="py-3 px-4"><span class="text-sm text-gray-500">Jan 2024</span></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><input type="checkbox"/></td><td class="py-3 px-4 font-medium">11A002</td><td class="py-3 px-4">Sarah Johnson</td><td class="py-3 px-4">Grade 11</td><td class="py-3 px-4">Science</td><td class="py-3 px-4"><EMAIL></td><td class="py-3 px-4"><span class="text-sm text-gray-500">Jan 2024</span></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><input type="checkbox"/></td><td class="py-3 px-4 font-medium">11A003</td><td class="py-3 px-4">Michael Brown</td><td class="py-3 px-4">Grade 11</td><td class="py-3 px-4">Science</td><td class="py-3 px-4"><EMAIL></td><td class="py-3 px-4"><span class="text-sm text-gray-500">Jan 2024</span></td></tr></tbody></table></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Bills</h3><p class="text-sm text-muted-foreground">Recently generated bills</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="flex items-center justify-between p-4 border rounded-lg"><div class="flex items-center space-x-4"><div><div class="font-medium">BILL-2024-001</div><div class="text-sm text-gray-600">John Smith<!-- --> (<!-- -->11A001<!-- -->)</div></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">SENT</span></div><div class="text-right"><div class="font-medium">$<!-- -->14,625</div><div class="text-sm text-gray-500">Due: <!-- -->2024-02-15</div></div><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View</button><button class="text-green-600 hover:text-green-800 text-sm font-medium">Send</button></div></div></div></div></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[7567,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"1299\",\"static/chunks/app/financials/billing/page-019d9d5b42c04e19.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\nb:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"Tn7xWRdXVWNLs4FpbaknK\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/financials/billing\",\"initialTree\":[\"\",{\"children\":[\"financials\",{\"children\":[\"billing\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"financials\",{\"children\":[\"billing\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"financials\",\"children\",\"billing\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"financials\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$La\"],\"globalErrorComponent\":\"$b\"}]]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>