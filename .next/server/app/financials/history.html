<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/1efa94dc89f20134.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/app/financials/history/page-697cdbbb1ebf9ccc.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900">Payment History</h1><p class="text-gray-600">View and manage all payment transactions</p></div><button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Export History</button></div><div class="grid gap-4 md:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Total Transactions</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-blue-600">4</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Successful Payments</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-green-600">3</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Failed Payments</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-red-600">1</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6 pb-3"><h3 class="tracking-tight text-sm font-medium">Total Amount</h3></div><div class="p-6 pt-0"><div class="text-2xl font-bold text-purple-600">$<!-- -->37,625</div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 pt-6"><div class="grid grid-cols-1 md:grid-cols-5 gap-4"><div><label class="block text-sm font-medium text-gray-700 mb-2">Search</label><input type="text" placeholder="Search by name, roll number, or transaction ID" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value=""/></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Status</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Status</option><option value="completed">Completed</option><option value="failed">Failed</option><option value="refunded">Refunded</option><option value="cancelled">Cancelled</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option value="all" selected="">All Methods</option><option value="online">Online Payment</option><option value="bank_transfer">Bank Transfer</option><option value="cash">Cash</option><option value="cheque">Cheque</option><option value="card">Card</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-2">Start Date</label><input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value=""/></div><div><label class="block text-sm font-medium text-gray-700 mb-2">End Date</label><input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value=""/></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Payment Transactions</h3><p class="text-sm text-muted-foreground">Showing <!-- -->4<!-- --> of <!-- -->4<!-- --> transactions</p></div><div class="p-6 pt-0"><div class="overflow-x-auto"><table class="w-full table-auto"><thead><tr class="border-b border-gray-200"><th class="text-left py-3 px-4 font-medium text-gray-900">Transaction</th><th class="text-left py-3 px-4 font-medium text-gray-900">Student</th><th class="text-left py-3 px-4 font-medium text-gray-900">Amount</th><th class="text-left py-3 px-4 font-medium text-gray-900">Method</th><th class="text-left py-3 px-4 font-medium text-gray-900">Date</th><th class="text-left py-3 px-4 font-medium text-gray-900">Status</th><th class="text-left py-3 px-4 font-medium text-gray-900">Actions</th></tr></thead><tbody><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><div><div class="font-medium">TXN-2024-001</div><div class="text-sm text-gray-600">RCP-2024-001</div></div></td><td class="py-3 px-4"><div><div class="font-medium">John Smith</div><div class="text-sm text-gray-600">11A001</div></div></td><td class="py-3 px-4 font-medium">$<!-- -->14,625</td><td class="py-3 px-4"><div class="flex items-center space-x-2"><span>💳</span><span class="capitalize">online</span></div></td><td class="py-3 px-4">2024-01-25</td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">COMPLETED</span></td><td class="py-3 px-4"><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View</button><button class="text-green-600 hover:text-green-800 text-sm font-medium">Receipt</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><div><div class="font-medium">TXN-2024-002</div><div class="text-sm text-gray-600">RCP-2024-002</div></div></td><td class="py-3 px-4"><div><div class="font-medium">Sarah Johnson</div><div class="text-sm text-gray-600">11A002</div></div></td><td class="py-3 px-4 font-medium">$<!-- -->15,000</td><td class="py-3 px-4"><div class="flex items-center space-x-2"><span>🏦</span><span class="capitalize">bank transfer</span></div></td><td class="py-3 px-4">2024-01-22</td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">COMPLETED</span></td><td class="py-3 px-4"><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View</button><button class="text-green-600 hover:text-green-800 text-sm font-medium">Receipt</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><div><div class="font-medium">TXN-2024-003</div><div class="text-sm text-gray-600">RCP-2024-003</div></div></td><td class="py-3 px-4"><div><div class="font-medium">Michael Brown</div><div class="text-sm text-gray-600">11A003</div></div></td><td class="py-3 px-4 font-medium">$<!-- -->8,000</td><td class="py-3 px-4"><div class="flex items-center space-x-2"><span>💵</span><span class="capitalize">cash</span></div></td><td class="py-3 px-4">2024-01-20</td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">COMPLETED</span></td><td class="py-3 px-4"><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View</button><button class="text-green-600 hover:text-green-800 text-sm font-medium">Receipt</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><div><div class="font-medium">TXN-2024-004</div><div class="text-sm text-gray-600">RCP-2024-004</div></div></td><td class="py-3 px-4"><div><div class="font-medium">Emily Davis</div><div class="text-sm text-gray-600">11A004</div></div></td><td class="py-3 px-4 font-medium">$<!-- -->5,000</td><td class="py-3 px-4"><div class="flex items-center space-x-2"><span>📝</span><span class="capitalize">cheque</span></div></td><td class="py-3 px-4">2024-01-18</td><td class="py-3 px-4"><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">FAILED</span></td><td class="py-3 px-4"><div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View</button><button class="text-green-600 hover:text-green-800 text-sm font-medium">Receipt</button></div></td></tr></tbody></table></div></div></div></div><script src="/_next/static/chunks/webpack-9bb557e25c99e8c6.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/1efa94dc89f20134.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[7831,[],\"\"]\n7:I[1628,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"3692\",\"static/chunks/app/financials/history/page-697cdbbb1ebf9ccc.js\"],\"\"]\n8:I[5613,[],\"\"]\n9:I[1778,[],\"\"]\nb:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1efa94dc89f20134.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"az92YWeqjFDK2ONnGo-1U\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/financials/history\",\"initialTree\":[\"\",{\"children\":[\"financials\",{\"children\":[\"history\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"financials\",{\"children\":[\"history\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"$L6\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$7\",\"isStaticGeneration\":true}],null]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"financials\",\"children\",\"history\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"financials\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$La\"],\"globalErrorComponent\":\"$b\"}]]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>