<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ce7d169ac7e92b8e.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-569104bb1102a2f6.js" async="" crossorigin=""></script><script src="/_next/static/chunks/4938-7ffbefafd39045e0.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-b28836943ba31248.js" async="" crossorigin=""></script><script src="/_next/static/chunks/7895-dcf2d3b63c7719ea.js" async=""></script><script src="/_next/static/chunks/5250-c6905a3e4a59cbd2.js" async=""></script><script src="/_next/static/chunks/app/academic/page-babaa46a4202124c.js" async=""></script><title>EMS - Employee Management System</title><meta name="description" content="Comprehensive multi-tenant employee management system with modular architecture"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body class="__className_e8ce0c"><div class="space-y-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h1 class="text-3xl font-bold tracking-tight">Academic Dashboard</h1><p class="text-muted-foreground">Academic Year <!-- -->2024-25<!-- --> - Overview and Management</p></div><div class="flex items-center space-x-2"><button class="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90">Generate Reports</button><button class="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90">New Assessment</button></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Total Students</h3><span class="text-2xl">👥</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">1250</div><p class="text-xs text-muted-foreground">Active enrollment</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Teachers</h3><span class="text-2xl">👨‍🏫</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">85</div><p class="text-xs text-muted-foreground">Active faculty</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Classes</h3><span class="text-2xl">🏫</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">42</div><p class="text-xs text-muted-foreground">Active classes</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Subjects</h3><span class="text-2xl">📚</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">28</div><p class="text-xs text-muted-foreground">Curriculum subjects</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Attendance</h3><span class="text-2xl">📊</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">92.5<!-- -->%</div><p class="text-xs text-muted-foreground">Average this month</p></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="tracking-tight text-sm font-medium">Assessments</h3><span class="text-2xl">📝</span></div><div class="p-6 pt-0"><div class="text-2xl font-bold">15</div><p class="text-xs text-muted-foreground">Upcoming this week</p></div></div></div><div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"><div class="rounded-lg border bg-card text-card-foreground shadow-sm lg:col-span-2"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Recent Activities</h3><p class="text-sm text-muted-foreground">Latest academic activities and updates</p></div><div class="p-6 pt-0"><div class="space-y-4"><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm">📝</span></div><div><p class="font-medium">Math Quiz - Grade 9A</p><p class="text-sm text-muted-foreground">Ms. Johnson</p></div></div><div class="text-right"><p class="text-sm font-medium capitalize">completed</p><p class="text-xs text-muted-foreground">2024-01-15</p></div></div><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm">📚</span></div><div><p class="font-medium">Introduction to Physics</p><p class="text-sm text-muted-foreground">Mr. Smith</p></div></div><div class="text-right"><p class="text-sm font-medium capitalize">published</p><p class="text-xs text-muted-foreground">2024-01-15</p></div></div><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm">✅</span></div><div><p class="font-medium">Daily Attendance - Grade 10B</p><p class="text-sm text-muted-foreground">Mrs. Brown</p></div></div><div class="text-right"><p class="text-sm font-medium capitalize">marked</p><p class="text-xs text-muted-foreground">2024-01-15</p></div></div><div class="flex items-center justify-between p-3 border rounded-lg"><div class="flex items-center space-x-3"><div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"><span class="text-sm">📊</span></div><div><p class="font-medium">English Essay Grades</p><p class="text-sm text-muted-foreground">Ms. Davis</p></div></div><div class="text-right"><p class="text-sm font-medium capitalize">published</p><p class="text-xs text-muted-foreground">2024-01-14</p></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Upcoming Assessments</h3><p class="text-sm text-muted-foreground">Scheduled tests and assignments</p></div><div class="p-6 pt-0"><div class="space-y-3"><div class="p-3 border rounded-lg"><div class="flex items-center justify-between mb-2"><span class="font-medium text-sm">Mathematics</span><span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Test</span></div><p class="text-sm text-muted-foreground">Grade 9A</p><p class="text-sm text-muted-foreground">Ms. Johnson</p><p class="text-xs text-primary font-medium mt-1">2024-01-18</p></div><div class="p-3 border rounded-lg"><div class="flex items-center justify-between mb-2"><span class="font-medium text-sm">Science</span><span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Quiz</span></div><p class="text-sm text-muted-foreground">Grade 8B</p><p class="text-sm text-muted-foreground">Mr. Wilson</p><p class="text-xs text-primary font-medium mt-1">2024-01-19</p></div><div class="p-3 border rounded-lg"><div class="flex items-center justify-between mb-2"><span class="font-medium text-sm">English</span><span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Assignment</span></div><p class="text-sm text-muted-foreground">Grade 10A</p><p class="text-sm text-muted-foreground">Ms. Davis</p><p class="text-xs text-primary font-medium mt-1">2024-01-20</p></div><div class="p-3 border rounded-lg"><div class="flex items-center justify-between mb-2"><span class="font-medium text-sm">History</span><span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Project</span></div><p class="text-sm text-muted-foreground">Grade 11B</p><p class="text-sm text-muted-foreground">Mr. Thompson</p><p class="text-xs text-primary font-medium mt-1">2024-01-22</p></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Class Performance Overview</h3><p class="text-sm text-muted-foreground">Academic performance and attendance by class</p></div><div class="p-6 pt-0"><div class="overflow-x-auto"><table class="w-full table-auto"><thead><tr class="border-b border-gray-200"><th class="text-left py-3 px-4 font-medium text-gray-900">Class</th><th class="text-left py-3 px-4 font-medium text-gray-900">Students</th><th class="text-left py-3 px-4 font-medium text-gray-900">Avg Grade</th><th class="text-left py-3 px-4 font-medium text-gray-900">Attendance</th><th class="text-left py-3 px-4 font-medium text-gray-900">Actions</th></tr></thead><tbody><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-primary">Grade 9A</span></td><td class="py-3 px-4"><span class="text-gray-900">30</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><span class="text-gray-900">85.2<!-- -->%</span><div class="w-16 h-2 bg-gray-200 rounded-full"><div class="h-2 bg-green-500 rounded-full" style="width:85.2%"></div></div></div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><span class="text-gray-900">94.5<!-- -->%</span><div class="w-16 h-2 bg-gray-200 rounded-full"><div class="h-2 bg-blue-500 rounded-full" style="width:94.5%"></div></div></div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">View Details</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Reports</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-primary">Grade 9B</span></td><td class="py-3 px-4"><span class="text-gray-900">28</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><span class="text-gray-900">82.7<!-- -->%</span><div class="w-16 h-2 bg-gray-200 rounded-full"><div class="h-2 bg-green-500 rounded-full" style="width:82.7%"></div></div></div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><span class="text-gray-900">91.2<!-- -->%</span><div class="w-16 h-2 bg-gray-200 rounded-full"><div class="h-2 bg-blue-500 rounded-full" style="width:91.2%"></div></div></div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">View Details</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Reports</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-primary">Grade 10A</span></td><td class="py-3 px-4"><span class="text-gray-900">32</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><span class="text-gray-900">88.1<!-- -->%</span><div class="w-16 h-2 bg-gray-200 rounded-full"><div class="h-2 bg-green-500 rounded-full" style="width:88.1%"></div></div></div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><span class="text-gray-900">96.3<!-- -->%</span><div class="w-16 h-2 bg-gray-200 rounded-full"><div class="h-2 bg-blue-500 rounded-full" style="width:96.3%"></div></div></div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">View Details</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Reports</button></div></td></tr><tr class="border-b border-gray-100 hover:bg-gray-50"><td class="py-3 px-4"><span class="font-medium text-primary">Grade 10B</span></td><td class="py-3 px-4"><span class="text-gray-900">29</span></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><span class="text-gray-900">84.9<!-- -->%</span><div class="w-16 h-2 bg-gray-200 rounded-full"><div class="h-2 bg-green-500 rounded-full" style="width:84.9%"></div></div></div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><span class="text-gray-900">93.8<!-- -->%</span><div class="w-16 h-2 bg-gray-200 rounded-full"><div class="h-2 bg-blue-500 rounded-full" style="width:93.8%"></div></div></div></td><td class="py-3 px-4"><div class="flex items-center space-x-2"><button class="text-primary hover:text-primary/80 text-sm font-medium">View Details</button><button class="text-gray-600 hover:text-gray-800 text-sm font-medium">Reports</button></div></td></tr></tbody></table></div></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📚</span><h3 class="font-semibold tracking-tight text-lg">Curriculum</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Manage curriculum and syllabus</p><button class="text-sm font-medium text-primary hover:text-primary/80">Manage Curriculum →</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📝</span><h3 class="font-semibold tracking-tight text-lg">Assessments</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Create and manage assessments</p><button class="text-sm font-medium text-primary hover:text-primary/80">Manage Assessments →</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📊</span><h3 class="font-semibold tracking-tight text-lg">Attendance</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Track student attendance</p><button class="text-sm font-medium text-primary hover:text-primary/80">Mark Attendance →</button></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📋</span><h3 class="font-semibold tracking-tight text-lg">Reports</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Generate academic reports</p><button class="text-sm font-medium text-primary hover:text-primary/80">View Reports →</button></div></div></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6"><a href="/academic/curriculum"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📚</span><h3 class="font-semibold tracking-tight text-lg">Curriculum</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Manage subjects &amp; syllabus</p><span class="text-sm font-medium text-primary hover:text-primary/80">Manage Curriculum →</span></div></div></a><a href="/academic/schedule"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📅</span><h3 class="font-semibold tracking-tight text-lg">Schedule</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Class timetables</p><span class="text-sm font-medium text-primary hover:text-primary/80">Manage Schedule →</span></div></div></a><a href="/academic/assessments"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📝</span><h3 class="font-semibold tracking-tight text-lg">Assessments</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Tests &amp; assignments</p><span class="text-sm font-medium text-primary hover:text-primary/80">Create Assessments →</span></div></div></a><a href="/academic/grades"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📊</span><h3 class="font-semibold tracking-tight text-lg">Grades</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Grade entry &amp; management</p><span class="text-sm font-medium text-primary hover:text-primary/80">Enter Grades →</span></div></div></a><a href="/academic/attendance"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">✅</span><h3 class="font-semibold tracking-tight text-lg">Attendance</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Track student attendance</p><span class="text-sm font-medium text-primary hover:text-primary/80">Track Attendance →</span></div></div></a><a href="/academic/progress"><div class="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow cursor-pointer"><div class="flex flex-col space-y-1.5 p-6 pb-3"><div class="flex items-center space-x-2"><span class="text-2xl">📈</span><h3 class="font-semibold tracking-tight text-lg">Progress Reports</h3></div></div><div class="p-6 pt-0"><p class="text-sm text-muted-foreground mb-3">Student progress analytics</p><span class="text-sm font-medium text-primary hover:text-primary/80">View Progress →</span></div></div></a></div></div></div><script src="/_next/static/chunks/webpack-ad9d4ea5f5bfeccc.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/ce7d169ac7e92b8e.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L3\"\n"])</script><script>self.__next_f.push([1,"4:I[7690,[],\"\"]\n6:I[1514,[\"7895\",\"static/chunks/7895-dcf2d3b63c7719ea.js\",\"5250\",\"static/chunks/5250-c6905a3e4a59cbd2.js\",\"7525\",\"static/chunks/app/academic/page-babaa46a4202124c.js\"],\"AcademicDashboard\"]\n7:I[5613,[],\"\"]\n8:I[1778,[],\"\"]\na:I[8955,[],\"\"]\n"])</script><script>self.__next_f.push([1,"3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ce7d169ac7e92b8e.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"Tn7xWRdXVWNLs4FpbaknK\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/academic\",\"initialTree\":[\"\",{\"children\":[\"academic\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"academic\",{\"children\":[\"__PAGE__\",{},[\"$L5\",[\"$\",\"div\",null,{\"className\":\"space-y-6\",\"children\":[\"$\",\"$L6\",null,{\"tenantId\":\"demo-tenant-uuid-1234567890\"}]}],null]]},[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"academic\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}],null]],\"initialHead\":[false,\"$L9\"],\"globalErrorComponent\":\"$a\"}]]\n"])</script><script>self.__next_f.push([1,"9:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"EMS - Employee Management System\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Comprehensive multi-tenant employee management system with modular architecture\"}],[\"$\",\"meta\",\"4\",{\"name\":\"next-size-adjust\"}]]\n5:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>