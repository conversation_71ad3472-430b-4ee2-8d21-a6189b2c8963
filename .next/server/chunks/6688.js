"use strict";exports.id=6688,exports.ids=[6688],exports.modules={46688:(e,t,r)=>{r.r(t),r.d(t,{FetchError:()=>h,Headers:()=>R,Request:()=>G,Response:()=>U,default:()=>W});var o=r(12781),n=r(13685),i=r(57310),s=r(63122),l=r(95687),u=r(59796);let a=o.Readable,f=Symbol("buffer"),c=Symbol("type");class d{constructor(){this[c]="";let e=arguments[0],t=arguments[1],r=[];if(e){let t=Number(e.length);for(let o=0;o<t;o++){let t;let n=e[o];(t=n instanceof Buffer?n:ArrayBuffer.isView(n)?Buffer.from(n.buffer,n.byteOffset,n.byteLength):n instanceof ArrayBuffer?Buffer.from(n):n instanceof d?n[f]:Buffer.from("string"==typeof n?n:String(n))).length,r.push(t)}}this[f]=Buffer.concat(r);let o=t&&void 0!==t.type&&String(t.type).toLowerCase();o&&!/[^\u0020-\u007E]/.test(o)&&(this[c]=o)}get size(){return this[f].length}get type(){return this[c]}text(){return Promise.resolve(this[f].toString())}arrayBuffer(){let e=this[f];return Promise.resolve(e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength))}stream(){let e=new a;return e._read=function(){},e.push(this[f]),e.push(null),e}toString(){return"[object Blob]"}slice(){let e;let t=this.size,r=arguments[0],o=arguments[1];e=void 0===r?0:r<0?Math.max(t+r,0):Math.min(r,t);let n=Math.max((void 0===o?t:o<0?Math.max(t+o,0):Math.min(o,t))-e,0),i=this[f].slice(e,e+n),s=new d([],{type:arguments[2]});return s[f]=i,s}}function h(e,t,r){Error.call(this,e),this.message=e,this.type=t,r&&(this.code=this.errno=r.code),Error.captureStackTrace(this,this.constructor)}Object.defineProperties(d.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),Object.defineProperty(d.prototype,Symbol.toStringTag,{value:"Blob",writable:!1,enumerable:!1,configurable:!0}),h.prototype=Object.create(Error.prototype),h.prototype.constructor=h,h.prototype.name="FetchError";let p=Symbol("Body internals"),b=o.PassThrough;function y(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.size,i=r.timeout;null==e?e=null:g(e)?e=Buffer.from(e.toString()):w(e)||Buffer.isBuffer(e)||("[object ArrayBuffer]"===Object.prototype.toString.call(e)?e=Buffer.from(e):ArrayBuffer.isView(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):e instanceof o||(e=Buffer.from(String(e)))),this[p]={body:e,disturbed:!1,error:null},this.size=void 0===n?0:n,this.timeout=void 0===i?0:i,e instanceof o&&e.on("error",function(e){let r="AbortError"===e.name?e:new h(`Invalid response body while trying to fetch ${t.url}: ${e.message}`,"system",e);t[p].error=r})}function m(){var e=this;if(this[p].disturbed)return y.Promise.reject(TypeError(`body used already for: ${this.url}`));if(this[p].disturbed=!0,this[p].error)return y.Promise.reject(this[p].error);let t=this.body;if(null===t)return y.Promise.resolve(Buffer.alloc(0));if(w(t)&&(t=t.stream()),Buffer.isBuffer(t))return y.Promise.resolve(t);if(!(t instanceof o))return y.Promise.resolve(Buffer.alloc(0));let r=[],n=0,i=!1;return new y.Promise(function(o,s){let l;e.timeout&&(l=setTimeout(function(){i=!0,s(new h(`Response timeout while trying to fetch ${e.url} (over ${e.timeout}ms)`,"body-timeout"))},e.timeout)),t.on("error",function(t){"AbortError"===t.name?(i=!0,s(t)):s(new h(`Invalid response body while trying to fetch ${e.url}: ${t.message}`,"system",t))}),t.on("data",function(t){if(!i&&null!==t){if(e.size&&n+t.length>e.size){i=!0,s(new h(`content size at ${e.url} over limit: ${e.size}`,"max-size"));return}n+=t.length,r.push(t)}}),t.on("end",function(){if(!i){clearTimeout(l);try{o(Buffer.concat(r,n))}catch(t){s(new h(`Could not create Buffer from response body for ${e.url}: ${t.message}`,"system",t))}}})})}function g(e){return"object"==typeof e&&"function"==typeof e.append&&"function"==typeof e.delete&&"function"==typeof e.get&&"function"==typeof e.getAll&&"function"==typeof e.has&&"function"==typeof e.set&&("URLSearchParams"===e.constructor.name||"[object URLSearchParams]"===Object.prototype.toString.call(e)||"function"==typeof e.sort)}function w(e){return"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&"function"==typeof e.constructor&&"string"==typeof e.constructor.name&&/^(Blob|File)$/.test(e.constructor.name)&&/^(Blob|File)$/.test(e[Symbol.toStringTag])}function v(e){let t,r;let n=e.body;if(e.bodyUsed)throw Error("cannot clone body after it is used");return n instanceof o&&"function"!=typeof n.getBoundary&&(t=new b,r=new b,n.pipe(t),n.pipe(r),e[p].body=t,n=r),n}function T(e){if(null===e)return null;if("string"==typeof e)return"text/plain;charset=UTF-8";if(g(e))return"application/x-www-form-urlencoded;charset=UTF-8";if(w(e))return e.type||null;if(Buffer.isBuffer(e))return null;if("[object ArrayBuffer]"===Object.prototype.toString.call(e))return null;if(ArrayBuffer.isView(e))return null;else if("function"==typeof e.getBoundary)return`multipart/form-data;boundary=${e.getBoundary()}`;else if(e instanceof o)return null;else return"text/plain;charset=UTF-8"}function S(e){let t=e.body;return null===t?0:w(t)?t.size:Buffer.isBuffer(t)?t.length:t&&"function"==typeof t.getLengthSync&&(t._lengthRetrievers&&0==t._lengthRetrievers.length||t.hasKnownLength&&t.hasKnownLength())?t.getLengthSync():null}y.prototype={get body(){return this[p].body},get bodyUsed(){return this[p].disturbed},arrayBuffer(){return m.call(this).then(function(e){return e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength)})},blob(){let e=this.headers&&this.headers.get("content-type")||"";return m.call(this).then(function(t){return Object.assign(new d([],{type:e.toLowerCase()}),{[f]:t})})},json(){var e=this;return m.call(this).then(function(t){try{return JSON.parse(t.toString())}catch(t){return y.Promise.reject(new h(`invalid json response body at ${e.url} reason: ${t.message}`,"invalid-json"))}})},text(){return m.call(this).then(function(e){return e.toString()})},buffer(){return m.call(this)},textConverted(){var e=this;return m.call(this).then(function(t){return function(e,t){throw Error("The package `encoding` must be installed to use the textConverted() function")}(0,e.headers)})}},Object.defineProperties(y.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0}}),y.mixIn=function(e){for(let t of Object.getOwnPropertyNames(y.prototype))if(!(t in e)){let r=Object.getOwnPropertyDescriptor(y.prototype,t);Object.defineProperty(e,t,r)}},y.Promise=global.Promise;let E=/[^\^_`a-zA-Z\-0-9!#$%&'*+.|~]/,j=/[^\t\x20-\x7e\x80-\xff]/;function O(e){if(e=`${e}`,E.test(e)||""===e)throw TypeError(`${e} is not a legal HTTP header name`)}function P(e){if(e=`${e}`,j.test(e))throw TypeError(`${e} is not a legal HTTP header value`)}function B(e,t){for(let r in t=t.toLowerCase(),e)if(r.toLowerCase()===t)return r}let x=Symbol("map");class R{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;if(this[x]=Object.create(null),e instanceof R){let t=e.raw();for(let e of Object.keys(t))for(let r of t[e])this.append(e,r);return}if(null==e);else if("object"==typeof e){let t=e[Symbol.iterator];if(null!=t){if("function"!=typeof t)throw TypeError("Header pairs must be iterable");let r=[];for(let t of e){if("object"!=typeof t||"function"!=typeof t[Symbol.iterator])throw TypeError("Each header pair must be iterable");r.push(Array.from(t))}for(let e of r){if(2!==e.length)throw TypeError("Each header pair must be a name/value tuple");this.append(e[0],e[1])}}else for(let t of Object.keys(e)){let r=e[t];this.append(t,r)}}else throw TypeError("Provided initializer must be an object")}get(e){O(e=`${e}`);let t=B(this[x],e);return void 0===t?null:this[x][t].join(", ")}forEach(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,r=$(this),o=0;for(;o<r.length;){var n=r[o];let i=n[0],s=n[1];e.call(t,s,i,this),r=$(this),o++}}set(e,t){e=`${e}`,t=`${t}`,O(e),P(t);let r=B(this[x],e);this[x][void 0!==r?r:e]=[t]}append(e,t){e=`${e}`,t=`${t}`,O(e),P(t);let r=B(this[x],e);void 0!==r?this[x][r].push(t):this[x][e]=[t]}has(e){return O(e=`${e}`),void 0!==B(this[x],e)}delete(e){O(e=`${e}`);let t=B(this[x],e);void 0!==t&&delete this[x][t]}raw(){return this[x]}keys(){return L(this,"key")}values(){return L(this,"value")}[Symbol.iterator](){return L(this,"key+value")}}function $(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"key+value";return Object.keys(e[x]).sort().map("key"===t?function(e){return e.toLowerCase()}:"value"===t?function(t){return e[x][t].join(", ")}:function(t){return[t.toLowerCase(),e[x][t].join(", ")]})}R.prototype.entries=R.prototype[Symbol.iterator],Object.defineProperty(R.prototype,Symbol.toStringTag,{value:"Headers",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(R.prototype,{get:{enumerable:!0},forEach:{enumerable:!0},set:{enumerable:!0},append:{enumerable:!0},has:{enumerable:!0},delete:{enumerable:!0},keys:{enumerable:!0},values:{enumerable:!0},entries:{enumerable:!0}});let C=Symbol("internal");function L(e,t){let r=Object.create(A);return r[C]={target:e,kind:t,index:0},r}let A=Object.setPrototypeOf({next(){if(!this||Object.getPrototypeOf(this)!==A)throw TypeError("Value of `this` is not a HeadersIterator");var e=this[C];let t=e.target,r=e.kind,o=e.index,n=$(t,r);return o>=n.length?{value:void 0,done:!0}:(this[C].index=o+1,{value:n[o],done:!1})}},Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));Object.defineProperty(A,Symbol.toStringTag,{value:"HeadersIterator",writable:!1,enumerable:!1,configurable:!0});let k=Symbol("Response internals"),z=n.STATUS_CODES;class U{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};y.call(this,e,t);let r=t.status||200,o=new R(t.headers);if(null!=e&&!o.has("Content-Type")){let t=T(e);t&&o.append("Content-Type",t)}this[k]={url:t.url,status:r,statusText:t.statusText||z[r],headers:o,counter:t.counter}}get url(){return this[k].url||""}get status(){return this[k].status}get ok(){return this[k].status>=200&&this[k].status<300}get redirected(){return this[k].counter>0}get statusText(){return this[k].statusText}get headers(){return this[k].headers}clone(){return new U(v(this),{url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected})}}y.mixIn(U.prototype),Object.defineProperties(U.prototype,{url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}}),Object.defineProperty(U.prototype,Symbol.toStringTag,{value:"Response",writable:!1,enumerable:!1,configurable:!0});let _=Symbol("Request internals"),H=i.URL||s.URL,q=i.parse,F=i.format;function I(e){return/^[a-zA-Z][a-zA-Z\d+\-.]*:/.exec(e)&&(e=new H(e).toString()),q(e)}let M="destroy"in o.Readable.prototype;function D(e){return"object"==typeof e&&"object"==typeof e[_]}class G{constructor(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};D(e)?t=I(e.url):(t=e&&e.href?I(e.href):I(`${e}`),e={});let o=r.method||e.method||"GET";if(o=o.toUpperCase(),(null!=r.body||D(e)&&null!==e.body)&&("GET"===o||"HEAD"===o))throw TypeError("Request with GET/HEAD method cannot have body");let n=null!=r.body?r.body:D(e)&&null!==e.body?v(e):null;y.call(this,n,{timeout:r.timeout||e.timeout||0,size:r.size||e.size||0});let i=new R(r.headers||e.headers||{});if(null!=n&&!i.has("Content-Type")){let e=T(n);e&&i.append("Content-Type",e)}let s=D(e)?e.signal:null;if("signal"in r&&(s=r.signal),null!=s&&!function(e){let t=e&&"object"==typeof e&&Object.getPrototypeOf(e);return!!(t&&"AbortSignal"===t.constructor.name)}(s))throw TypeError("Expected signal to be an instanceof AbortSignal");this[_]={method:o,redirect:r.redirect||e.redirect||"follow",headers:i,parsedURL:t,signal:s},this.follow=void 0!==r.follow?r.follow:void 0!==e.follow?e.follow:20,this.compress=void 0!==r.compress?r.compress:void 0===e.compress||e.compress,this.counter=r.counter||e.counter||0,this.agent=r.agent||e.agent}get method(){return this[_].method}get url(){return F(this[_].parsedURL)}get headers(){return this[_].headers}get redirect(){return this[_].redirect}get signal(){return this[_].signal}clone(){return new G(this)}}function N(e){Error.call(this,e),this.type="aborted",this.message=e,Error.captureStackTrace(this,this.constructor)}y.mixIn(G.prototype),Object.defineProperty(G.prototype,Symbol.toStringTag,{value:"Request",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(G.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0}}),N.prototype=Object.create(Error.prototype),N.prototype.constructor=N,N.prototype.name="AbortError";let Z=i.URL||s.URL,V=o.PassThrough,K=function(e,t){let r=new Z(t).hostname,o=new Z(e).hostname;return r===o||"."===r[r.length-o.length-1]&&r.endsWith(o)};function Y(e,t){if(!Y.Promise)throw Error("native promise missing, set fetch.Promise to your favorite alternative");return y.Promise=Y.Promise,new Y.Promise(function(r,i){var s;let a,f;let c=new G(e,t),d=function(e){let t=e[_].parsedURL,r=new R(e[_].headers);if(r.has("Accept")||r.set("Accept","*/*"),!t.protocol||!t.hostname)throw TypeError("Only absolute URLs are supported");if(!/^https?:$/.test(t.protocol))throw TypeError("Only HTTP(S) protocols are supported");if(e.signal&&e.body instanceof o.Readable&&!M)throw Error("Cancellation of streamed requests with AbortSignal is not supported in node < 8");let n=null;if(null==e.body&&/^(POST|PUT)$/i.test(e.method)&&(n="0"),null!=e.body){let t=S(e);"number"==typeof t&&(n=String(t))}n&&r.set("Content-Length",n),r.has("User-Agent")||r.set("User-Agent","node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"),e.compress&&!r.has("Accept-Encoding")&&r.set("Accept-Encoding","gzip,deflate");let i=e.agent;return"function"==typeof i&&(i=i(t)),r.has("Connection")||i||r.set("Connection","close"),Object.assign({},t,{method:e.method,headers:function(e){let t=Object.assign({__proto__:null},e[x]),r=B(e[x],"Host");return void 0!==r&&(t[r]=t[r][0]),t}(r),agent:i})}(c),p=("https:"===d.protocol?l:n).request,b=c.signal,y=null,m=function(){let e=new N("The user aborted a request.");i(e),c.body&&c.body instanceof o.Readable&&J(c.body,e),y&&y.body&&y.body.emit("error",e)};if(b&&b.aborted){m();return}let g=function(){m(),T()},v=p(d);function T(){v.abort(),b&&b.removeEventListener("abort",g),clearTimeout(a)}b&&b.addEventListener("abort",g),c.timeout&&v.once("socket",function(e){a=setTimeout(function(){i(new h(`network timeout at: ${c.url}`,"request-timeout")),T()},c.timeout)}),v.on("error",function(e){i(new h(`request to ${c.url} failed, reason: ${e.message}`,"system",e)),y&&y.body&&J(y.body,e),T()}),s=function(e){(!b||!b.aborted)&&y&&y.body&&J(y.body,e)},v.on("socket",function(e){f=e}),v.on("response",function(e){let t=e.headers;"chunked"!==t["transfer-encoding"]||t["content-length"]||e.once("close",function(e){if(f&&f.listenerCount("data")>0&&!e){let e=Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",s(e)}})}),14>parseInt(process.version.substring(1))&&v.on("socket",function(e){e.addListener("close",function(t){let r=e.listenerCount("data")>0;if(y&&r&&!t&&!(b&&b.aborted)){let e=Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",y.body.emit("error",e)}})}),v.on("response",function(e){clearTimeout(a);let t=function(e){let t=new R;for(let r of Object.keys(e))if(!E.test(r)){if(Array.isArray(e[r]))for(let o of e[r])j.test(o)||(void 0===t[x][r]?t[x][r]=[o]:t[x][r].push(o));else j.test(e[r])||(t[x][r]=[e[r]])}return t}(e.headers);if(Y.isRedirect(e.statusCode)){let n=t.get("Location"),s=null;try{s=null===n?null:new Z(n,c.url).toString()}catch(e){if("manual"!==c.redirect){i(new h(`uri requested responds with an invalid redirect URL: ${n}`,"invalid-redirect")),T();return}}switch(c.redirect){case"error":i(new h(`uri requested responds with a redirect, redirect mode is set to error: ${c.url}`,"no-redirect")),T();return;case"manual":if(null!==s)try{t.set("Location",s)}catch(e){i(e)}break;case"follow":var o;if(null===s)break;if(c.counter>=c.follow){i(new h(`maximum redirect reached at: ${c.url}`,"max-redirect")),T();return}let l={headers:new R(c.headers),follow:c.follow,counter:c.counter+1,agent:c.agent,compress:c.compress,method:c.method,body:c.body,signal:c.signal,timeout:c.timeout,size:c.size};if(!K(c.url,s)||(o=c.url,new Z(s).protocol!==new Z(o).protocol))for(let e of["authorization","www-authenticate","cookie","cookie2"])l.headers.delete(e);if(303!==e.statusCode&&c.body&&null===S(c)){i(new h("Cannot follow redirect with body being a readable stream","unsupported-redirect")),T();return}(303===e.statusCode||(301===e.statusCode||302===e.statusCode)&&"POST"===c.method)&&(l.method="GET",l.body=void 0,l.headers.delete("content-length")),r(Y(new G(s,l))),T();return}}e.once("end",function(){b&&b.removeEventListener("abort",g)});let n=e.pipe(new V),s={url:c.url,status:e.statusCode,statusText:e.statusMessage,headers:t,size:c.size,timeout:c.timeout,counter:c.counter},l=t.get("Content-Encoding");if(!c.compress||"HEAD"===c.method||null===l||204===e.statusCode||304===e.statusCode){r(y=new U(n,s));return}let f={flush:u.Z_SYNC_FLUSH,finishFlush:u.Z_SYNC_FLUSH};if("gzip"==l||"x-gzip"==l){r(y=new U(n=n.pipe(u.createGunzip(f)),s));return}if("deflate"==l||"x-deflate"==l){let t=e.pipe(new V);t.once("data",function(e){r(y=new U(n=(15&e[0])==8?n.pipe(u.createInflate()):n.pipe(u.createInflateRaw()),s))}),t.on("end",function(){y||r(y=new U(n,s))});return}if("br"==l&&"function"==typeof u.createBrotliDecompress){r(y=new U(n=n.pipe(u.createBrotliDecompress()),s));return}r(y=new U(n,s))}),function(e,t){let r=t.body;null===r?e.end():w(r)?r.stream().pipe(e):Buffer.isBuffer(r)?(e.write(r),e.end()):r.pipe(e)}(v,c)})}function J(e,t){e.destroy?e.destroy(t):(e.emit("error",t),e.end())}Y.isRedirect=function(e){return 301===e||302===e||303===e||307===e||308===e},Y.Promise=global.Promise;let W=Y}};