"use strict";exports.id=6671,exports.ids=[6671],exports.modules={18791:(e,s,a)=>{a.d(s,{Zb:()=>c,aY:()=>x,SZ:()=>m,Ol:()=>d,ll:()=>o});var n=a(95344),t=a(3729),i=a(56815),l=a(79377);function r(...e){return(0,l.m6)((0,i.W)(e))}let c=t.forwardRef(({className:e,...s},a)=>n.jsx("div",{ref:a,className:r("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));c.displayName="Card";let d=t.forwardRef(({className:e,...s},a)=>n.jsx("div",{ref:a,className:r("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let o=t.forwardRef(({className:e,...s},a)=>n.jsx("h3",{ref:a,className:r("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let m=t.forwardRef(({className:e,...s},a)=>n.jsx("p",{ref:a,className:r("text-sm text-muted-foreground",e),...s}));m.displayName="CardDescription";let x=t.forwardRef(({className:e,...s},a)=>n.jsx("div",{ref:a,className:r("p-6 pt-0",e),...s}));x.displayName="CardContent",t.forwardRef(({className:e,...s},a)=>n.jsx("div",{ref:a,className:r("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},36671:(e,s,a)=>{a.d(s,{N:()=>l});var n=a(95344),t=a(3729),i=a(18791);function l({tenantId:e,onBookFacility:s,onCancelBooking:a,onUpdateFacility:l,onScheduleMaintenance:r}){let[c,d]=(0,t.useState)("facilities"),[o,m]=(0,t.useState)(""),[x,u]=(0,t.useState)("all"),[h,p]=(0,t.useState)("all"),[g,y]=(0,t.useState)(null),j=[{id:"1",facilityId:"FAC-001",name:"Main Common Room",type:"common_room",location:"Ground Floor, Block A",capacity:50,currentOccupancy:12,amenities:["TV","Sofa Sets","Air Conditioning","Wi-Fi","Games"],operatingHours:{weekdays:{start:"06:00",end:"23:00"},weekends:{start:"06:00",end:"24:00"}},bookingRequired:!1,maxBookingDuration:0,advanceBookingDays:0,status:"available",maintenanceSchedule:{lastMaintenance:"2024-01-15",nextMaintenance:"2024-02-15",frequency:"monthly"},rules:["Maintain silence after 10 PM","Clean up after use","No food or drinks on furniture","Report any damages immediately"],contactPerson:"Facility Manager",contactPhone:"+91-9876543240",description:"Large common room for students to relax, watch TV, and socialize with friends."},{id:"2",facilityId:"FAC-002",name:"Study Hall",type:"study_hall",location:"First Floor, Block B",capacity:80,currentOccupancy:45,amenities:["Individual Study Tables","Reading Lamps","Air Conditioning","Wi-Fi","Silent Zone"],operatingHours:{weekdays:{start:"05:00",end:"24:00"},weekends:{start:"05:00",end:"24:00"}},bookingRequired:!1,maxBookingDuration:0,advanceBookingDays:0,status:"available",maintenanceSchedule:{lastMaintenance:"2024-01-20",nextMaintenance:"2024-02-20",frequency:"monthly"},rules:["Maintain complete silence","No mobile phones allowed","No group discussions","Keep personal belongings secure"],contactPerson:"Study Hall Supervisor",contactPhone:"+91-9876543241",description:"24/7 study hall with individual seating and silent environment for focused studying."},{id:"3",facilityId:"FAC-003",name:"Fitness Gym",type:"gym",location:"Basement, Block A",capacity:25,currentOccupancy:8,amenities:["Cardio Equipment","Weight Training","Yoga Mats","Sound System","Lockers"],operatingHours:{weekdays:{start:"05:00",end:"22:00"},weekends:{start:"06:00",end:"21:00"}},bookingRequired:!0,maxBookingDuration:120,advanceBookingDays:7,status:"available",maintenanceSchedule:{lastMaintenance:"2024-01-25",nextMaintenance:"2024-02-25",frequency:"monthly"},rules:["Booking required for peak hours","Proper gym attire mandatory","Clean equipment after use","No dropping weights","Maximum 2 hours per session"],contactPerson:"Gym Instructor",contactPhone:"+91-9876543242",description:"Well-equipped gym with modern fitness equipment and professional guidance."},{id:"4",facilityId:"FAC-004",name:"Laundry Room",type:"laundry",location:"Ground Floor, Block C",capacity:12,currentOccupancy:3,amenities:["Washing Machines","Dryers","Ironing Boards","Detergent Dispenser"],operatingHours:{weekdays:{start:"06:00",end:"22:00"},weekends:{start:"07:00",end:"21:00"}},bookingRequired:!0,maxBookingDuration:180,advanceBookingDays:2,status:"available",maintenanceSchedule:{lastMaintenance:"2024-01-30",nextMaintenance:"2024-02-28",frequency:"monthly"},rules:["Book time slots in advance","Remove clothes promptly","Clean lint filters after use","Report machine issues immediately"],contactPerson:"Laundry Attendant",contactPhone:"+91-9876543243",description:"Self-service laundry facility with washing machines, dryers, and ironing facilities."}],f=[{id:"1",bookingId:"BK-2024-001",facilityId:"3",facilityName:"Fitness Gym",studentId:"STU-2024-001",studentName:"Rahul Sharma",bookingDate:"2024-02-06",startTime:"07:00",endTime:"09:00",duration:120,purpose:"Morning workout",numberOfPeople:1,status:"confirmed",bookingDate_created:"2024-02-04",notes:"Regular morning workout session"},{id:"2",bookingId:"BK-2024-002",facilityId:"4",facilityName:"Laundry Room",studentId:"STU-2024-002",studentName:"Priya Singh",bookingDate:"2024-02-06",startTime:"14:00",endTime:"17:00",duration:180,purpose:"Weekly laundry",numberOfPeople:1,status:"confirmed",bookingDate_created:"2024-02-05",specialRequests:"Need extra drying time"}],b=e=>{switch(e){case"available":return"bg-green-100 text-green-800";case"occupied":return"bg-blue-100 text-blue-800";case"maintenance":return"bg-yellow-100 text-yellow-800";case"closed":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},N=e=>{switch(e){case"common_room":return"\uD83D\uDECB️";case"study_hall":return"\uD83D\uDCDA";case"gym":return"\uD83D\uDCAA";case"laundry":return"\uD83D\uDC55";case"kitchen":return"\uD83C\uDF73";case"parking":return"\uD83D\uDE97";case"garden":return"\uD83C\uDF33";case"sports":return"⚽";default:return"\uD83C\uDFE2"}},v=e=>{switch(e){case"confirmed":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-red-100 text-red-800";case"completed":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},k=j.filter(e=>{let s=e.name.toLowerCase().includes(o.toLowerCase())||e.location.toLowerCase().includes(o.toLowerCase())||e.amenities.some(e=>e.toLowerCase().includes(o.toLowerCase())),a="all"===x||e.type===x,n="all"===h||e.status===h;return s&&a&&n});return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Facility Management"}),n.jsx("p",{className:"text-gray-600",children:"Manage hostel facilities and bookings"})]}),n.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Add Facility"})]}),(0,n.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,n.jsxs)(i.Zb,{children:[n.jsx(i.Ol,{className:"pb-3",children:n.jsx(i.ll,{className:"text-sm font-medium",children:"Total Facilities"})}),n.jsx(i.aY,{children:n.jsx("div",{className:"text-2xl font-bold text-blue-600",children:j.length})})]}),(0,n.jsxs)(i.Zb,{children:[n.jsx(i.Ol,{className:"pb-3",children:n.jsx(i.ll,{className:"text-sm font-medium",children:"Available"})}),n.jsx(i.aY,{children:n.jsx("div",{className:"text-2xl font-bold text-green-600",children:j.filter(e=>"available"===e.status).length})})]}),(0,n.jsxs)(i.Zb,{children:[n.jsx(i.Ol,{className:"pb-3",children:n.jsx(i.ll,{className:"text-sm font-medium",children:"Active Bookings"})}),n.jsx(i.aY,{children:n.jsx("div",{className:"text-2xl font-bold text-purple-600",children:f.filter(e=>"confirmed"===e.status).length})})]}),(0,n.jsxs)(i.Zb,{children:[n.jsx(i.Ol,{className:"pb-3",children:n.jsx(i.ll,{className:"text-sm font-medium",children:"Utilization Rate"})}),(0,n.jsxs)(i.aY,{children:[n.jsx("div",{className:"text-2xl font-bold text-orange-600",children:"78%"}),n.jsx("p",{className:"text-xs text-gray-500",children:"This month"})]})]})]}),n.jsx("div",{className:"border-b border-gray-200",children:(0,n.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,n.jsxs)("button",{onClick:()=>d("facilities"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"facilities"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:["\uD83C\uDFE2 Facilities (",j.length,")"]}),(0,n.jsxs)("button",{onClick:()=>d("bookings"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"bookings"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:["\uD83D\uDCC5 Bookings (",f.length,")"]})]})}),(0,n.jsxs)("div",{children:["facilities"===c&&(0,n.jsxs)("div",{className:"space-y-6",children:[n.jsx(i.Zb,{children:n.jsx(i.aY,{className:"pt-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),n.jsx("input",{type:"text",placeholder:"Search by name, location, or amenities",value:o,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Type"}),(0,n.jsxs)("select",{value:x,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[n.jsx("option",{value:"all",children:"All Types"}),n.jsx("option",{value:"common_room",children:"Common Room"}),n.jsx("option",{value:"study_hall",children:"Study Hall"}),n.jsx("option",{value:"gym",children:"Gym"}),n.jsx("option",{value:"laundry",children:"Laundry"}),n.jsx("option",{value:"kitchen",children:"Kitchen"}),n.jsx("option",{value:"parking",children:"Parking"}),n.jsx("option",{value:"garden",children:"Garden"}),n.jsx("option",{value:"sports",children:"Sports"}),n.jsx("option",{value:"other",children:"Other"})]})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,n.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[n.jsx("option",{value:"all",children:"All Status"}),n.jsx("option",{value:"available",children:"Available"}),n.jsx("option",{value:"occupied",children:"Occupied"}),n.jsx("option",{value:"maintenance",children:"Maintenance"}),n.jsx("option",{value:"closed",children:"Closed"})]})]}),n.jsx("div",{className:"flex items-end",children:n.jsx("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Facilities"})})]})})}),n.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:k.map(e=>(0,n.jsxs)(i.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,n.jsxs)(i.Ol,{children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx("span",{className:"text-2xl",children:N(e.type)}),n.jsx(i.ll,{className:"text-lg",children:e.name})]}),n.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${b(e.status)}`,children:e.status.toUpperCase()})]}),n.jsx(i.SZ,{children:e.location})]}),n.jsx(i.aY,{children:(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{className:"text-gray-600",children:"Capacity:"}),n.jsx("span",{className:"font-medium",children:e.capacity})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{className:"text-gray-600",children:"Current Occupancy:"}),n.jsx("span",{className:"font-medium",children:e.currentOccupancy})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{className:"text-gray-600",children:"Booking Required:"}),n.jsx("span",{className:"font-medium",children:e.bookingRequired?"Yes":"No"})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{className:"text-gray-600",children:"Operating Hours:"}),(0,n.jsxs)("span",{className:"font-medium",children:[e.operatingHours.weekdays.start," - ",e.operatingHours.weekdays.end]})]}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.amenities.slice(0,3).map((e,s)=>n.jsx("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full",children:e},s)),e.amenities.length>3&&(0,n.jsxs)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full",children:["+",e.amenities.length-3]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("span",{className:"text-xs text-gray-500",children:["Next maintenance: ",e.maintenanceSchedule.nextMaintenance]}),n.jsx("button",{onClick:()=>y(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"})]})]})})]},e.id))})]}),"bookings"===c&&(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[n.jsx("h2",{className:"text-lg font-semibold",children:"Facility Bookings"}),n.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ New Booking"})]}),n.jsx("div",{className:"space-y-4",children:f.map(e=>n.jsx(i.Zb,{children:n.jsx(i.aY,{className:"pt-6",children:n.jsx("div",{className:"flex items-start justify-between",children:(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[n.jsx("h3",{className:"text-lg font-semibold",children:e.facilityName}),n.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${v(e.status)}`,children:e.status.toUpperCase()})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-3",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Student: ",e.studentName]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Booking ID: ",e.bookingId]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Purpose: ",e.purpose]})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Date: ",e.bookingDate]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Time: ",e.startTime," - ",e.endTime]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Duration: ",e.duration," minutes"]})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"text-sm text-gray-500",children:["People: ",e.numberOfPeople," • Booked on: ",e.bookingDate_created]}),"confirmed"===e.status&&n.jsx("button",{onClick:()=>a?.(e.id),className:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700",children:"Cancel"})]})]})})})},e.id))})]})]}),g&&n.jsx(()=>g?n.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[n.jsx("h3",{className:"text-xl font-semibold",children:g.name}),n.jsx("button",{onClick:()=>y(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-medium mb-3",children:"Facility Information"}),(0,n.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Facility ID:"})," ",g.facilityId]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Type:"})," ",N(g.type)," ",g.type.replace("_"," ").toUpperCase()]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Location:"})," ",g.location]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Capacity:"})," ",g.capacity]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Current Occupancy:"})," ",g.currentOccupancy]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Status:"}),n.jsx("span",{className:`ml-2 px-2 py-1 text-xs rounded-full ${b(g.status)}`,children:g.status.toUpperCase()})]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Contact:"})," ",g.contactPerson]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Phone:"})," ",g.contactPhone]})]})]}),(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-medium mb-3",children:"Operating Hours & Booking"}),(0,n.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Weekdays:"})," ",g.operatingHours.weekdays.start," - ",g.operatingHours.weekdays.end]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Weekends:"})," ",g.operatingHours.weekends.start," - ",g.operatingHours.weekends.end]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Booking Required:"})," ",g.bookingRequired?"Yes":"No"]}),g.bookingRequired&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Max Duration:"})," ",g.maxBookingDuration," minutes"]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Advance Booking:"})," ",g.advanceBookingDays," days"]})]})]})]})]}),(0,n.jsxs)("div",{className:"mb-6",children:[n.jsx("h4",{className:"font-medium mb-3",children:"Description"}),n.jsx("p",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded",children:g.description})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-medium mb-3",children:"Amenities"}),n.jsx("div",{className:"flex flex-wrap gap-2",children:g.amenities.map((e,s)=>n.jsx("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e},s))})]}),(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-medium mb-3",children:"Maintenance Schedule"}),(0,n.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Last Maintenance:"})," ",g.maintenanceSchedule.lastMaintenance]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Next Maintenance:"})," ",g.maintenanceSchedule.nextMaintenance]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Frequency:"})," ",g.maintenanceSchedule.frequency.toUpperCase()]})]})]})]}),(0,n.jsxs)("div",{className:"mb-6",children:[n.jsx("h4",{className:"font-medium mb-3",children:"Rules & Guidelines"}),n.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:g.rules.map((e,s)=>(0,n.jsxs)("li",{className:"flex items-start",children:[n.jsx("span",{className:"text-blue-500 mr-2",children:"•"}),n.jsx("span",{children:e})]},s))})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3",children:[g.bookingRequired&&n.jsx("button",{onClick:()=>s?.(g.id),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Book Facility"}),n.jsx("button",{onClick:()=>l?.(g.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Edit Facility"}),n.jsx("button",{onClick:()=>r?.(g.id),className:"bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700",children:"Schedule Maintenance"})]})]})}):null,{})]})}}};