"use strict";exports.id=9279,exports.ids=[9279],exports.modules={18791:(e,s,t)=>{t.d(s,{Zb:()=>d,aY:()=>x,SZ:()=>m,Ol:()=>c,ll:()=>o});var a=t(95344),l=t(3729),r=t(56815),i=t(79377);function n(...e){return(0,i.m6)((0,r.W)(e))}let d=l.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:n("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));d.displayName="Card";let c=l.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:n("flex flex-col space-y-1.5 p-6",e),...s}));c.displayName="CardHeader";let o=l.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:n("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let m=l.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:n("text-sm text-muted-foreground",e),...s}));m.displayName="CardDescription";let x=l.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:n("p-6 pt-0",e),...s}));x.displayName="CardContent",l.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:n("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},19279:(e,s,t)=>{t.d(s,{z:()=>i});var a=t(95344),l=t(3729),r=t(18791);function i({tenantId:e,onCreateRequest:s,onAssignRequest:t,onUpdateStatus:i,onAddFeedback:n}){let[d,c]=(0,l.useState)("requests"),[o,m]=(0,l.useState)(""),[x,u]=(0,l.useState)("all"),[h,p]=(0,l.useState)("all"),[g,j]=(0,l.useState)("all"),[b,f]=(0,l.useState)(null),N=[{id:"1",requestId:"MNT-2024-001",studentId:"STU-2024-001",studentName:"Rahul Sharma",roomNumber:"A-101",hostelBlock:"Block A",category:"electrical",title:"Fan not working",description:"The ceiling fan in my room has stopped working since yesterday. It makes a strange noise when switched on.",priority:"medium",status:"assigned",submittedDate:"2024-02-01",assignedTo:"Ravi Kumar (Electrician)",assignedDate:"2024-02-01",estimatedCost:500,notes:"Likely needs new capacitor"},{id:"2",requestId:"MNT-2024-002",studentId:"STU-2024-002",studentName:"Priya Singh",roomNumber:"B-201",hostelBlock:"Block B",category:"plumbing",title:"Bathroom tap leaking",description:"The bathroom tap has been leaking continuously for the past 3 days. Water is being wasted.",priority:"high",status:"completed",submittedDate:"2024-01-28",assignedTo:"Suresh Yadav (Plumber)",assignedDate:"2024-01-28",completedDate:"2024-01-29",estimatedCost:200,actualCost:150,feedback:{rating:5,comment:"Quick and efficient service. Problem resolved completely.",submittedDate:"2024-01-30"}},{id:"3",requestId:"MNT-2024-003",studentId:"STU-2024-003",studentName:"Amit Kumar",roomNumber:"A-101",hostelBlock:"Block A",category:"internet",title:"Wi-Fi not working",description:"Internet connection is very slow and frequently disconnects. Unable to attend online classes.",priority:"urgent",status:"submitted",submittedDate:"2024-02-03",notes:"Student has important exam tomorrow"}],y=[{id:"1",name:"Ravi Kumar",specialization:["Electrical","AC/Heating"],phone:"+91-9876543220",email:"<EMAIL>",currentWorkload:3,maxCapacity:5,status:"busy"},{id:"2",name:"Suresh Yadav",specialization:["Plumbing","General Maintenance"],phone:"+91-9876543221",email:"<EMAIL>",currentWorkload:1,maxCapacity:4,status:"available"},{id:"3",name:"Mohan Singh",specialization:["Furniture","Cleaning","General"],phone:"+91-9876543222",email:"<EMAIL>",currentWorkload:2,maxCapacity:6,status:"available"}],v=e=>{switch(e){case"submitted":return"bg-yellow-100 text-yellow-800";case"assigned":return"bg-blue-100 text-blue-800";case"in_progress":return"bg-purple-100 text-purple-800";case"completed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"low":return"bg-green-100 text-green-800";case"medium":return"bg-yellow-100 text-yellow-800";case"high":return"bg-orange-100 text-orange-800";case"urgent":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},C=e=>{switch(e){case"electrical":return"⚡";case"plumbing":return"\uD83D\uDEBF";case"furniture":return"\uD83E\uDE91";case"cleaning":return"\uD83E\uDDF9";case"ac_heating":return"❄️";case"internet":return"\uD83D\uDCF6";default:return"\uD83D\uDD27"}},k=e=>{switch(e){case"available":return"bg-green-100 text-green-800";case"busy":return"bg-yellow-100 text-yellow-800";case"off_duty":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},D=N.filter(e=>{let s=e.title.toLowerCase().includes(o.toLowerCase())||e.studentName.toLowerCase().includes(o.toLowerCase())||e.roomNumber.toLowerCase().includes(o.toLowerCase())||e.requestId.toLowerCase().includes(o.toLowerCase()),t="all"===x||e.category===x,a="all"===h||e.status===h,l="all"===g||e.priority===g;return s&&t&&a&&l});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Maintenance Requests"}),a.jsx("p",{className:"text-gray-600",children:"Manage hostel maintenance and repair requests"})]}),a.jsx("button",{onClick:s,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ New Request"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)(r.Zb,{children:[a.jsx(r.Ol,{className:"pb-3",children:a.jsx(r.ll,{className:"text-sm font-medium",children:"Total Requests"})}),a.jsx(r.aY,{children:a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:N.length})})]}),(0,a.jsxs)(r.Zb,{children:[a.jsx(r.Ol,{className:"pb-3",children:a.jsx(r.ll,{className:"text-sm font-medium",children:"Pending"})}),a.jsx(r.aY,{children:a.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:N.filter(e=>["submitted","assigned","in_progress"].includes(e.status)).length})})]}),(0,a.jsxs)(r.Zb,{children:[a.jsx(r.Ol,{className:"pb-3",children:a.jsx(r.ll,{className:"text-sm font-medium",children:"Urgent"})}),a.jsx(r.aY,{children:a.jsx("div",{className:"text-2xl font-bold text-red-600",children:N.filter(e=>"urgent"===e.priority).length})})]}),(0,a.jsxs)(r.Zb,{children:[a.jsx(r.Ol,{className:"pb-3",children:a.jsx(r.ll,{className:"text-sm font-medium",children:"Avg Resolution"})}),(0,a.jsxs)(r.aY,{children:[a.jsx("div",{className:"text-2xl font-bold text-green-600",children:"2.5"}),a.jsx("p",{className:"text-xs text-gray-500",children:"Days"})]})]})]}),a.jsx("div",{className:"border-b border-gray-200",children:(0,a.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,a.jsxs)("button",{onClick:()=>c("requests"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"requests"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:["\uD83D\uDD27 Requests (",N.length,")"]}),(0,a.jsxs)("button",{onClick:()=>c("staff"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"staff"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:["\uD83D\uDC77 Staff (",y.length,")"]})]})}),(0,a.jsxs)("div",{children:["requests"===d&&(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx(r.Zb,{children:a.jsx(r.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),a.jsx("input",{type:"text",placeholder:"Search by title, student, room, or ID",value:o,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),(0,a.jsxs)("select",{value:x,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"all",children:"All Categories"}),a.jsx("option",{value:"electrical",children:"Electrical"}),a.jsx("option",{value:"plumbing",children:"Plumbing"}),a.jsx("option",{value:"furniture",children:"Furniture"}),a.jsx("option",{value:"cleaning",children:"Cleaning"}),a.jsx("option",{value:"ac_heating",children:"AC/Heating"}),a.jsx("option",{value:"internet",children:"Internet"}),a.jsx("option",{value:"other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"all",children:"All Status"}),a.jsx("option",{value:"submitted",children:"Submitted"}),a.jsx("option",{value:"assigned",children:"Assigned"}),a.jsx("option",{value:"in_progress",children:"In Progress"}),a.jsx("option",{value:"completed",children:"Completed"}),a.jsx("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),(0,a.jsxs)("select",{value:g,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"all",children:"All Priorities"}),a.jsx("option",{value:"low",children:"Low"}),a.jsx("option",{value:"medium",children:"Medium"}),a.jsx("option",{value:"high",children:"High"}),a.jsx("option",{value:"urgent",children:"Urgent"})]})]}),a.jsx("div",{className:"flex items-end",children:a.jsx("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Requests"})})]})})}),a.jsx("div",{className:"space-y-4",children:D.map(e=>a.jsx(r.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:a.jsx(r.aY,{className:"pt-6",children:a.jsx("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx("span",{className:"text-2xl",children:C(e.category)}),a.jsx("h3",{className:"text-lg font-semibold",children:e.title}),a.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${w(e.priority)}`,children:e.priority.toUpperCase()}),a.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${v(e.status)}`,children:e.status.replace("_"," ").toUpperCase()})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Student: ",e.studentName]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Room: ",e.roomNumber," (",e.hostelBlock,")"]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Request ID: ",e.requestId]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Submitted: ",e.submittedDate]}),e.assignedTo&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Assigned to: ",e.assignedTo]}),e.estimatedCost&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Est. Cost: ₹",e.estimatedCost]})]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:[e.description.substring(0,150),"..."]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Category: ",e.category.replace("_"," ").toUpperCase()]}),a.jsx("button",{onClick:()=>f(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"})]})]})})})},e.id))})]}),"staff"===d&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h2",{className:"text-lg font-semibold",children:"Maintenance Staff"}),a.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Add Staff"})]}),a.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:y.map(e=>(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx(r.ll,{className:"text-lg",children:e.name}),a.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${k(e.status)}`,children:e.status.replace("_"," ").toUpperCase()})]}),a.jsx(r.SZ,{children:e.specialization.join(", ")})]}),a.jsx(r.aY,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Phone:"}),a.jsx("span",{className:"font-medium",children:e.phone})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Email:"}),a.jsx("span",{className:"font-medium",children:e.email})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Workload:"}),(0,a.jsxs)("span",{className:"font-medium",children:[e.currentWorkload,"/",e.maxCapacity]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.currentWorkload/e.maxCapacity*100}%`}})}),a.jsx("div",{className:"flex justify-end",children:a.jsx("button",{className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"})})]})})]},e.id))})]})]}),b&&a.jsx(()=>b?a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h3",{className:"text-xl font-semibold",children:b.title}),a.jsx("button",{onClick:()=>f(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium mb-3",children:"Request Details"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Request ID:"})," ",b.requestId]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Student:"})," ",b.studentName]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Room:"})," ",b.roomNumber," (",b.hostelBlock,")"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Category:"}),(0,a.jsxs)("span",{className:"ml-2",children:[C(b.category)," ",b.category.replace("_"," ").toUpperCase()]})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Priority:"}),a.jsx("span",{className:`ml-2 px-2 py-1 text-xs rounded-full ${w(b.priority)}`,children:b.priority.toUpperCase()})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Status:"}),a.jsx("span",{className:`ml-2 px-2 py-1 text-xs rounded-full ${v(b.status)}`,children:b.status.replace("_"," ").toUpperCase()})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Submitted:"})," ",b.submittedDate]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium mb-3",children:"Assignment & Progress"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[b.assignedTo&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Assigned to:"})," ",b.assignedTo]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Assigned on:"})," ",b.assignedDate]})]}),b.completedDate&&(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Completed on:"})," ",b.completedDate]}),b.estimatedCost&&(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Estimated Cost:"})," ₹",b.estimatedCost]}),b.actualCost&&(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Actual Cost:"})," ₹",b.actualCost]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h4",{className:"font-medium mb-3",children:"Description"}),a.jsx("p",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded",children:b.description})]}),b.notes&&(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h4",{className:"font-medium mb-3",children:"Notes"}),a.jsx("p",{className:"text-sm text-gray-600 bg-yellow-50 p-3 rounded",children:b.notes})]}),b.feedback&&(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h4",{className:"font-medium mb-3",children:"Student Feedback"}),(0,a.jsxs)("div",{className:"bg-green-50 p-3 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx("span",{className:"font-medium",children:"Rating:"}),a.jsx("div",{className:"flex",children:[1,2,3,4,5].map(e=>a.jsx("span",{className:e<=b.feedback.rating?"text-yellow-400":"text-gray-300",children:"⭐"},e))}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["(",b.feedback.rating,"/5)"]})]}),a.jsx("p",{className:"text-sm text-gray-600",children:b.feedback.comment}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Submitted on: ",b.feedback.submittedDate]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:["submitted"===b.status&&(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("select",{className:"px-3 py-2 border border-gray-300 rounded text-sm",children:[a.jsx("option",{value:"",children:"Assign to Staff"}),y.filter(e=>"available"===e.status).map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.name," (",e.specialization.join(", "),")"]},e.id))]}),a.jsx("button",{onClick:()=>t?.(b.id,"selected-staff-id"),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Assign"})]}),"assigned"===b.status&&a.jsx("button",{onClick:()=>i?.(b.id,"in_progress"),className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700",children:"Mark In Progress"}),"in_progress"===b.status&&a.jsx("button",{onClick:()=>i?.(b.id,"completed"),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Mark Completed"}),a.jsx("button",{className:"bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700",children:"Edit Request"})]})]})}):null,{})]})}}};