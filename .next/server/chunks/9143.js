"use strict";exports.id=9143,exports.ids=[9143],exports.modules={18791:(e,s,a)=>{a.d(s,{Zb:()=>r,aY:()=>x,SZ:()=>m,Ol:()=>c,ll:()=>o});var t=a(95344),l=a(3729),n=a(56815),i=a(79377);function d(...e){return(0,i.m6)((0,n.W)(e))}let r=l.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));r.displayName="Card";let c=l.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:d("flex flex-col space-y-1.5 p-6",e),...s}));c.displayName="CardHeader";let o=l.forwardRef(({className:e,...s},a)=>t.jsx("h3",{ref:a,className:d("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let m=l.forwardRef(({className:e,...s},a)=>t.jsx("p",{ref:a,className:d("text-sm text-muted-foreground",e),...s}));m.displayName="CardDescription";let x=l.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:d("p-6 pt-0",e),...s}));x.displayName="CardContent",l.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:d("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},89143:(e,s,a)=>{a.d(s,{s:()=>i});var t=a(95344),l=a(3729),n=a(18791);function i({tenantId:e,onViewResident:s,onEditResident:a,onCheckOut:i,onAddDisciplinaryRecord:d}){let[r,c]=(0,l.useState)(""),[o,m]=(0,l.useState)("all"),[x,u]=(0,l.useState)("all"),[h,p]=(0,l.useState)("all"),[j,f]=(0,l.useState)(null),g=[{id:"1",studentId:"STU-2024-001",firstName:"Rahul",lastName:"Sharma",fullName:"Rahul Sharma",email:"<EMAIL>",phone:"+91-9876543210",course:"Computer Science",year:2,gender:"male",dateOfBirth:"2002-05-15",address:{permanent:"123 Main Street, Sector 15",city:"Delhi",state:"Delhi",pincode:"110001",country:"India"},emergencyContact:{name:"Suresh Sharma",relationship:"Father",phone:"+91-**********",email:"<EMAIL>"},roomDetails:{roomId:"1",roomNumber:"A-101",hostelBlock:"Block A",bedNumber:"Bed 1",checkInDate:"2024-01-15",rent:15e3},documents:[{type:"Aadhar Card",number:"1234-5678-9012",verified:!0},{type:"Student ID",number:"STU-2024-001",verified:!0}],medicalInfo:{bloodGroup:"O+",allergies:["Peanuts"],medications:[],emergencyMedicalContact:"+91-**********"},disciplinaryRecords:[],feeStatus:{totalDue:45e3,paid:3e4,pending:15e3,lastPayment:"2024-01-15",nextDue:"2024-02-15"},status:"active",joinDate:"2024-01-15",lastUpdated:"2024-01-25"},{id:"2",studentId:"STU-2024-002",firstName:"Priya",lastName:"Singh",fullName:"Priya Singh",email:"<EMAIL>",phone:"+91-**********",course:"Mechanical Engineering",year:1,gender:"female",dateOfBirth:"2003-08-22",address:{permanent:"456 Park Avenue, Model Town",city:"Mumbai",state:"Maharashtra",pincode:"400001",country:"India"},emergencyContact:{name:"Rajesh Singh",relationship:"Father",phone:"+91-**********",email:"<EMAIL>"},roomDetails:{roomId:"3",roomNumber:"B-201",hostelBlock:"Block B",bedNumber:"Bed 1",checkInDate:"2024-01-20",rent:12e3},documents:[{type:"Aadhar Card",number:"**************",verified:!0},{type:"Student ID",number:"STU-2024-002",verified:!0}],medicalInfo:{bloodGroup:"A+",allergies:[],medications:["Vitamin D"],emergencyMedicalContact:"+91-**********"},disciplinaryRecords:[{date:"2024-01-22",type:"warning",description:"Late return to hostel",action:"Verbal warning issued",resolved:!0}],feeStatus:{totalDue:36e3,paid:36e3,pending:0,lastPayment:"2024-01-20",nextDue:"2024-02-20"},status:"active",joinDate:"2024-01-20",lastUpdated:"2024-01-28"},{id:"3",studentId:"STU-2024-003",firstName:"Amit",lastName:"Kumar",fullName:"Amit Kumar",email:"<EMAIL>",phone:"+91-**********",course:"Business Administration",year:3,gender:"male",dateOfBirth:"2001-12-10",address:{permanent:"789 Garden Road, Civil Lines",city:"Bangalore",state:"Karnataka",pincode:"560001",country:"India"},emergencyContact:{name:"Sunita Kumar",relationship:"Mother",phone:"+91-**********",email:"<EMAIL>"},roomDetails:{roomId:"1",roomNumber:"A-101",hostelBlock:"Block A",bedNumber:"Bed 2",checkInDate:"2024-01-15",rent:15e3},documents:[{type:"Aadhar Card",number:"**************",verified:!0},{type:"Student ID",number:"STU-2024-003",verified:!0},{type:"Passport",number:"P1234567",verified:!1}],medicalInfo:{bloodGroup:"B+",allergies:["Dust"],medications:["Inhaler"],emergencyMedicalContact:"+91-**********"},disciplinaryRecords:[],feeStatus:{totalDue:45e3,paid:45e3,pending:0,lastPayment:"2024-01-15",nextDue:"2024-02-15"},status:"active",joinDate:"2024-01-15",lastUpdated:"2024-01-30"}],N=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"inactive":default:return"bg-gray-100 text-gray-800";case"suspended":return"bg-red-100 text-red-800";case"graduated":return"bg-blue-100 text-blue-800";case"transferred":return"bg-purple-100 text-purple-800"}},v=e=>{switch(e){case"warning":return"bg-yellow-100 text-yellow-800";case"fine":return"bg-orange-100 text-orange-800";case"suspension":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},b=g.filter(e=>{let s=e.fullName.toLowerCase().includes(r.toLowerCase())||e.studentId.toLowerCase().includes(r.toLowerCase())||e.course.toLowerCase().includes(r.toLowerCase())||e.roomDetails.roomNumber.toLowerCase().includes(r.toLowerCase()),a="all"===o||e.roomDetails.hostelBlock===o,t="all"===x||e.status===x,l="all"===h||e.year.toString()===h;return s&&a&&t&&l});return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Resident Management"}),t.jsx("p",{className:"text-gray-600",children:"Manage hostel residents and their information"})]}),t.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Add Resident"})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,t.jsxs)(n.Zb,{children:[t.jsx(n.Ol,{className:"pb-3",children:t.jsx(n.ll,{className:"text-sm font-medium",children:"Total Residents"})}),t.jsx(n.aY,{children:t.jsx("div",{className:"text-2xl font-bold text-blue-600",children:g.length})})]}),(0,t.jsxs)(n.Zb,{children:[t.jsx(n.Ol,{className:"pb-3",children:t.jsx(n.ll,{className:"text-sm font-medium",children:"Active Residents"})}),t.jsx(n.aY,{children:t.jsx("div",{className:"text-2xl font-bold text-green-600",children:g.filter(e=>"active"===e.status).length})})]}),(0,t.jsxs)(n.Zb,{children:[t.jsx(n.Ol,{className:"pb-3",children:t.jsx(n.ll,{className:"text-sm font-medium",children:"Pending Fees"})}),t.jsx(n.aY,{children:(0,t.jsxs)("div",{className:"text-2xl font-bold text-orange-600",children:["₹",g.reduce((e,s)=>e+s.feeStatus.pending,0).toLocaleString()]})})]}),(0,t.jsxs)(n.Zb,{children:[t.jsx(n.Ol,{className:"pb-3",children:t.jsx(n.ll,{className:"text-sm font-medium",children:"Disciplinary Cases"})}),t.jsx(n.aY,{children:t.jsx("div",{className:"text-2xl font-bold text-red-600",children:g.reduce((e,s)=>e+s.disciplinaryRecords.filter(e=>!e.resolved).length,0)})})]})]}),t.jsx(n.Zb,{children:t.jsx(n.aY,{className:"pt-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),t.jsx("input",{type:"text",placeholder:"Search by name, ID, course, or room",value:r,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Block"}),(0,t.jsxs)("select",{value:o,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[t.jsx("option",{value:"all",children:"All Blocks"}),t.jsx("option",{value:"Block A",children:"Block A"}),t.jsx("option",{value:"Block B",children:"Block B"}),t.jsx("option",{value:"Block C",children:"Block C"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,t.jsxs)("select",{value:x,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[t.jsx("option",{value:"all",children:"All Status"}),t.jsx("option",{value:"active",children:"Active"}),t.jsx("option",{value:"inactive",children:"Inactive"}),t.jsx("option",{value:"suspended",children:"Suspended"}),t.jsx("option",{value:"graduated",children:"Graduated"}),t.jsx("option",{value:"transferred",children:"Transferred"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Year"}),(0,t.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[t.jsx("option",{value:"all",children:"All Years"}),t.jsx("option",{value:"1",children:"1st Year"}),t.jsx("option",{value:"2",children:"2nd Year"}),t.jsx("option",{value:"3",children:"3rd Year"}),t.jsx("option",{value:"4",children:"4th Year"})]})]}),t.jsx("div",{className:"flex items-end",children:t.jsx("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Residents"})})]})})}),t.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:b.map(e=>(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx(n.ll,{className:"text-lg",children:e.fullName}),t.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${N(e.status)}`,children:e.status.toUpperCase()})]}),(0,t.jsxs)(n.SZ,{children:[e.studentId," • ",e.course]})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Room:"}),t.jsx("span",{className:"font-medium",children:e.roomDetails.roomNumber})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Block:"}),t.jsx("span",{className:"font-medium",children:e.roomDetails.hostelBlock})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Year:"}),t.jsx("span",{className:"font-medium",children:e.year})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Check-in:"}),t.jsx("span",{className:"font-medium",children:e.roomDetails.checkInDate})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Rent:"}),(0,t.jsxs)("span",{className:"font-medium",children:["₹",e.roomDetails.rent.toLocaleString()]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Fee Status:"}),t.jsx("span",{className:`font-medium ${e.feeStatus.pending>0?"text-red-600":"text-green-600"}`,children:e.feeStatus.pending>0?`₹${e.feeStatus.pending.toLocaleString()} Due`:"Paid"})]})]}),(0,t.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex space-x-2",children:[e.disciplinaryRecords.some(e=>!e.resolved)&&t.jsx("span",{className:"px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full",children:"Disciplinary"}),e.feeStatus.pending>0&&t.jsx("span",{className:"px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full",children:"Fee Due"})]}),t.jsx("button",{onClick:()=>f(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"})]})]})]},e.id))}),j&&t.jsx(()=>j?t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-5xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[t.jsx("h3",{className:"text-xl font-semibold",children:j.fullName}),t.jsx("button",{onClick:()=>f(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium mb-3",children:"Personal Information"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Student ID:"})," ",j.studentId]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Email:"})," ",j.email]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Phone:"})," ",j.phone]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Date of Birth:"})," ",j.dateOfBirth]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Gender:"})," ",j.gender]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Course:"})," ",j.course]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Year:"})," ",j.year]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Status:"}),t.jsx("span",{className:`ml-2 px-2 py-1 text-xs rounded-full ${N(j.status)}`,children:j.status.toUpperCase()})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium mb-3",children:"Room Information"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Room:"})," ",j.roomDetails.roomNumber]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Block:"})," ",j.roomDetails.hostelBlock]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Bed:"})," ",j.roomDetails.bedNumber]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Check-in:"})," ",j.roomDetails.checkInDate]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Rent:"})," ₹",j.roomDetails.rent.toLocaleString(),"/month"]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Join Date:"})," ",j.joinDate]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Last Updated:"})," ",j.lastUpdated]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium mb-3",children:"Address"}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded",children:[t.jsx("div",{children:j.address.permanent}),(0,t.jsxs)("div",{children:[j.address.city,", ",j.address.state]}),(0,t.jsxs)("div",{children:[j.address.pincode,", ",j.address.country]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium mb-3",children:"Emergency Contact"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Name:"})," ",j.emergencyContact.name]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Relationship:"})," ",j.emergencyContact.relationship]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Phone:"})," ",j.emergencyContact.phone]}),j.emergencyContact.email&&(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Email:"})," ",j.emergencyContact.email]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium mb-3",children:"Documents"}),t.jsx("div",{className:"space-y-2",children:j.documents.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 border rounded",children:[(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium text-sm",children:e.type}),t.jsx("div",{className:"text-sm text-gray-600",children:e.number})]}),t.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${e.verified?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.verified?"Verified":"Pending"})]},s))})]}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium mb-3",children:"Medical Information"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Blood Group:"})," ",j.medicalInfo.bloodGroup]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Emergency Medical Contact:"})," ",j.medicalInfo.emergencyMedicalContact]}),j.medicalInfo.allergies&&j.medicalInfo.allergies.length>0&&(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Allergies:"}),t.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:j.medicalInfo.allergies.map((e,s)=>t.jsx("span",{className:"px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full",children:e},s))})]}),j.medicalInfo.medications&&j.medicalInfo.medications.length>0&&(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Medications:"}),t.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:j.medicalInfo.medications.map((e,s)=>t.jsx("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e},s))})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium mb-3",children:"Fee Status"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Total Due:"})," ₹",j.feeStatus.totalDue.toLocaleString()]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Paid:"})," ₹",j.feeStatus.paid.toLocaleString()]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Pending:"})," ₹",j.feeStatus.pending.toLocaleString()]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Last Payment:"})," ",j.feeStatus.lastPayment]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Next Due:"})," ",j.feeStatus.nextDue]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium mb-3",children:"Disciplinary Records"}),j.disciplinaryRecords.length>0?t.jsx("div",{className:"space-y-2",children:j.disciplinaryRecords.map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[t.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${v(e.type)}`,children:e.type.toUpperCase()}),t.jsx("span",{className:"text-xs text-gray-500",children:e.date})]}),t.jsx("div",{className:"text-sm",children:e.description}),t.jsx("div",{className:"text-xs text-gray-600 mt-1",children:e.action})]},s))}):t.jsx("p",{className:"text-sm text-gray-500",children:"No disciplinary records"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[t.jsx("button",{onClick:()=>a?.(j.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Edit Resident"}),t.jsx("button",{onClick:()=>d?.(j.id),className:"bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700",children:"Add Record"}),t.jsx("button",{onClick:()=>i?.(j.id),className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700",children:"Check Out"})]})]})}):null,{})]})}}};