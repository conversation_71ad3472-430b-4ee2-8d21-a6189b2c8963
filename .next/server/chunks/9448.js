"use strict";exports.id=9448,exports.ids=[9448],exports.modules={18791:(e,s,t)=>{t.d(s,{Zb:()=>d,aY:()=>x,SZ:()=>m,Ol:()=>c,ll:()=>o});var a=t(95344),l=t(3729),r=t(56815),n=t(79377);function i(...e){return(0,n.m6)((0,r.W)(e))}let d=l.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));d.displayName="Card";let c=l.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:i("flex flex-col space-y-1.5 p-6",e),...s}));c.displayName="CardHeader";let o=l.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:i("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let m=l.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:i("text-sm text-muted-foreground",e),...s}));m.displayName="CardDescription";let x=l.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:i("p-6 pt-0",e),...s}));x.displayName="CardContent",l.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:i("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},19448:(e,s,t)=>{t.d(s,{n:()=>n});var a=t(95344),l=t(3729),r=t(18791);function n({tenantId:e,onAllocateRoom:s,onDeallocateRoom:t,onApproveRequest:n,onRejectRequest:i}){let[d,c]=(0,l.useState)("rooms"),[o,m]=(0,l.useState)(""),[x,u]=(0,l.useState)("all"),[p,h]=(0,l.useState)("all"),[b,j]=(0,l.useState)("all"),[g,f]=(0,l.useState)(null),N=[{id:"1",roomNumber:"A-101",hostelBlock:"Block A",floor:1,roomType:"double",capacity:2,currentOccupancy:2,amenities:["Wi-Fi","AC","Study Table","Wardrobe"],rent:15e3,currency:"INR",status:"occupied",residents:[{studentId:"STU-2024-001",studentName:"Rahul Sharma",checkInDate:"2024-01-15",bedNumber:"Bed 1"},{studentId:"STU-2024-002",studentName:"Amit Kumar",checkInDate:"2024-01-15",bedNumber:"Bed 2"}],facilities:{bathroom:"attached",ac:!0,wifi:!0,furniture:["Study Table","Chair","Wardrobe","Bed"],appliances:["Fan","Light","AC"]},lastMaintenance:"2024-01-10",nextInspection:"2024-02-15",notes:"Recently renovated room with new furniture"},{id:"2",roomNumber:"A-102",hostelBlock:"Block A",floor:1,roomType:"single",capacity:1,currentOccupancy:0,amenities:["Wi-Fi","AC","Study Table","Wardrobe"],rent:2e4,currency:"INR",status:"available",residents:[],facilities:{bathroom:"attached",ac:!0,wifi:!0,furniture:["Study Table","Chair","Wardrobe","Bed"],appliances:["Fan","Light","AC"]},lastMaintenance:"2024-01-05",nextInspection:"2024-02-10",notes:"Premium single room with balcony"},{id:"3",roomNumber:"B-201",hostelBlock:"Block B",floor:2,roomType:"triple",capacity:3,currentOccupancy:1,amenities:["Wi-Fi","Fan","Study Table","Wardrobe"],rent:12e3,currency:"INR",status:"occupied",residents:[{studentId:"STU-2024-003",studentName:"Priya Singh",checkInDate:"2024-01-20",bedNumber:"Bed 1"}],facilities:{bathroom:"shared",ac:!1,wifi:!0,furniture:["Study Table","Chair","Wardrobe","Bed"],appliances:["Fan","Light"]},lastMaintenance:"2023-12-20",nextInspection:"2024-02-20",notes:"Shared bathroom facility, 2 beds available"}],y=[{id:"1",requestId:"REQ-2024-001",studentId:"STU-2024-004",studentName:"Neha Patel",studentEmail:"<EMAIL>",course:"Computer Science",year:2,gender:"female",preferredRoomType:"single",preferredBlock:"Block A",specialRequirements:["Ground Floor","AC Required"],requestDate:"2024-01-25",priority:"high",status:"pending"},{id:"2",requestId:"REQ-2024-002",studentId:"STU-2024-005",studentName:"Vikash Gupta",studentEmail:"<EMAIL>",course:"Mechanical Engineering",year:1,gender:"male",preferredRoomType:"double",specialRequirements:["Wi-Fi Required"],requestDate:"2024-01-28",priority:"medium",status:"approved"},{id:"3",requestId:"REQ-2024-003",studentId:"STU-2024-006",studentName:"Anjali Verma",studentEmail:"<EMAIL>",course:"Business Administration",year:3,gender:"female",preferredRoomType:"triple",preferredBlock:"Block B",requestDate:"2024-01-30",priority:"low",status:"allocated",allocatedRoomId:"3",allocatedDate:"2024-01-31"}],v=e=>{switch(e){case"available":return"bg-green-100 text-green-800";case"occupied":return"bg-blue-100 text-blue-800";case"maintenance":return"bg-yellow-100 text-yellow-800";case"reserved":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"single":return"bg-purple-100 text-purple-800";case"double":return"bg-blue-100 text-blue-800";case"triple":return"bg-green-100 text-green-800";case"quad":return"bg-orange-100 text-orange-800";case"suite":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},C=e=>{switch(e){case"high":return"bg-red-100 text-red-800";case"medium":return"bg-yellow-100 text-yellow-800";case"low":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},R=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"allocated":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},k=N.filter(e=>{let s=e.roomNumber.toLowerCase().includes(o.toLowerCase())||e.hostelBlock.toLowerCase().includes(o.toLowerCase())||e.residents.some(e=>e.studentName.toLowerCase().includes(o.toLowerCase())),t="all"===x||e.hostelBlock===x,a="all"===p||e.status===p,l="all"===b||e.roomType===b;return s&&t&&a&&l}),B=y.filter(e=>e.studentName.toLowerCase().includes(o.toLowerCase())||e.requestId.toLowerCase().includes(o.toLowerCase())||e.course.toLowerCase().includes(o.toLowerCase()));return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Room Allocation"}),a.jsx("p",{className:"text-gray-600",children:"Manage hostel rooms and student allocations"})]}),a.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Add Room"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)(r.Zb,{children:[a.jsx(r.Ol,{className:"pb-3",children:a.jsx(r.ll,{className:"text-sm font-medium",children:"Total Rooms"})}),a.jsx(r.aY,{children:a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:N.length})})]}),(0,a.jsxs)(r.Zb,{children:[a.jsx(r.Ol,{className:"pb-3",children:a.jsx(r.ll,{className:"text-sm font-medium",children:"Available Rooms"})}),a.jsx(r.aY,{children:a.jsx("div",{className:"text-2xl font-bold text-green-600",children:N.filter(e=>"available"===e.status||e.currentOccupancy<e.capacity).length})})]}),(0,a.jsxs)(r.Zb,{children:[a.jsx(r.Ol,{className:"pb-3",children:a.jsx(r.ll,{className:"text-sm font-medium",children:"Occupancy Rate"})}),a.jsx(r.aY,{children:(0,a.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[Math.round(N.reduce((e,s)=>e+s.currentOccupancy,0)/N.reduce((e,s)=>e+s.capacity,0)*100),"%"]})})]}),(0,a.jsxs)(r.Zb,{children:[a.jsx(r.Ol,{className:"pb-3",children:a.jsx(r.ll,{className:"text-sm font-medium",children:"Pending Requests"})}),a.jsx(r.aY,{children:a.jsx("div",{className:"text-2xl font-bold text-orange-600",children:y.filter(e=>"pending"===e.status).length})})]})]}),a.jsx("div",{className:"border-b border-gray-200",children:(0,a.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,a.jsxs)("button",{onClick:()=>c("rooms"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"rooms"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:["\uD83C\uDFE0 Rooms (",N.length,")"]}),(0,a.jsxs)("button",{onClick:()=>c("requests"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"requests"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:["\uD83D\uDCCB Allocation Requests (",y.length,")"]})]})}),(0,a.jsxs)("div",{children:["rooms"===d&&(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx(r.Zb,{children:a.jsx(r.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),a.jsx("input",{type:"text",placeholder:"Search by room number, block, or resident",value:o,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Block"}),(0,a.jsxs)("select",{value:x,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"all",children:"All Blocks"}),a.jsx("option",{value:"Block A",children:"Block A"}),a.jsx("option",{value:"Block B",children:"Block B"}),a.jsx("option",{value:"Block C",children:"Block C"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsxs)("select",{value:p,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"all",children:"All Status"}),a.jsx("option",{value:"available",children:"Available"}),a.jsx("option",{value:"occupied",children:"Occupied"}),a.jsx("option",{value:"maintenance",children:"Maintenance"}),a.jsx("option",{value:"reserved",children:"Reserved"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Room Type"}),(0,a.jsxs)("select",{value:b,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"all",children:"All Types"}),a.jsx("option",{value:"single",children:"Single"}),a.jsx("option",{value:"double",children:"Double"}),a.jsx("option",{value:"triple",children:"Triple"}),a.jsx("option",{value:"quad",children:"Quad"}),a.jsx("option",{value:"suite",children:"Suite"})]})]}),a.jsx("div",{className:"flex items-end",children:a.jsx("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Rooms"})})]})})}),a.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:k.map(e=>(0,a.jsxs)(r.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx(r.ll,{className:"text-lg",children:e.roomNumber}),a.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${v(e.status)}`,children:e.status.toUpperCase()})]}),(0,a.jsxs)(r.SZ,{children:[e.hostelBlock," • Floor ",e.floor]})]}),a.jsx(r.aY,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${w(e.roomType)}`,children:e.roomType.toUpperCase()}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:["₹",e.rent.toLocaleString(),"/month"]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Occupancy:"}),(0,a.jsxs)("span",{className:"font-medium",children:[e.currentOccupancy,"/",e.capacity]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Bathroom:"}),a.jsx("span",{className:"font-medium",children:e.facilities.bathroom})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"AC:"}),a.jsx("span",{className:"font-medium",children:e.facilities.ac?"✅":"❌"})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.amenities.slice(0,3).map((e,s)=>a.jsx("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full",children:e},s)),e.amenities.length>3&&(0,a.jsxs)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full",children:["+",e.amenities.length-3]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["Next inspection: ",e.nextInspection]}),a.jsx("button",{onClick:()=>f(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"})]})]})})]},e.id))})]}),"requests"===d&&(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx(r.Zb,{children:a.jsx(r.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),a.jsx("input",{type:"text",placeholder:"Search by student name, ID, or course",value:o,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),a.jsx("div",{className:"flex items-end",children:a.jsx("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Requests"})})]})})}),a.jsx("div",{className:"space-y-4",children:B.map(e=>a.jsx(r.Zb,{children:a.jsx(r.aY,{className:"pt-6",children:a.jsx("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx("h3",{className:"text-lg font-semibold",children:e.studentName}),a.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${C(e.priority)}`,children:e.priority.toUpperCase()}),a.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${R(e.status)}`,children:e.status.toUpperCase()})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium text-gray-900",children:"Student Details"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["ID: ",e.studentId]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Email: ",e.studentEmail]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Course: ",e.course]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Year: ",e.year]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Gender: ",e.gender]})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium text-gray-900",children:"Preferences"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Room Type: ",e.preferredRoomType]}),e.preferredBlock&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Block: ",e.preferredBlock]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Request Date: ",e.requestDate]}),e.allocatedDate&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Allocated: ",e.allocatedDate]})]})]}),e.specialRequirements&&e.specialRequirements.length>0&&(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Special Requirements"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:e.specialRequirements.map((e,s)=>a.jsx("span",{className:"px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full",children:e},s))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Request ID: ",e.requestId]}),"pending"===e.status&&(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{onClick:()=>n?.(e.id),className:"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700",children:"Approve"}),a.jsx("button",{onClick:()=>i?.(e.id,"No suitable rooms available"),className:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700",children:"Reject"})]}),"approved"===e.status&&(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("select",{className:"px-3 py-1 border border-gray-300 rounded text-sm",children:[a.jsx("option",{value:"",children:"Select Room"}),N.filter(e=>"available"===e.status&&e.currentOccupancy<e.capacity).map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.roomNumber," (",e.hostelBlock,")"]},e.id))]}),a.jsx("button",{onClick:()=>s?.(e.id,"selected-room-id"),className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Allocate"})]})]})]})})})},e.id))})]})]}),g&&a.jsx(()=>g?a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold",children:["Room ",g.roomNumber]}),a.jsx("button",{onClick:()=>f(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium mb-3",children:"Room Details"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Room Number:"})," ",g.roomNumber]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Block:"})," ",g.hostelBlock]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Floor:"})," ",g.floor]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Type:"}),a.jsx("span",{className:`ml-2 px-2 py-1 text-xs rounded-full ${w(g.roomType)}`,children:g.roomType.toUpperCase()})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Capacity:"})," ",g.capacity]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Current Occupancy:"})," ",g.currentOccupancy]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Rent:"})," ₹",g.rent.toLocaleString(),"/month"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Status:"}),a.jsx("span",{className:`ml-2 px-2 py-1 text-xs rounded-full ${v(g.status)}`,children:g.status.toUpperCase()})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium mb-3",children:"Facilities"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Bathroom:"})," ",g.facilities.bathroom]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"AC:"})," ",g.facilities.ac?"✅":"❌"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Wi-Fi:"})," ",g.facilities.wifi?"✅":"❌"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Last Maintenance:"})," ",g.lastMaintenance]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Next Inspection:"})," ",g.nextInspection]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h4",{className:"font-medium mb-3",children:"Amenities"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:g.amenities.map((e,s)=>a.jsx("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e},s))})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h4",{className:"font-medium mb-3",children:"Furniture & Appliances"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h5",{className:"font-medium text-sm mb-2",children:"Furniture"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:g.facilities.furniture.map((e,s)=>a.jsx("span",{className:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full",children:e},s))})]}),(0,a.jsxs)("div",{children:[a.jsx("h5",{className:"font-medium text-sm mb-2",children:"Appliances"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:g.facilities.appliances.map((e,s)=>a.jsx("span",{className:"px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full",children:e},s))})]})]})]}),g.residents.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h4",{className:"font-medium mb-3",children:"Current Residents"}),a.jsx("div",{className:"space-y-2",children:g.residents.map((e,s)=>a.jsx("div",{className:"border rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h5",{className:"font-medium",children:e.studentName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Student ID: ",e.studentId]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Check-in: ",e.checkInDate]}),e.bedNumber&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Bed: ",e.bedNumber]})]}),a.jsx("button",{onClick:()=>t?.(g.id,e.studentId),className:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700",children:"Deallocate"})]})},s))})]}),g.notes&&(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h4",{className:"font-medium mb-3",children:"Notes"}),a.jsx("p",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded",children:g.notes})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[a.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Edit Room"}),a.jsx("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Schedule Maintenance"}),a.jsx("button",{className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700",children:"Generate Report"})]})]})}):null,{})]})}}};