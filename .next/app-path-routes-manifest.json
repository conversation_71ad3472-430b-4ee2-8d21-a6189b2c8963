{"/_not-found": "/_not-found", "/academic/assessments/page": "/academic/assessments", "/academic/attendance/page": "/academic/attendance", "/academic/curriculum/page": "/academic/curriculum", "/academic/grades/page": "/academic/grades", "/academic/page": "/academic", "/academic/schedule/page": "/academic/schedule", "/admissions/applications/page": "/admissions/applications", "/academic/progress/page": "/academic/progress", "/admissions/classes/page": "/admissions/classes", "/admissions/applications/new/page": "/admissions/applications/new", "/admissions/enrollment/page": "/admissions/enrollment", "/admissions/interviews/page": "/admissions/interviews", "/admissions/page": "/admissions", "/admissions/decisions/page": "/admissions/decisions", "/admissions/reports/page": "/admissions/reports", "/alumni/achievements/page": "/alumni/achievements", "/alumni/donations/page": "/alumni/donations", "/alumni/events/page": "/alumni/events", "/alumni/page": "/alumni", "/alumni/networking/page": "/alumni/networking", "/api/health/route": "/api/health", "/alumni/directory/page": "/alumni/directory", "/auth/login/page": "/auth/login", "/auth/callback/route": "/auth/callback", "/alumni/jobs/page": "/alumni/jobs", "/financials/fee-structure/page": "/financials/fee-structure", "/financials/billing/page": "/financials/billing", "/financials/financial-aid/page": "/financials/financial-aid", "/financials/history/page": "/financials/history", "/financials/page": "/financials", "/financials/payments/page": "/financials/payments", "/financials/reports/page": "/financials/reports", "/hostel/facilities/page": "/hostel/facilities", "/hostel/page": "/hostel", "/hostel/maintenance/page": "/hostel/maintenance", "/hostel/residents/page": "/hostel/residents", "/hostel/rooms/page": "/hostel/rooms", "/hostel/visitors/page": "/hostel/visitors", "/library/catalog/page": "/library/catalog", "/library/circulation/page": "/library/circulation", "/library/digital/page": "/library/digital", "/library/inventory/page": "/library/inventory", "/library/members/page": "/library/members", "/library/reservations/page": "/library/reservations", "/library/page": "/library", "/test/page": "/test", "/page": "/", "/dashboard/academic/attendance/page": "/dashboard/academic/attendance", "/dashboard/academic/assessments/page": "/dashboard/academic/assessments", "/dashboard/academic/curriculum/page": "/dashboard/academic/curriculum", "/dashboard/academic/page": "/dashboard/academic", "/dashboard/academic/grades/page": "/dashboard/academic/grades", "/dashboard/academic/schedule/page": "/dashboard/academic/schedule", "/dashboard/academic/progress/page": "/dashboard/academic/progress", "/dashboard/admissions/applications/page": "/dashboard/admissions/applications", "/dashboard/admissions/decisions/page": "/dashboard/admissions/decisions", "/dashboard/admissions/interviews/page": "/dashboard/admissions/interviews", "/dashboard/admissions/enrollment/page": "/dashboard/admissions/enrollment", "/dashboard/admissions/page": "/dashboard/admissions", "/dashboard/alumni/directory/page": "/dashboard/alumni/directory", "/dashboard/admissions/reports/page": "/dashboard/admissions/reports", "/dashboard/alumni/jobs/page": "/dashboard/alumni/jobs", "/dashboard/alumni/events/page": "/dashboard/alumni/events", "/dashboard/alumni/page": "/dashboard/alumni", "/dashboard/financials/billing/page": "/dashboard/financials/billing", "/dashboard/financials/fee-structure/page": "/dashboard/financials/fee-structure", "/dashboard/financials/financial-aid/page": "/dashboard/financials/financial-aid", "/dashboard/financials/history/page": "/dashboard/financials/history", "/dashboard/financials/page": "/dashboard/financials", "/dashboard/financials/payments/page": "/dashboard/financials/payments", "/dashboard/admissions/classes/page": "/dashboard/admissions/classes", "/dashboard/hostel/billing/page": "/dashboard/hostel/billing", "/dashboard/financials/reports/page": "/dashboard/financials/reports", "/dashboard/hostel/facilities/page": "/dashboard/hostel/facilities", "/dashboard/hostel/maintenance/page": "/dashboard/hostel/maintenance", "/dashboard/hostel/page": "/dashboard/hostel", "/dashboard/hostel/residents/page": "/dashboard/hostel/residents", "/dashboard/hostel/rooms/page": "/dashboard/hostel/rooms", "/dashboard/hostel/visitors/page": "/dashboard/hostel/visitors", "/dashboard/library/catalog/page": "/dashboard/library/catalog", "/dashboard/library/inventory/page": "/dashboard/library/inventory", "/dashboard/library/digital/page": "/dashboard/library/digital", "/dashboard/library/members/page": "/dashboard/library/members", "/dashboard/library/page": "/dashboard/library", "/dashboard/page": "/dashboard", "/dashboard/teachers/page": "/dashboard/teachers", "/dashboard/teachers/profiles/page": "/dashboard/teachers/profiles", "/dashboard/teachers/evaluations/page": "/dashboard/teachers/evaluations", "/dashboard/teachers/training/page": "/dashboard/teachers/training", "/dashboard/transport/page": "/dashboard/transport", "/dashboard/transport/safety/page": "/dashboard/transport/safety", "/dashboard/transport/vehicles/page": "/dashboard/transport/vehicles", "/dashboard/transport/routes/page": "/dashboard/transport/routes", "/dashboard/transport/students/page": "/dashboard/transport/students", "/dashboard/transport/drivers/page": "/dashboard/transport/drivers", "/dashboard/teachers/schedules/page": "/dashboard/teachers/schedules"}