{"/_not-found": "/_not-found", "/academic/assessments/page": "/academic/assessments", "/academic/curriculum/page": "/academic/curriculum", "/academic/attendance/page": "/academic/attendance", "/academic/grades/page": "/academic/grades", "/academic/progress/page": "/academic/progress", "/academic/schedule/page": "/academic/schedule", "/admissions/applications/page": "/admissions/applications", "/admissions/classes/page": "/admissions/classes", "/admissions/decisions/page": "/admissions/decisions", "/academic/page": "/academic", "/admissions/enrollment/page": "/admissions/enrollment", "/admissions/page": "/admissions", "/admissions/interviews/page": "/admissions/interviews", "/admissions/reports/page": "/admissions/reports", "/alumni/achievements/page": "/alumni/achievements", "/alumni/directory/page": "/alumni/directory", "/alumni/donations/page": "/alumni/donations", "/alumni/events/page": "/alumni/events", "/alumni/jobs/page": "/alumni/jobs", "/alumni/networking/page": "/alumni/networking", "/alumni/page": "/alumni", "/api/health/route": "/api/health", "/admissions/applications/new/page": "/admissions/applications/new", "/auth/callback/route": "/auth/callback", "/auth/login/page": "/auth/login", "/financials/billing/page": "/financials/billing", "/financials/fee-structure/page": "/financials/fee-structure", "/financials/financial-aid/page": "/financials/financial-aid", "/financials/page": "/financials", "/financials/payments/page": "/financials/payments", "/financials/reports/page": "/financials/reports", "/hostel/facilities/page": "/hostel/facilities", "/hostel/maintenance/page": "/hostel/maintenance", "/hostel/page": "/hostel", "/hostel/residents/page": "/hostel/residents", "/hostel/rooms/page": "/hostel/rooms", "/hostel/visitors/page": "/hostel/visitors", "/library/catalog/page": "/library/catalog", "/library/digital/page": "/library/digital", "/library/circulation/page": "/library/circulation", "/library/inventory/page": "/library/inventory", "/library/members/page": "/library/members", "/library/reservations/page": "/library/reservations", "/page": "/", "/test/page": "/test", "/library/page": "/library", "/financials/history/page": "/financials/history", "/dashboard/academic/assessments/page": "/dashboard/academic/assessments", "/dashboard/academic/attendance/page": "/dashboard/academic/attendance", "/dashboard/academic/curriculum/page": "/dashboard/academic/curriculum", "/dashboard/academic/grades/page": "/dashboard/academic/grades", "/dashboard/academic/page": "/dashboard/academic", "/dashboard/academic/progress/page": "/dashboard/academic/progress", "/dashboard/admissions/applications/page": "/dashboard/admissions/applications", "/dashboard/academic/schedule/page": "/dashboard/academic/schedule", "/dashboard/admissions/classes/page": "/dashboard/admissions/classes", "/dashboard/admissions/decisions/page": "/dashboard/admissions/decisions", "/dashboard/admissions/enrollment/page": "/dashboard/admissions/enrollment", "/dashboard/admissions/interviews/page": "/dashboard/admissions/interviews", "/dashboard/admissions/page": "/dashboard/admissions", "/dashboard/admissions/reports/page": "/dashboard/admissions/reports", "/dashboard/alumni/directory/page": "/dashboard/alumni/directory", "/dashboard/alumni/events/page": "/dashboard/alumni/events", "/dashboard/alumni/jobs/page": "/dashboard/alumni/jobs", "/dashboard/alumni/page": "/dashboard/alumni", "/dashboard/financials/billing/page": "/dashboard/financials/billing", "/dashboard/financials/fee-structure/page": "/dashboard/financials/fee-structure", "/dashboard/financials/payments/page": "/dashboard/financials/payments", "/dashboard/financials/page": "/dashboard/financials", "/dashboard/financials/history/page": "/dashboard/financials/history", "/dashboard/hostel/billing/page": "/dashboard/hostel/billing", "/dashboard/financials/financial-aid/page": "/dashboard/financials/financial-aid", "/dashboard/hostel/page": "/dashboard/hostel", "/dashboard/hostel/maintenance/page": "/dashboard/hostel/maintenance", "/dashboard/hostel/residents/page": "/dashboard/hostel/residents", "/dashboard/library/catalog/page": "/dashboard/library/catalog", "/dashboard/hostel/visitors/page": "/dashboard/hostel/visitors", "/dashboard/library/digital/page": "/dashboard/library/digital", "/dashboard/library/inventory/page": "/dashboard/library/inventory", "/dashboard/library/page": "/dashboard/library", "/dashboard/library/members/page": "/dashboard/library/members", "/dashboard/page": "/dashboard", "/dashboard/teachers/page": "/dashboard/teachers", "/dashboard/teachers/evaluations/page": "/dashboard/teachers/evaluations", "/dashboard/teachers/profiles/page": "/dashboard/teachers/profiles", "/dashboard/teachers/schedules/page": "/dashboard/teachers/schedules", "/dashboard/teachers/training/page": "/dashboard/teachers/training", "/dashboard/hostel/facilities/page": "/dashboard/hostel/facilities", "/dashboard/transport/drivers/page": "/dashboard/transport/drivers", "/dashboard/transport/routes/page": "/dashboard/transport/routes", "/dashboard/transport/safety/page": "/dashboard/transport/safety", "/dashboard/transport/vehicles/page": "/dashboard/transport/vehicles", "/dashboard/hostel/rooms/page": "/dashboard/hostel/rooms", "/dashboard/financials/reports/page": "/dashboard/financials/reports", "/dashboard/transport/students/page": "/dashboard/transport/students", "/dashboard/transport/page": "/dashboard/transport"}