{"/_not-found": "/_not-found", "/academic/attendance/page": "/academic/attendance", "/academic/curriculum/page": "/academic/curriculum", "/academic/grades/page": "/academic/grades", "/academic/progress/page": "/academic/progress", "/academic/assessments/page": "/academic/assessments", "/academic/schedule/page": "/academic/schedule", "/admissions/decisions/page": "/admissions/decisions", "/admissions/classes/page": "/admissions/classes", "/admissions/applications/page": "/admissions/applications", "/admissions/applications/new/page": "/admissions/applications/new", "/admissions/enrollment/page": "/admissions/enrollment", "/admissions/reports/page": "/admissions/reports", "/academic/page": "/academic", "/alumni/events/page": "/alumni/events", "/alumni/achievements/page": "/alumni/achievements", "/admissions/interviews/page": "/admissions/interviews", "/alumni/jobs/page": "/alumni/jobs", "/alumni/page": "/alumni", "/admissions/page": "/admissions", "/alumni/directory/page": "/alumni/directory", "/alumni/donations/page": "/alumni/donations", "/alumni/networking/page": "/alumni/networking", "/auth/callback/route": "/auth/callback", "/auth/login/page": "/auth/login", "/financials/fee-structure/page": "/financials/fee-structure", "/financials/page": "/financials", "/financials/reports/page": "/financials/reports", "/financials/history/page": "/financials/history", "/financials/financial-aid/page": "/financials/financial-aid", "/financials/billing/page": "/financials/billing", "/hostel/facilities/page": "/hostel/facilities", "/hostel/maintenance/page": "/hostel/maintenance", "/hostel/residents/page": "/hostel/residents", "/hostel/page": "/hostel", "/hostel/visitors/page": "/hostel/visitors", "/library/catalog/page": "/library/catalog", "/library/circulation/page": "/library/circulation", "/library/digital/page": "/library/digital", "/library/inventory/page": "/library/inventory", "/library/members/page": "/library/members", "/page": "/", "/library/page": "/library", "/library/reservations/page": "/library/reservations", "/financials/payments/page": "/financials/payments", "/hostel/rooms/page": "/hostel/rooms", "/dashboard/academic/curriculum/page": "/dashboard/academic/curriculum", "/dashboard/academic/grades/page": "/dashboard/academic/grades", "/dashboard/academic/attendance/page": "/dashboard/academic/attendance", "/dashboard/academic/progress/page": "/dashboard/academic/progress", "/dashboard/academic/schedule/page": "/dashboard/academic/schedule", "/dashboard/academic/assessments/page": "/dashboard/academic/assessments", "/dashboard/admissions/page": "/dashboard/admissions", "/dashboard/academic/page": "/dashboard/academic", "/dashboard/admissions/enrollment/page": "/dashboard/admissions/enrollment", "/dashboard/alumni/jobs/page": "/dashboard/alumni/jobs", "/dashboard/admissions/decisions/page": "/dashboard/admissions/decisions", "/dashboard/admissions/interviews/page": "/dashboard/admissions/interviews", "/dashboard/alumni/events/page": "/dashboard/alumni/events", "/dashboard/alumni/directory/page": "/dashboard/alumni/directory", "/dashboard/admissions/classes/page": "/dashboard/admissions/classes", "/dashboard/admissions/reports/page": "/dashboard/admissions/reports", "/dashboard/financials/billing/page": "/dashboard/financials/billing", "/dashboard/alumni/page": "/dashboard/alumni", "/dashboard/financials/fee-structure/page": "/dashboard/financials/fee-structure", "/dashboard/financials/page": "/dashboard/financials", "/dashboard/financials/financial-aid/page": "/dashboard/financials/financial-aid", "/dashboard/hostel/facilities/page": "/dashboard/hostel/facilities", "/dashboard/financials/history/page": "/dashboard/financials/history", "/dashboard/hostel/billing/page": "/dashboard/hostel/billing", "/dashboard/financials/payments/page": "/dashboard/financials/payments", "/dashboard/hostel/maintenance/page": "/dashboard/hostel/maintenance", "/dashboard/hostel/page": "/dashboard/hostel", "/dashboard/hostel/rooms/page": "/dashboard/hostel/rooms", "/dashboard/admissions/applications/page": "/dashboard/admissions/applications", "/dashboard/financials/reports/page": "/dashboard/financials/reports", "/dashboard/hostel/visitors/page": "/dashboard/hostel/visitors", "/dashboard/hostel/residents/page": "/dashboard/hostel/residents", "/dashboard/library/catalog/page": "/dashboard/library/catalog", "/dashboard/library/digital/page": "/dashboard/library/digital", "/dashboard/library/inventory/page": "/dashboard/library/inventory", "/dashboard/page": "/dashboard", "/dashboard/library/page": "/dashboard/library", "/dashboard/teachers/schedules/page": "/dashboard/teachers/schedules", "/dashboard/teachers/evaluations/page": "/dashboard/teachers/evaluations", "/dashboard/transport/page": "/dashboard/transport", "/dashboard/teachers/profiles/page": "/dashboard/teachers/profiles", "/dashboard/teachers/training/page": "/dashboard/teachers/training", "/dashboard/teachers/page": "/dashboard/teachers", "/dashboard/transport/students/page": "/dashboard/transport/students", "/dashboard/transport/routes/page": "/dashboard/transport/routes", "/dashboard/library/members/page": "/dashboard/library/members", "/dashboard/transport/safety/page": "/dashboard/transport/safety", "/dashboard/transport/drivers/page": "/dashboard/transport/drivers", "/dashboard/transport/vehicles/page": "/dashboard/transport/vehicles"}