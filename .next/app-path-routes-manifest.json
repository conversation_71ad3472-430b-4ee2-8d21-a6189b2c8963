{"/_not-found": "/_not-found", "/academic/assessments/page": "/academic/assessments", "/academic/curriculum/page": "/academic/curriculum", "/academic/grades/page": "/academic/grades", "/academic/attendance/page": "/academic/attendance", "/academic/page": "/academic", "/academic/schedule/page": "/academic/schedule", "/academic/progress/page": "/academic/progress", "/admissions/applications/new/page": "/admissions/applications/new", "/admissions/applications/page": "/admissions/applications", "/admissions/classes/page": "/admissions/classes", "/admissions/enrollment/page": "/admissions/enrollment", "/admissions/decisions/page": "/admissions/decisions", "/admissions/interviews/page": "/admissions/interviews", "/admissions/page": "/admissions", "/admissions/reports/page": "/admissions/reports", "/alumni/achievements/page": "/alumni/achievements", "/alumni/directory/page": "/alumni/directory", "/alumni/donations/page": "/alumni/donations", "/alumni/events/page": "/alumni/events", "/alumni/jobs/page": "/alumni/jobs", "/alumni/networking/page": "/alumni/networking", "/alumni/page": "/alumni", "/auth/callback/route": "/auth/callback", "/auth/login/page": "/auth/login", "/financials/billing/page": "/financials/billing", "/financials/fee-structure/page": "/financials/fee-structure", "/financials/financial-aid/page": "/financials/financial-aid", "/financials/history/page": "/financials/history", "/financials/reports/page": "/financials/reports", "/hostel/facilities/page": "/hostel/facilities", "/hostel/maintenance/page": "/hostel/maintenance", "/hostel/page": "/hostel", "/hostel/rooms/page": "/hostel/rooms", "/hostel/visitors/page": "/hostel/visitors", "/hostel/residents/page": "/hostel/residents", "/library/catalog/page": "/library/catalog", "/library/circulation/page": "/library/circulation", "/library/inventory/page": "/library/inventory", "/library/digital/page": "/library/digital", "/library/members/page": "/library/members", "/library/page": "/library", "/library/reservations/page": "/library/reservations", "/page": "/", "/api/health/route": "/api/health", "/financials/page": "/financials", "/financials/payments/page": "/financials/payments", "/dashboard/academic/assessments/page": "/dashboard/academic/assessments", "/dashboard/academic/page": "/dashboard/academic", "/dashboard/academic/grades/page": "/dashboard/academic/grades", "/dashboard/academic/schedule/page": "/dashboard/academic/schedule", "/dashboard/academic/progress/page": "/dashboard/academic/progress", "/dashboard/academic/curriculum/page": "/dashboard/academic/curriculum", "/dashboard/academic/attendance/page": "/dashboard/academic/attendance", "/dashboard/admissions/applications/page": "/dashboard/admissions/applications", "/dashboard/admissions/interviews/page": "/dashboard/admissions/interviews", "/dashboard/admissions/page": "/dashboard/admissions", "/dashboard/admissions/decisions/page": "/dashboard/admissions/decisions", "/dashboard/admissions/reports/page": "/dashboard/admissions/reports", "/dashboard/alumni/jobs/page": "/dashboard/alumni/jobs", "/dashboard/alumni/directory/page": "/dashboard/alumni/directory", "/dashboard/alumni/events/page": "/dashboard/alumni/events", "/dashboard/alumni/page": "/dashboard/alumni", "/dashboard/financials/billing/page": "/dashboard/financials/billing", "/dashboard/financials/financial-aid/page": "/dashboard/financials/financial-aid", "/dashboard/financials/fee-structure/page": "/dashboard/financials/fee-structure", "/dashboard/financials/page": "/dashboard/financials", "/dashboard/financials/history/page": "/dashboard/financials/history", "/dashboard/financials/reports/page": "/dashboard/financials/reports", "/dashboard/hostel/billing/page": "/dashboard/hostel/billing", "/dashboard/hostel/facilities/page": "/dashboard/hostel/facilities", "/dashboard/hostel/maintenance/page": "/dashboard/hostel/maintenance", "/dashboard/hostel/page": "/dashboard/hostel", "/dashboard/hostel/residents/page": "/dashboard/hostel/residents", "/dashboard/financials/payments/page": "/dashboard/financials/payments", "/dashboard/library/catalog/page": "/dashboard/library/catalog", "/dashboard/library/inventory/page": "/dashboard/library/inventory", "/dashboard/library/digital/page": "/dashboard/library/digital", "/dashboard/library/members/page": "/dashboard/library/members", "/dashboard/page": "/dashboard", "/dashboard/teachers/evaluations/page": "/dashboard/teachers/evaluations", "/dashboard/hostel/rooms/page": "/dashboard/hostel/rooms", "/dashboard/teachers/page": "/dashboard/teachers", "/dashboard/admissions/enrollment/page": "/dashboard/admissions/enrollment", "/dashboard/teachers/profiles/page": "/dashboard/teachers/profiles", "/dashboard/transport/drivers/page": "/dashboard/transport/drivers", "/dashboard/transport/page": "/dashboard/transport", "/dashboard/transport/safety/page": "/dashboard/transport/safety", "/dashboard/transport/vehicles/page": "/dashboard/transport/vehicles", "/dashboard/transport/students/page": "/dashboard/transport/students", "/dashboard/teachers/training/page": "/dashboard/teachers/training", "/dashboard/admissions/classes/page": "/dashboard/admissions/classes", "/dashboard/teachers/schedules/page": "/dashboard/teachers/schedules", "/dashboard/library/page": "/dashboard/library", "/dashboard/hostel/visitors/page": "/dashboard/hostel/visitors", "/dashboard/transport/routes/page": "/dashboard/transport/routes"}