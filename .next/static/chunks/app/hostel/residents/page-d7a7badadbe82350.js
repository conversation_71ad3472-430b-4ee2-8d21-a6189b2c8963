(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7961],{7136:function(e,n,o){Promise.resolve().then(o.bind(o,9315))},9315:function(e,n,o){"use strict";o.r(n),o.d(n,{default:function(){return s}});var t=o(7437),i=o(2952);function s(){return(0,t.jsx)(i.s,{tenantId:"demo-tenant-uuid-1234567890",onViewResident:e=>{console.log("View resident:",e)},onEditResident:e=>{console.log("Edit resident:",e)},onCheckOut:e=>{console.log("Check out resident:",e),confirm("Are you sure you want to check out this resident?")&&alert("Resident ".concat(e," checked out successfully"))},onAddDisciplinaryRecord:e=>{console.log("Add disciplinary record for resident:",e)}})}}},function(e){e.O(0,[7895,2952,2971,4938,1744],function(){return e(e.s=7136)}),_N_E=e.O()}]);