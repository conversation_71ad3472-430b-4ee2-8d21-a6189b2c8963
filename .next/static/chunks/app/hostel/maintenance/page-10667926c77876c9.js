(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[57],{7291:function(e,n,t){Promise.resolve().then(t.bind(t,9431))},9431:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return s}});var a=t(7437),o=t(1939);function s(){return(0,a.jsx)(o.z,{tenantId:"demo-tenant-uuid-1234567890",onCreateRequest:()=>{console.log("Create new maintenance request")},onAssignRequest:(e,n)=>{console.log("Assign maintenance request:",{requestId:e,staffId:n}),alert("Maintenance request ".concat(e," assigned to staff ").concat(n))},onUpdateStatus:(e,n)=>{console.log("Update maintenance request status:",{requestId:e,status:n}),alert("Maintenance request ".concat(e," status updated to ").concat(n))},onAddFeedback:(e,n,t)=>{console.log("Add feedback for maintenance request:",{requestId:e,rating:n,comment:t}),alert("Feedback added for maintenance request ".concat(e))}})}}},function(e){e.O(0,[7895,1939,2971,4938,1744],function(){return e(e.s=7291)}),_N_E=e.O()}]);