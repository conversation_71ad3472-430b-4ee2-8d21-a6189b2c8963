(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8786],{9871:function(n,o,e){Promise.resolve().then(e.bind(e,6013))},6013:function(n,o,e){"use strict";e.r(o),e.d(o,{default:function(){return c}});var t=e(7437),i=e(6273);function c(){return(0,t.jsx)(i.Z,{tenantId:"demo-tenant-uuid-1234567890",onCheckInVisitor:()=>{console.log("Check in new visitor")},onCheckOutVisitor:n=>{console.log("Check out visitor:",n),alert("Visitor ".concat(n," checked out successfully"))},onDenyEntry:(n,o)=>{console.log("Deny visitor entry:",{visitorId:n,reason:o}),alert("Visitor entry denied: ".concat(o))},onUpdatePolicy:n=>{console.log("Update visitor policy:",n)}})}}},function(n){n.O(0,[7895,6273,2971,4938,1744],function(){return n(n.s=9871)}),_N_E=n.O()}]);