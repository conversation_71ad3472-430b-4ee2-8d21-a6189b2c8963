(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9144],{2028:function(o,e,t){Promise.resolve().then(t.bind(t,6653))},6653:function(o,e,t){"use strict";t.r(e),t.d(e,{default:function(){return l}});var n=t(7437),c=t(8487);function l(){return(0,n.jsx)(c.n,{tenantId:"demo-tenant-uuid-1234567890",onAllocateRoom:(o,e)=>{console.log("Allocate room:",{requestId:o,roomId:e}),alert("Room allocated successfully for request ".concat(o))},onDeallocateRoom:(o,e)=>{console.log("Deallocate room:",{roomId:o,studentId:e}),confirm("Are you sure you want to deallocate this room?")&&alert("Room ".concat(o," deallocated for student ").concat(e))},onApproveRequest:o=>{console.log("Approve allocation request:",o),alert("Allocation request ".concat(o," approved"))},onRejectRequest:(o,e)=>{console.log("Reject allocation request:",{requestId:o,reason:e}),alert("Allocation request ".concat(o," rejected: ").concat(e))}})}}},function(o){o.O(0,[7895,8487,2971,4938,1744],function(){return o(o.s=2028)}),_N_E=o.O()}]);