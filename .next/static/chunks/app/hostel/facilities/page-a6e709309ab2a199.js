(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3997],{1811:function(n,e,o){Promise.resolve().then(o.bind(o,1984))},1984:function(n,e,o){"use strict";o.r(e),o.d(e,{default:function(){return i}});var c=o(7437),t=o(663);function i(){return(0,c.jsx)(t.N,{tenantId:"demo-tenant-uuid-1234567890",onBookFacility:n=>{console.log("Book facility:",n)},onCancelBooking:n=>{console.log("Cancel facility booking:",n),confirm("Are you sure you want to cancel this booking?")&&alert("Booking ".concat(n," cancelled successfully"))},onUpdateFacility:n=>{console.log("Update facility:",n)},onScheduleMaintenance:n=>{console.log("Schedule facility maintenance:",n),alert("Maintenance scheduled for facility ".concat(n))}})}}},function(n){n.O(0,[7895,663,2971,4938,1744],function(){return n(n.s=1811)}),_N_E=n.O()}]);