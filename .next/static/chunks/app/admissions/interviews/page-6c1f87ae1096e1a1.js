(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4555],{4455:function(e,t,s){Promise.resolve().then(s.bind(s,7804))},7804:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return i}});var r=s(7437),a=s(2265),n=s(9197);function l(e){let{tenantId:t,onScheduleInterview:s,onViewFeedback:l}=e,[i,d]=(0,a.useState)("upcoming"),[c,o]=(0,a.useState)(!1),[m,x]=(0,a.useState)(null),u=[{id:"1",applicationId:"APP-2024-001",studentName:"<PERSON>",applicationNumber:"APP-2024-001",scheduledDate:"2024-01-25",scheduledTime:"10:00",duration:30,interviewType:"in_person",interviewer:"Dr. <PERSON>",location:"Room 101, Admin Building",status:"scheduled",notes:"Initial interview for Grade 9 admission"},{id:"2",applicationId:"APP-2024-002",studentName:"<PERSON>",applicationNumber:"APP-2024-002",scheduledDate:"2024-01-25",scheduledTime:"11:00",duration:30,interviewType:"virtual",interviewer:"Prof. John Davis",location:"Zoom Meeting",status:"scheduled",notes:"Arts stream interview"},{id:"3",applicationId:"APP-2024-003",studentName:"Michael Brown",applicationNumber:"APP-2024-003",scheduledDate:"2024-01-20",scheduledTime:"14:00",duration:45,interviewType:"in_person",interviewer:"Dr. Emily Davis",location:"Room 205, Academic Block",status:"completed",notes:"Commerce stream interview",feedback:{rating:4,comments:"Excellent communication skills and strong academic background. Shows great potential.",recommendation:"accept"}}],h=[{id:"APP-2024-004",studentName:"Emily Davis",grade:"Grade 9",program:"Science Stream"},{id:"APP-2024-005",studentName:"David Wilson",grade:"Grade 10",program:"Arts Stream"},{id:"APP-2024-006",studentName:"Lisa Garcia",grade:"Grade 11",program:"Commerce Stream"}],p=e=>{switch(e){case"scheduled":return"bg-blue-100 text-blue-800";case"completed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";case"rescheduled":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},b=e=>{switch(e){case"in_person":return"\uD83C\uDFE2";case"virtual":return"\uD83D\uDCBB";case"phone":return"\uD83D\uDCDE";default:return"\uD83D\uDCCB"}},j=u.filter(e=>"scheduled"===e.status),f=u.filter(e=>"completed"===e.status);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Interview Management"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Schedule and manage admission interviews"})]}),(0,r.jsx)("button",{onClick:()=>o(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Schedule Interview"})]}),(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,r.jsxs)("button",{onClick:()=>d("upcoming"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("upcoming"===i?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83D\uDCC5 Upcoming (",j.length,")"]}),(0,r.jsxs)("button",{onClick:()=>d("completed"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("completed"===i?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["✅ Completed (",f.length,")"]}),(0,r.jsxs)("button",{onClick:()=>d("pending"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("pending"===i?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["⏳ Pending (",h.length,")"]})]})}),(0,r.jsxs)("div",{children:["upcoming"===i&&(0,r.jsx)("div",{className:"space-y-4",children:j.map(e=>(0,r.jsx)(n.Zb,{children:(0,r.jsxs)(n.aY,{className:"pt-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"text-2xl",children:b(e.interviewType)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-lg",children:e.studentName}),(0,r.jsx)("p",{className:"text-gray-600",children:e.applicationNumber}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.notes})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-lg font-semibold",children:e.scheduledDate}),(0,r.jsxs)("div",{className:"text-gray-600",children:[e.scheduledTime," (",e.duration," min)"]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.interviewer}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.location})]})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(p(e.status)),children:e.status.toUpperCase()}),(0,r.jsxs)("div",{className:"space-x-2",children:[(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"Reschedule"}),(0,r.jsx)("button",{className:"text-green-600 hover:text-green-800 text-sm font-medium",children:"Mark Complete"}),(0,r.jsx)("button",{className:"text-red-600 hover:text-red-800 text-sm font-medium",children:"Cancel"})]})]})]})},e.id))}),"completed"===i&&(0,r.jsx)("div",{className:"space-y-4",children:f.map(e=>(0,r.jsx)(n.Zb,{children:(0,r.jsxs)(n.aY,{className:"pt-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"text-2xl",children:b(e.interviewType)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-lg",children:e.studentName}),(0,r.jsx)("p",{className:"text-gray-600",children:e.applicationNumber}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Interviewed by ",e.interviewer]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-lg font-semibold",children:e.scheduledDate}),(0,r.jsx)("div",{className:"text-gray-600",children:e.scheduledTime}),e.feedback&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{className:"text-sm",children:"Rating:"}),(0,r.jsx)("div",{className:"flex",children:[1,2,3,4,5].map(t=>(0,r.jsx)("span",{className:"text-sm ".concat(t<=e.feedback.rating?"text-yellow-400":"text-gray-300"),children:"⭐"},t))})]}),(0,r.jsxs)("div",{className:"text-sm font-medium ".concat("accept"===e.feedback.recommendation?"text-green-600":"reject"===e.feedback.recommendation?"text-red-600":"text-yellow-600"),children:["Recommendation: ",e.feedback.recommendation.toUpperCase()]})]})]})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(p(e.status)),children:e.status.toUpperCase()}),(0,r.jsxs)("div",{className:"space-x-2",children:[(0,r.jsx)("button",{onClick:()=>null==l?void 0:l(e.id),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Feedback"}),(0,r.jsx)("button",{className:"text-gray-600 hover:text-gray-800 text-sm font-medium",children:"Download Report"})]})]})]})},e.id))}),"pending"===i&&(0,r.jsx)("div",{className:"space-y-4",children:h.map(e=>(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-lg",children:e.studentName}),(0,r.jsx)("p",{className:"text-gray-600",children:e.id}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[e.grade," - ",e.program]})]}),(0,r.jsx)("button",{onClick:()=>{x(e.id),o(!0)},className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Schedule Interview"})]})})},e.id))})]}),c&&(0,r.jsx)(()=>(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Schedule Interview"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Application"}),(0,r.jsx)("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md",children:h.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.studentName," - ",e.id]},e.id))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date"}),(0,r.jsx)("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Time"}),(0,r.jsx)("input",{type:"time",className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Interview Type"}),(0,r.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md",children:[(0,r.jsx)("option",{value:"in_person",children:"In Person"}),(0,r.jsx)("option",{value:"virtual",children:"Virtual"}),(0,r.jsx)("option",{value:"phone",children:"Phone"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Interviewer"}),(0,r.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md",children:[(0,r.jsx)("option",{value:"dr_wilson",children:"Dr. Sarah Wilson"}),(0,r.jsx)("option",{value:"prof_davis",children:"Prof. John Davis"}),(0,r.jsx)("option",{value:"dr_emily",children:"Dr. Emily Davis"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Location/Link"}),(0,r.jsx)("input",{type:"text",placeholder:"Room number or meeting link",className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:()=>o(!1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{onClick:()=>{o(!1)},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Schedule Interview"})]})]})}),{})]})}function i(){return(0,r.jsx)(l,{tenantId:"demo-tenant-uuid-1234567890",onScheduleInterview:e=>{console.log("Schedule interview for:",e)},onViewFeedback:e=>{console.log("View feedback for:",e)}})}},9197:function(e,t,s){"use strict";s.d(t,{Zb:function(){return d},aY:function(){return x},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var r=s(7437),a=s(2265),n=s(7042),l=s(4769);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,l.m6)((0,n.W)(t))}let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});d.displayName="Card";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:i("flex flex-col space-y-1.5 p-6",s),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:i("text-2xl font-semibold leading-none tracking-tight",s),...a})});o.displayName="CardTitle";let m=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:i("text-sm text-muted-foreground",s),...a})});m.displayName="CardDescription";let x=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:i("p-6 pt-0",s),...a})});x.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:i("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=4455)}),_N_E=e.O()}]);