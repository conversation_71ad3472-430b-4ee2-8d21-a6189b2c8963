(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7441],{235:function(e,t,s){Promise.resolve().then(s.bind(s,9018))},9018:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return c}});var a=s(7437),l=s(2265),r=s(9197);function i(e){let{tenantId:t,onViewApplication:s,onNewApplication:i}=e,[n,d]=(0,l.useState)(""),[c,o]=(0,l.useState)("all"),[m,u]=(0,l.useState)("all"),[x,p]=(0,l.useState)("submittedDate"),[h,j]=(0,l.useState)("desc"),g=[{id:"1",applicationNumber:"APP-2024-001",studentName:"<PERSON>",email:"<EMAIL>",phone:"******-0123",appliedGrade:"Grade 9",appliedProgram:"Science Stream",status:"pending",submittedDate:"2024-01-15",lastUpdated:"2024-01-15",priority:"high",documents:{total:5,submitted:3}},{id:"2",applicationNumber:"APP-2024-002",studentName:"Sarah Johnson",email:"<EMAIL>",phone:"******-0124",appliedGrade:"Grade 10",appliedProgram:"Arts Stream",status:"under_review",submittedDate:"2024-01-14",lastUpdated:"2024-01-16",priority:"medium",documents:{total:5,submitted:5}},{id:"3",applicationNumber:"APP-2024-003",studentName:"Michael Brown",email:"<EMAIL>",phone:"******-0125",appliedGrade:"Grade 11",appliedProgram:"Commerce Stream",status:"interview_scheduled",submittedDate:"2024-01-13",lastUpdated:"2024-01-17",priority:"high",documents:{total:5,submitted:5}},{id:"4",applicationNumber:"APP-2024-004",studentName:"Emily Davis",email:"<EMAIL>",phone:"******-0126",appliedGrade:"Grade 9",appliedProgram:"Science Stream",status:"accepted",submittedDate:"2024-01-12",lastUpdated:"2024-01-18",priority:"medium",documents:{total:5,submitted:5}},{id:"5",applicationNumber:"APP-2024-005",studentName:"David Wilson",email:"<EMAIL>",phone:"******-0127",appliedGrade:"Grade 10",appliedProgram:"Science Stream",status:"rejected",submittedDate:"2024-01-11",lastUpdated:"2024-01-19",priority:"low",documents:{total:5,submitted:4}}],b=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"under_review":return"bg-blue-100 text-blue-800";case"interview_scheduled":return"bg-purple-100 text-purple-800";case"accepted":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"waitlisted":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},f=e=>{switch(e){case"high":return"text-red-600";case"medium":return"text-yellow-600";case"low":return"text-green-600";default:return"text-gray-600"}},v=g.filter(e=>{let t=e.studentName.toLowerCase().includes(n.toLowerCase())||e.applicationNumber.toLowerCase().includes(n.toLowerCase())||e.email.toLowerCase().includes(n.toLowerCase()),s="all"===c||e.status===c,a="all"===m||e.appliedGrade===m;return t&&s&&a}),N=[...v].sort((e,t)=>{let s=e[x],a=t[x];return("string"==typeof s&&"string"==typeof a&&(s=s.toLowerCase(),a=a.toLowerCase()),"asc"===h)?s<a?-1:s>a?1:0:s>a?-1:s<a?1:0});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Applications"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage student admission applications"})]}),(0,a.jsx)("button",{onClick:i,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:"+ New Application"})]}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by name, email, or application number...",value:n,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsxs)("select",{value:c,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Statuses"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"under_review",children:"Under Review"}),(0,a.jsx)("option",{value:"interview_scheduled",children:"Interview Scheduled"}),(0,a.jsx)("option",{value:"accepted",children:"Accepted"}),(0,a.jsx)("option",{value:"rejected",children:"Rejected"}),(0,a.jsx)("option",{value:"waitlisted",children:"Waitlisted"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Grade"}),(0,a.jsxs)("select",{value:m,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Grades"}),(0,a.jsx)("option",{value:"Grade 9",children:"Grade 9"}),(0,a.jsx)("option",{value:"Grade 10",children:"Grade 10"}),(0,a.jsx)("option",{value:"Grade 11",children:"Grade 11"}),(0,a.jsx)("option",{value:"Grade 12",children:"Grade 12"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sort By"}),(0,a.jsxs)("select",{value:"".concat(x,"-").concat(h),onChange:e=>{let[t,s]=e.target.value.split("-");p(t),j(s)},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"submittedDate-desc",children:"Newest First"}),(0,a.jsx)("option",{value:"submittedDate-asc",children:"Oldest First"}),(0,a.jsx)("option",{value:"studentName-asc",children:"Name A-Z"}),(0,a.jsx)("option",{value:"studentName-desc",children:"Name Z-A"}),(0,a.jsx)("option",{value:"status-asc",children:"Status"})]})]})]})})}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsxs)(r.ll,{children:["Applications (",N.length,")"]}),(0,a.jsxs)(r.SZ,{children:[v.length," of ",g.length," applications shown"]})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full table-auto",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Application"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Student"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Grade/Program"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Documents"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Priority"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Submitted"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:N.map(e=>(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.applicationNumber}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.studentName}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.phone})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.appliedGrade}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.appliedProgram})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(b(e.status)),children:e.status.replace("_"," ").toUpperCase()})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("span",{className:"font-medium ".concat(e.documents.submitted===e.documents.total?"text-green-600":"text-orange-600"),children:[e.documents.submitted,"/",e.documents.total]}),(0,a.jsx)("div",{className:"text-gray-500",children:"documents"})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"text-sm font-medium ".concat(f(e.priority)),children:e.priority.toUpperCase()})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.submittedDate})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>null==s?void 0:s(e.id),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View"}),(0,a.jsx)("button",{className:"text-gray-600 hover:text-gray-800 text-sm font-medium",children:"Edit"})]})})]},e.id))})]})})})]})]})}function n(e){let{applicationId:t,tenantId:s,onBack:i,onStatusUpdate:n}=e,[d,c]=(0,l.useState)("overview"),[o,m]=(0,l.useState)(""),[u,x]=(0,l.useState)(""),p={applicationNumber:"APP-2024-001",status:"under_review",student:{firstName:"John",lastName:"Smith",dateOfBirth:"2008-05-15",gender:"male",nationality:"American",religion:"Christian",bloodGroup:"A+",photo:"/placeholder-avatar.jpg"},contact:{email:"<EMAIL>",phone:"******-0123",address:"123 Main Street, Apt 4B",city:"New York",state:"NY",zipCode:"10001",country:"USA"},parents:{father:{name:"Robert Smith",occupation:"Engineer",phone:"******-0124",email:"<EMAIL>"},mother:{name:"Mary Smith",occupation:"Teacher",phone:"******-0125",email:"<EMAIL>"}},academic:{previousSchool:"Lincoln Middle School",previousGrade:"Grade 8",previousPercentage:"92%",appliedGrade:"Grade 9",appliedProgram:"Science Stream",preferredStartDate:"2024-09-01"},documents:[{name:"Birth Certificate",status:"submitted",uploadDate:"2024-01-15"},{name:"Previous Transcripts",status:"submitted",uploadDate:"2024-01-15"},{name:"Medical Records",status:"submitted",uploadDate:"2024-01-15"},{name:"Photos",status:"pending",uploadDate:null},{name:"Identity Proof",status:"pending",uploadDate:null}]},h=()=>(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsx)(r.ll,{children:"Student Information"})}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC64"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold",children:[p.student.firstName," ",p.student.lastName]}),(0,a.jsx)("p",{className:"text-gray-600",children:p.contact.email}),(0,a.jsx)("p",{className:"text-gray-600",children:p.contact.phone})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Date of Birth:"}),(0,a.jsx)("p",{children:p.student.dateOfBirth})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Gender:"}),(0,a.jsx)("p",{className:"capitalize",children:p.student.gender})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Nationality:"}),(0,a.jsx)("p",{children:p.student.nationality})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Blood Group:"}),(0,a.jsx)("p",{children:p.student.bloodGroup})]})]})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsx)(r.ll,{children:"Application Details"})}),(0,a.jsx)(r.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Application Number:"}),(0,a.jsx)("p",{children:p.applicationNumber})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Applied Grade:"}),(0,a.jsx)("p",{children:p.academic.appliedGrade})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Program:"}),(0,a.jsx)("p",{children:p.academic.appliedProgram})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Preferred Start:"}),(0,a.jsx)("p",{children:p.academic.preferredStartDate})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Previous School:"}),(0,a.jsx)("p",{children:p.academic.previousSchool})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Previous Grade:"}),(0,a.jsxs)("p",{children:[p.academic.previousGrade," (",p.academic.previousPercentage,")"]})]})]})})]}),(0,a.jsxs)(r.Zb,{className:"lg:col-span-2",children:[(0,a.jsx)(r.Ol,{children:(0,a.jsx)(r.ll,{children:"Documents Status"})}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:p.documents.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.uploadDate?"Uploaded: ".concat(e.uploadDate):"Not uploaded"})]}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("submitted"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:e.status})]},t))})})]})]}),j=()=>(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsx)(r.ll,{children:"Personal Details"})}),(0,a.jsx)(r.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"font-medium",children:"First Name"}),(0,a.jsx)("p",{children:p.student.firstName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"font-medium",children:"Last Name"}),(0,a.jsx)("p",{children:p.student.lastName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"font-medium",children:"Date of Birth"}),(0,a.jsx)("p",{children:p.student.dateOfBirth})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"font-medium",children:"Gender"}),(0,a.jsx)("p",{className:"capitalize",children:p.student.gender})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"font-medium",children:"Nationality"}),(0,a.jsx)("p",{children:p.student.nationality})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"font-medium",children:"Religion"}),(0,a.jsx)("p",{children:p.student.religion})]})]})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsx)(r.ll,{children:"Contact Information"})}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"font-medium",children:"Email"}),(0,a.jsx)("p",{children:p.contact.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"font-medium",children:"Phone"}),(0,a.jsx)("p",{children:p.contact.phone})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"font-medium",children:"Address"}),(0,a.jsx)("p",{children:p.contact.address}),(0,a.jsxs)("p",{children:[p.contact.city,", ",p.contact.state," ",p.contact.zipCode]}),(0,a.jsx)("p",{children:p.contact.country})]})]})]}),(0,a.jsxs)(r.Zb,{className:"lg:col-span-2",children:[(0,a.jsx)(r.Ol,{children:(0,a.jsx)(r.ll,{children:"Parent/Guardian Information"})}),(0,a.jsx)(r.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Father's Information"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Name"}),(0,a.jsx)("p",{children:p.parents.father.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Occupation"}),(0,a.jsx)("p",{children:p.parents.father.occupation})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Phone"}),(0,a.jsx)("p",{children:p.parents.father.phone})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Email"}),(0,a.jsx)("p",{children:p.parents.father.email})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Mother's Information"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Name"}),(0,a.jsx)("p",{children:p.parents.mother.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Occupation"}),(0,a.jsx)("p",{children:p.parents.mother.occupation})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Phone"}),(0,a.jsx)("p",{children:p.parents.mother.phone})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Email"}),(0,a.jsx)("p",{children:p.parents.mother.email})]})]})]})]})})]})]});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:i,className:"text-gray-600 hover:text-gray-800",children:"← Back"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Application Review"}),(0,a.jsxs)("p",{className:"text-gray-600",children:[p.applicationNumber," - ",p.student.firstName," ",p.student.lastName]})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)("span",{className:"px-3 py-1 text-sm rounded-full bg-blue-100 text-blue-800",children:p.status.replace("_"," ").toUpperCase()})})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",label:"Overview",icon:"\uD83D\uDCCB"},{id:"personal",label:"Personal Info",icon:"\uD83D\uDC64"},{id:"academic",label:"Academic Info",icon:"\uD83D\uDCDA"},{id:"documents",label:"Documents",icon:"\uD83D\uDCC4"},{id:"history",label:"Review History",icon:"\uD83D\uDCDD"}].map(e=>(0,a.jsxs)("button",{onClick:()=>c(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(d===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,a.jsx)("span",{className:"mr-2",children:e.icon}),e.label]},e.id))})}),(0,a.jsx)("div",{className:"min-h-96",children:(()=>{switch(d){case"overview":return h();case"personal":return j();case"academic":return(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Academic Information Details"});case"documents":return(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Document Management Interface"});case"history":return(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Review History Timeline"});default:return null}})()}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"Review Decision"}),(0,a.jsx)(r.SZ,{children:"Add your review notes and update the application status"})]}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Review Notes"}),(0,a.jsx)("textarea",{value:o,onChange:e=>m(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Add your review comments here..."})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Decision"}),(0,a.jsxs)("select",{value:u,onChange:e=>x(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select Decision"}),(0,a.jsx)("option",{value:"approved_for_interview",children:"Approve for Interview"}),(0,a.jsx)("option",{value:"accepted",children:"Accept Application"}),(0,a.jsx)("option",{value:"rejected",children:"Reject Application"}),(0,a.jsx)("option",{value:"waitlisted",children:"Add to Waitlist"}),(0,a.jsx)("option",{value:"request_documents",children:"Request Additional Documents"})]})]}),(0,a.jsx)("button",{onClick:()=>{u&&n&&n(u,o)},disabled:!u||!o,className:"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Update Status"})]})]})]})]})}function d(e){var t,s;let{tenantId:i,onSubmit:n,onCancel:d}=e,[c,o]=(0,l.useState)(1),[m,u]=(0,l.useState)({firstName:"",lastName:"",dateOfBirth:"",gender:"",nationality:"",religion:"",bloodGroup:"",email:"",phone:"",address:"",city:"",state:"",zipCode:"",country:"",fatherName:"",fatherOccupation:"",fatherPhone:"",fatherEmail:"",motherName:"",motherOccupation:"",motherPhone:"",motherEmail:"",guardianName:"",guardianRelation:"",guardianPhone:"",previousSchool:"",previousGrade:"",previousPercentage:"",appliedGrade:"",appliedProgram:"",preferredStartDate:"",medicalConditions:"",specialNeeds:"",extracurriculars:"",achievements:"",documents:{birthCertificate:null,previousTranscripts:null,medicalRecords:null,photos:null,identityProof:null}}),x=[{id:1,title:"Personal Info",description:"Basic personal details"},{id:2,title:"Contact Info",description:"Address and contact details"},{id:3,title:"Parent/Guardian",description:"Family information"},{id:4,title:"Academic Info",description:"Educational background"},{id:5,title:"Additional Info",description:"Medical and other details"},{id:6,title:"Documents",description:"Upload required documents"},{id:7,title:"Review",description:"Review and submit"}],p=(e,t)=>{u(s=>({...s,[e]:t}))},h=()=>(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name *"}),(0,a.jsx)("input",{type:"text",value:m.firstName,onChange:e=>p("firstName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name *"}),(0,a.jsx)("input",{type:"text",value:m.lastName,onChange:e=>p("lastName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth *"}),(0,a.jsx)("input",{type:"date",value:m.dateOfBirth,onChange:e=>p("dateOfBirth",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gender *"}),(0,a.jsxs)("select",{value:m.gender,onChange:e=>p("gender",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select Gender"}),(0,a.jsx)("option",{value:"male",children:"Male"}),(0,a.jsx)("option",{value:"female",children:"Female"}),(0,a.jsx)("option",{value:"other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nationality"}),(0,a.jsx)("input",{type:"text",value:m.nationality,onChange:e=>p("nationality",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Blood Group"}),(0,a.jsxs)("select",{value:m.bloodGroup,onChange:e=>p("bloodGroup",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select Blood Group"}),(0,a.jsx)("option",{value:"A+",children:"A+"}),(0,a.jsx)("option",{value:"A-",children:"A-"}),(0,a.jsx)("option",{value:"B+",children:"B+"}),(0,a.jsx)("option",{value:"B-",children:"B-"}),(0,a.jsx)("option",{value:"AB+",children:"AB+"}),(0,a.jsx)("option",{value:"AB-",children:"AB-"}),(0,a.jsx)("option",{value:"O+",children:"O+"}),(0,a.jsx)("option",{value:"O-",children:"O-"})]})]})]}),j=()=>(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email *"}),(0,a.jsx)("input",{type:"email",value:m.email,onChange:e=>p("email",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone *"}),(0,a.jsx)("input",{type:"tel",value:m.phone,onChange:e=>p("phone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Address *"}),(0,a.jsx)("textarea",{value:m.address,onChange:e=>p("address",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"City *"}),(0,a.jsx)("input",{type:"text",value:m.city,onChange:e=>p("city",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"State/Province *"}),(0,a.jsx)("input",{type:"text",value:m.state,onChange:e=>p("state",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"ZIP/Postal Code"}),(0,a.jsx)("input",{type:"text",value:m.zipCode,onChange:e=>p("zipCode",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Country *"}),(0,a.jsx)("input",{type:"text",value:m.country,onChange:e=>p("country",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]})]});return(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"New Student Application"}),(0,a.jsx)(r.SZ,{children:"Complete all steps to submit your application"})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"flex items-center justify-between mb-8",children:x.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ".concat(c>=e.id?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"),children:e.id}),(0,a.jsxs)("div",{className:"ml-2 hidden sm:block",children:[(0,a.jsx)("div",{className:"text-sm font-medium ".concat(c>=e.id?"text-blue-600":"text-gray-500"),children:e.title}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:e.description})]}),t<x.length-1&&(0,a.jsx)("div",{className:"w-8 h-0.5 mx-4 ".concat(c>e.id?"bg-blue-600":"bg-gray-200")})]},e.id))})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:null===(t=x[c-1])||void 0===t?void 0:t.title}),(0,a.jsx)(r.SZ,{children:null===(s=x[c-1])||void 0===s?void 0:s.description})]}),(0,a.jsx)(r.aY,{children:(()=>{switch(c){case 1:return h();case 2:return j();case 3:return(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Parent/Guardian Information Form"});case 4:return(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Academic Information Form"});case 5:return(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Additional Information Form"});case 6:return(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Document Upload Form"});case 7:return(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Review and Submit"});default:return null}})()})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("button",{onClick:d,className:"px-6 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,a.jsxs)("div",{className:"space-x-4",children:[c>1&&(0,a.jsx)("button",{onClick:()=>{c>1&&o(c-1)},className:"px-6 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"Previous"}),c<x.length?(0,a.jsx)("button",{onClick:()=>{c<x.length&&o(c+1)},className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Next"}):(0,a.jsx)("button",{onClick:()=>{n&&n(m)},className:"px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:"Submit Application"})]})]})]})}function c(){let[e,t]=(0,l.useState)("list"),[s,r]=(0,l.useState)(null),c=()=>{t("list"),r(null)};return"review"===e&&s?(0,a.jsx)(n,{applicationId:s,tenantId:"demo-tenant-uuid-1234567890",onBack:c,onStatusUpdate:(e,s)=>{console.log("Status updated:",e,s),t("list")}}):"new"===e?(0,a.jsx)(d,{tenantId:"demo-tenant-uuid-1234567890",onSubmit:e=>{console.log("Application submitted:",e),t("list")},onCancel:c}):(0,a.jsx)(i,{tenantId:"demo-tenant-uuid-1234567890",onViewApplication:e=>{r(e),t("review")},onNewApplication:()=>{t("new")}})}},9197:function(e,t,s){"use strict";s.d(t,{Zb:function(){return d},aY:function(){return u},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var a=s(7437),l=s(2265),r=s(7042),i=s(4769);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,i.m6)((0,r.W)(t))}let d=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:n("rounded-lg border bg-card text-card-foreground shadow-sm",s),...l})});d.displayName="Card";let c=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:n("flex flex-col space-y-1.5 p-6",s),...l})});c.displayName="CardHeader";let o=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("h3",{ref:t,className:n("text-2xl font-semibold leading-none tracking-tight",s),...l})});o.displayName="CardTitle";let m=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("p",{ref:t,className:n("text-sm text-muted-foreground",s),...l})});m.displayName="CardDescription";let u=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:n("p-6 pt-0",s),...l})});u.displayName="CardContent",l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:n("flex items-center p-6 pt-0",s),...l})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=235)}),_N_E=e.O()}]);