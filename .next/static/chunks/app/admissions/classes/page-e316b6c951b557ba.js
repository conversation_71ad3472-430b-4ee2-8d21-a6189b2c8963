(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5512],{8327:function(n,t,e){Promise.resolve().then(e.bind(e,5667))},5667:function(n,t,e){"use strict";e.r(t),e.d(t,{default:function(){return u}});var o=e(7437),c=e(8438);function u(){return(0,o.jsx)(c.l,{tenantId:"demo-tenant-uuid-1234567890",onAllocateStudent:(n,t)=>{console.log("Student allocated to class:",{studentId:n,classId:t}),alert("Student ".concat(n," allocated to class ").concat(t))},onBack:()=>window.history.back()})}}},function(n){n.O(0,[7895,8438,2971,4938,1744],function(){return n(n.s=8327)}),_N_E=n.O()}]);