(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1486],{1760:function(e,r,t){Promise.resolve().then(t.bind(t,8077))},8077:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return o}});var n=t(7437),a=t(9353);function o(){return(0,n.jsx)(a.a,{tenantId:"demo-tenant-uuid-1234567890",onGenerateAdmissionLetter:e=>{console.log("Generate admission letter for:",e),alert("Generating admission letter for student ".concat(e))},onBack:()=>window.history.back()})}},9197:function(e,r,t){"use strict";t.d(r,{Zb:function(){return i},aY:function(){return c},SZ:function(){return u},Ol:function(){return l},ll:function(){return f}});var n=t(7437),a=t(2265),o=t(7042),s=t(4769);function d(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.m6)((0,o.W)(r))}let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:d("flex flex-col space-y-1.5 p-6",t),...a})});l.displayName="CardHeader";let f=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("h3",{ref:r,className:d("text-2xl font-semibold leading-none tracking-tight",t),...a})});f.displayName="CardTitle";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("p",{ref:r,className:d("text-sm text-muted-foreground",t),...a})});u.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:d("p-6 pt-0",t),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:d("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,9353,2971,4938,1744],function(){return e(e.s=1760)}),_N_E=e.O()}]);