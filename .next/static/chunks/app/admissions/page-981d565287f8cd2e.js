(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6450],{2377:function(e,s,t){Promise.resolve().then(t.bind(t,888))},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return c},aY:function(){return m},SZ:function(){return x},Ol:function(){return d},ll:function(){return o}});var r=t(7437),l=t(2265),n=t(7042),a=t(4769);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.m6)((0,n.W)(s))}let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...l})});c.displayName="Card";let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",t),...l})});d.displayName="CardHeader";let o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",t),...l})});o.displayName="CardTitle";let x=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",t),...l})});x.displayName="CardDescription";let m=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:s,className:i("p-6 pt-0",t),...l})});m.displayName="CardContent",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",t),...l})}).displayName="CardFooter"},888:function(e,s,t){"use strict";t.r(s),t.d(s,{AdmissionsDashboard:function(){return i}});var r=t(7437);t(2265);var l=t(1396),n=t.n(l),a=t(9197);function i(e){let{tenantId:s}=e,t={totalInquiries:245,totalApplications:189,pendingReviews:23,enrolledStudents:156,conversionRate:77.1};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Admissions Dashboard"}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("button",{className:"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90",children:"New Application"})})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-5",children:[(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Total Inquiries"}),(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",className:"h-4 w-4 text-muted-foreground",children:[(0,r.jsx)("path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}),(0,r.jsx)("circle",{cx:"9",cy:"7",r:"4"}),(0,r.jsx)("path",{d:"m22 21-3-3m0 0a5.5 5.5 0 1 0-7.78-7.78 5.5 5.5 0 0 0 7.78 7.78Z"})]})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.totalInquiries}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"+12% from last month"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Applications"}),(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",className:"h-4 w-4 text-muted-foreground",children:[(0,r.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),(0,r.jsx)("polyline",{points:"14,2 14,8 20,8"}),(0,r.jsx)("line",{x1:"16",y1:"13",x2:"8",y2:"13"}),(0,r.jsx)("line",{x1:"16",y1:"17",x2:"8",y2:"17"}),(0,r.jsx)("polyline",{points:"10,9 9,9 8,9"})]})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.totalApplications}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"+8% from last month"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Pending Reviews"}),(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",className:"h-4 w-4 text-muted-foreground",children:[(0,r.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,r.jsx)("polyline",{points:"12,6 12,12 16,14"})]})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.pendingReviews}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Requires attention"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Enrolled"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",className:"h-4 w-4 text-muted-foreground",children:(0,r.jsx)("path",{d:"M22 12h-4l-3 9L9 3l-3 9H2"})})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.enrolledStudents}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"+15% from last year"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Conversion Rate"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",className:"h-4 w-4 text-muted-foreground",children:(0,r.jsx)("path",{d:"M12 2v20m9-9H3"})})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[t.conversionRate,"%"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"+2.1% from last month"})]})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-7",children:[(0,r.jsxs)(a.Zb,{className:"col-span-4",children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Recent Applications"}),(0,r.jsx)(a.SZ,{children:"Latest admission applications requiring review"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"space-y-4",children:[{name:"John Smith",grade:"Grade 9",status:"Under Review",date:"2024-01-15"},{name:"Sarah Johnson",grade:"Grade 10",status:"Interview Scheduled",date:"2024-01-14"},{name:"Michael Brown",grade:"Grade 8",status:"Documents Pending",date:"2024-01-13"},{name:"Emily Davis",grade:"Grade 11",status:"Offered",date:"2024-01-12"}].map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-primary",children:e.name.split(" ").map(e=>e[0]).join("")})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.grade})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:e.status}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.date})]})]},s))})})]}),(0,r.jsxs)(a.Zb,{className:"col-span-3",children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Application Status Distribution"}),(0,r.jsx)(a.SZ,{children:"Current status of all applications"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"space-y-3",children:[{status:"Under Review",count:23,color:"bg-yellow-500"},{status:"Interview Scheduled",count:15,color:"bg-blue-500"},{status:"Offered",count:12,color:"bg-green-500"},{status:"Documents Pending",count:8,color:"bg-orange-500"},{status:"Waitlisted",count:5,color:"bg-purple-500"}].map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.color)}),(0,r.jsx)("span",{className:"text-sm",children:e.status})]}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.count})]},s))})})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6",children:[(0,r.jsx)(n(),{href:"/admissions/applications",children:(0,r.jsxs)(a.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"}),(0,r.jsx)(a.ll,{className:"text-lg",children:"Applications"})]})}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage applications"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Applications →"})]})]})}),(0,r.jsx)(n(),{href:"/admissions/interviews",children:(0,r.jsxs)(a.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFAF"}),(0,r.jsx)(a.ll,{className:"text-lg",children:"Interviews"})]})}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Schedule interviews"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Interviews →"})]})]})}),(0,r.jsx)(n(),{href:"/admissions/decisions",children:(0,r.jsxs)(a.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"✅"}),(0,r.jsx)(a.ll,{className:"text-lg",children:"Decisions"})]})}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Accept/reject students"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Make Decisions →"})]})]})}),(0,r.jsx)(n(),{href:"/admissions/classes",children:(0,r.jsxs)(a.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFEB"}),(0,r.jsx)(a.ll,{className:"text-lg",children:"Class Allocation"})]})}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Allocate to classes"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Classes →"})]})]})}),(0,r.jsx)(n(),{href:"/admissions/reports",children:(0,r.jsxs)(a.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,r.jsx)(a.ll,{className:"text-lg",children:"Reports"})]})}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Analytics & reports"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Reports →"})]})]})}),(0,r.jsx)(n(),{href:"/admissions/enrollment",children:(0,r.jsxs)(a.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"}),(0,r.jsx)(a.ll,{className:"text-lg",children:"Enrollment"})]})}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Enrollment status"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Enrollment →"})]})]})})]})]})}},1396:function(e,s,t){e.exports=t(5250)}},function(e){e.O(0,[7895,5250,2971,4938,1744],function(){return e(e.s=2377)}),_N_E=e.O()}]);