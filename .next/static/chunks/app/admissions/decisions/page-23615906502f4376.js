(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9417],{6346:function(e,t,s){Promise.resolve().then(s.bind(s,2879))},2879:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return r}});var a=s(7437),n=s(2265),i=s(9197);function l(e){let{tenantId:t,onAcceptStudent:s,onAllocateClass:l,onBack:c}=e,[d,r]=(0,n.useState)("pending"),[o,m]=(0,n.useState)(null),[x,u]=(0,n.useState)(!1),[h,p]=(0,n.useState)(""),[j,g]=(0,n.useState)(""),b=[{id:"1",applicationId:"APP-2024-001",studentName:"<PERSON>",grade:"Grade 9",stream:"Science",interviewDate:"2024-01-20",interviewer:"Dr. <PERSON>",overallScore:85,feedback:{academicKnowledge:88,communication:82,personality:85,motivation:90,comments:"Excellent student with strong academic foundation and clear goals. Shows great enthusiasm for science subjects."},recommendation:"accept",status:"pending_decision"},{id:"2",applicationId:"APP-2024-002",studentName:"<PERSON> <PERSON>",grade:"Grade 10",stream:"Arts",interviewDate:"2024-01-21",interviewer:"Prof. John Davis",overallScore:78,feedback:{academicKnowledge:75,communication:85,personality:80,motivation:75,comments:"Good communication skills and creative thinking. Would benefit from additional academic support."},recommendation:"accept",status:"pending_decision"},{id:"3",applicationId:"APP-2024-003",studentName:"Michael Brown",grade:"Grade 11",stream:"Science",interviewDate:"2024-01-19",interviewer:"Dr. Emily Davis",overallScore:92,feedback:{academicKnowledge:95,communication:88,personality:90,motivation:95,comments:"Outstanding candidate with exceptional academic abilities and leadership potential."},recommendation:"accept",status:"accepted"},{id:"4",applicationId:"APP-2024-004",studentName:"Emily Davis",grade:"Grade 9",stream:"Science",interviewDate:"2024-01-18",interviewer:"Dr. Sarah Wilson",overallScore:65,feedback:{academicKnowledge:60,communication:70,personality:65,motivation:65,comments:"Shows potential but needs improvement in academic fundamentals. Consider for waitlist."},recommendation:"waitlist",status:"waitlisted"}],v=e=>e>=85?"text-green-600":e>=70?"text-yellow-600":"text-red-600",N=e=>{switch(e){case"pending_decision":return"bg-yellow-100 text-yellow-800";case"accepted":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"waitlisted":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},f=e=>{m(e),p(""),g(""),u(!0)},y=()=>{o&&h&&s&&(s(o.id,h,j),u(!1),m(null))},w=b.filter(e=>"pending_decision"===e.status),k=b.filter(e=>"accepted"===e.status),S=b.filter(e=>"rejected"===e.status),D=b.filter(e=>"waitlisted"===e.status),C=e=>(0,a.jsx)(i.Zb,{className:"mb-4",children:(0,a.jsxs)(i.aY,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:e.studentName.split(" ").map(e=>e[0]).join("")})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-lg",children:e.studentName}),(0,a.jsx)("p",{className:"text-gray-600",children:e.applicationId}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.grade," - ",e.stream," | Interviewed by ",e.interviewer]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold ".concat(v(e.overallScore)),children:[e.overallScore,"/100"]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.interviewDate})]})]}),(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium",children:"Academic"}),(0,a.jsx)("div",{className:v(e.feedback.academicKnowledge),children:e.feedback.academicKnowledge})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium",children:"Communication"}),(0,a.jsx)("div",{className:v(e.feedback.communication),children:e.feedback.communication})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium",children:"Personality"}),(0,a.jsx)("div",{className:v(e.feedback.personality),children:e.feedback.personality})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium",children:"Motivation"}),(0,a.jsx)("div",{className:v(e.feedback.motivation),children:e.feedback.motivation})]})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.feedback.comments})}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(N(e.status)),children:e.status.replace("_"," ").toUpperCase()}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["Recommended: ",(0,a.jsx)("span",{className:"capitalize font-medium",children:e.recommendation})]})]}),(0,a.jsxs)("div",{className:"space-x-2",children:["pending_decision"===e.status&&(0,a.jsx)("button",{onClick:()=>f(e),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Make Decision"}),"accepted"===e.status&&(0,a.jsx)("button",{onClick:()=>null==l?void 0:l(e.id),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Allocate Class"})]})]})]})},e.id);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[c&&(0,a.jsx)("button",{onClick:c,className:"text-gray-600 hover:text-gray-800",children:"← Back"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Admission Decisions"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Review interview results and make admission decisions"})]})]})}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{className:"pb-3",children:(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Pending Decisions"})}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:w.length})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{className:"pb-3",children:(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Accepted"})}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:k.length})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{className:"pb-3",children:(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Waitlisted"})}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:D.length})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{className:"pb-3",children:(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Rejected"})}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:S.length})})]})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,a.jsxs)("button",{onClick:()=>r("pending"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("pending"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["⏳ Pending (",w.length,")"]}),(0,a.jsxs)("button",{onClick:()=>r("accepted"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("accepted"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["✅ Accepted (",k.length,")"]}),(0,a.jsxs)("button",{onClick:()=>r("waitlisted"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("waitlisted"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83D\uDFE1 Waitlisted (",D.length,")"]}),(0,a.jsxs)("button",{onClick:()=>r("rejected"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("rejected"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["❌ Rejected (",S.length,")"]})]})}),(0,a.jsxs)("div",{children:["pending"===d&&(0,a.jsx)("div",{children:w.length>0?w.map(C):(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No pending decisions"})}),"accepted"===d&&(0,a.jsx)("div",{children:k.length>0?k.map(C):(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No accepted students"})}),"waitlisted"===d&&(0,a.jsx)("div",{children:D.length>0?D.map(C):(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No waitlisted students"})}),"rejected"===d&&(0,a.jsx)("div",{children:S.length>0?S.map(C):(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No rejected students"})})]}),x&&(0,a.jsx)(()=>o?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-4",children:["Make Admission Decision for ",o.studentName]}),(0,a.jsxs)("div",{className:"mb-4 p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Application:"})," ",o.applicationId]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Grade:"})," ",o.grade]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Overall Score:"}),(0,a.jsxs)("span",{className:"ml-1 font-medium ".concat(v(o.overallScore)),children:[o.overallScore,"/100"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Recommendation:"}),(0,a.jsx)("span",{className:"ml-1 capitalize",children:o.recommendation})]})]}),(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("span",{className:"font-medium",children:"Interviewer Comments:"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:o.feedback.comments})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Decision"}),(0,a.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select Decision"}),(0,a.jsx)("option",{value:"accept",children:"Accept"}),(0,a.jsx)("option",{value:"reject",children:"Reject"}),(0,a.jsx)("option",{value:"waitlist",children:"Add to Waitlist"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Decision Notes"}),(0,a.jsx)("textarea",{value:j,onChange:e=>g(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Add notes about your decision..."})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,a.jsx)("button",{onClick:()=>u(!1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{onClick:y,disabled:!h,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Confirm Decision"})]})]})}):null,{})]})}var c=s(8438),d=s(9353);function r(){let[e,t]=(0,n.useState)("decisions"),[s,i]=(0,n.useState)(null),r=()=>{"allocation"===e?t("decisions"):"enrollment"===e&&t("allocation"),i(null)};return"allocation"===e?(0,a.jsx)(c.l,{tenantId:"demo-tenant-uuid-1234567890",onAllocateStudent:(e,s)=>{console.log("Student allocated:",{studentId:e,classId:s}),t("enrollment")},onBack:r}):"enrollment"===e?(0,a.jsx)(d.a,{tenantId:"demo-tenant-uuid-1234567890",onGenerateAdmissionLetter:e=>{console.log("Generate admission letter for:",e)},onBack:r}):(0,a.jsx)(l,{tenantId:"demo-tenant-uuid-1234567890",onAcceptStudent:(e,s,a)=>{console.log("Student decision:",{studentId:e,decision:s,notes:a}),"accept"===s&&(i(e),t("allocation"))},onAllocateClass:e=>{console.log("Navigate to class allocation for:",e),i(e),t("allocation")},onBack:()=>window.history.back()})}}},function(e){e.O(0,[7895,8438,9353,2971,4938,1744],function(){return e(e.s=6346)}),_N_E=e.O()}]);