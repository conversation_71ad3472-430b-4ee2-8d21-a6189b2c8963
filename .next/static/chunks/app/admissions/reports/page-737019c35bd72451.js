(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2833],{4686:function(e,s,t){Promise.resolve().then(t.bind(t,3279))},3279:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return n}});var a=t(7437),r=t(2265),l=t(9197);function n(){let[e,s]=(0,r.useState)("overview"),[t,n]=(0,r.useState)("this_month"),i={totalApplications:156,acceptedApplications:89,rejectedApplications:23,pendingApplications:44,interviewsScheduled:67,interviewsCompleted:45,acceptanceRate:57.1,averageProcessingTime:12.5};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Admissions Reports"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Analytics and insights for admission processes"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("select",{value:t,onChange:e=>n(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"this_week",children:"This Week"}),(0,a.jsx)("option",{value:"this_month",children:"This Month"}),(0,a.jsx)("option",{value:"this_quarter",children:"This Quarter"}),(0,a.jsx)("option",{value:"this_year",children:"This Year"}),(0,a.jsx)("option",{value:"custom",children:"Custom Range"})]}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Export Report"})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[(0,a.jsx)(l.ll,{children:"Report Type"}),(0,a.jsx)(l.SZ,{children:"Select the type of report you want to generate"})]}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4",children:[{id:"overview",name:"Admissions Overview",description:"General admissions statistics and trends"},{id:"applications",name:"Applications Report",description:"Detailed application analysis"},{id:"interviews",name:"Interview Report",description:"Interview statistics and outcomes"},{id:"enrollment",name:"Enrollment Report",description:"Enrollment trends and projections"},{id:"demographics",name:"Demographics Report",description:"Student demographic analysis"}].map(t=>(0,a.jsxs)("button",{onClick:()=>s(t.id),className:"p-4 border rounded-lg text-left transition-colors ".concat(e===t.id?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"),children:[(0,a.jsx)("h3",{className:"font-medium",children:t.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:t.description})]},t.id))})})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Total Applications"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"})]}),(0,a.jsxs)(l.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:i.totalApplications}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+12% from last month"})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Acceptance Rate"}),(0,a.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,a.jsxs)(l.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[i.acceptanceRate,"%"]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+2.1% from last month"})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Interviews Completed"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFAF"})]}),(0,a.jsxs)(l.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:i.interviewsCompleted}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["of ",i.interviewsScheduled," scheduled"]})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Avg Processing Time"}),(0,a.jsx)("span",{className:"text-2xl",children:"⏱️"})]}),(0,a.jsxs)(l.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[i.averageProcessingTime," days"]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"-1.2 days from last month"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[(0,a.jsx)(l.ll,{children:"Application Status Breakdown"}),(0,a.jsx)(l.SZ,{children:"Current status of all applications"})]}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Accepted"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:i.acceptedApplications}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",(i.acceptedApplications/i.totalApplications*100).toFixed(1),"%)"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Pending"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:i.pendingApplications}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",(i.pendingApplications/i.totalApplications*100).toFixed(1),"%)"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Rejected"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:i.rejectedApplications}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",(i.rejectedApplications/i.totalApplications*100).toFixed(1),"%)"]})]})]})]})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[(0,a.jsx)(l.ll,{children:"Monthly Application Trends"}),(0,a.jsx)(l.SZ,{children:"Application submissions over time"})]}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{month:"January",applications:45,change:"+12%"},{month:"February",applications:52,change:"+15%"},{month:"March",applications:38,change:"-27%"},{month:"April",applications:21,change:"-45%"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:e.month}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e.applications}),(0,a.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(e.change.startsWith("+")?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.change})]})]},s))})})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[(0,a.jsx)(l.ll,{children:"Detailed Application Data"}),(0,a.jsx)(l.SZ,{children:"Comprehensive view of application metrics"})]}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full table-auto",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Grade Level"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Applications"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Accepted"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Rejected"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Pending"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Acceptance Rate"})]})}),(0,a.jsx)("tbody",{children:[{grade:"Grade 9",total:45,accepted:28,rejected:8,pending:9,rate:62.2},{grade:"Grade 10",total:38,accepted:22,rejected:6,pending:10,rate:57.9},{grade:"Grade 11",total:42,accepted:25,rejected:5,pending:12,rate:59.5},{grade:"Grade 12",total:31,accepted:14,rejected:4,pending:13,rate:45.2}].map((e,s)=>(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"py-3 px-4 font-medium",children:e.grade}),(0,a.jsx)("td",{className:"py-3 px-4",children:e.total}),(0,a.jsx)("td",{className:"py-3 px-4 text-green-600",children:e.accepted}),(0,a.jsx)("td",{className:"py-3 px-4 text-red-600",children:e.rejected}),(0,a.jsx)("td",{className:"py-3 px-4 text-yellow-600",children:e.pending}),(0,a.jsxs)("td",{className:"py-3 px-4 font-medium",children:[e.rate,"%"]})]},s))})]})})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[(0,a.jsx)(l.ll,{children:"Export Options"}),(0,a.jsx)(l.SZ,{children:"Download reports in various formats"})]}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:[(0,a.jsx)("span",{children:"\uD83D\uDCCA"}),(0,a.jsx)("span",{children:"Export as Excel"})]}),(0,a.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:[(0,a.jsx)("span",{children:"\uD83D\uDCC4"}),(0,a.jsx)("span",{children:"Export as PDF"})]}),(0,a.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:[(0,a.jsx)("span",{children:"\uD83D\uDCC8"}),(0,a.jsx)("span",{children:"Export as CSV"})]}),(0,a.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:[(0,a.jsx)("span",{children:"\uD83D\uDDA8️"}),(0,a.jsx)("span",{children:"Print Report"})]})]})})]})]})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return c},aY:function(){return p},SZ:function(){return o},Ol:function(){return d},ll:function(){return x}});var a=t(7437),r=t(2265),l=t(7042),n=t(4769);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,l.W)(s))}let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});c.displayName="Card";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",t),...r})});d.displayName="CardHeader";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",t),...r})});x.displayName="CardTitle";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",t),...r})});o.displayName="CardDescription";let p=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:i("p-6 pt-0",t),...r})});p.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=4686)}),_N_E=e.O()}]);