(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8729],{5381:function(e,t,s){Promise.resolve().then(s.bind(s,1533))},1533:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return n}});var r=s(7437),i=s(2265),a=s(9197);function l(e){let{tenantId:t,onCreateReservation:s,onFulfillReservation:l,onCancelReservation:n,onExtendReservation:d}=e,[c,o]=(0,i.useState)("active"),[m,x]=(0,i.useState)(""),[u,h]=(0,i.useState)("all"),[p,f]=(0,i.useState)(null),[b,j]=(0,i.useState)(!1),v=[{id:"1",reservationNumber:"RES-2024-001",itemId:"BOOK-001",itemTitle:"Advanced Mathematics for Engineers",itemType:"book",isbn:"978-0-123456-78-9",memberId:"STU-002",memberName:"<PERSON>",memberType:"student",reservationDate:"2024-01-20",expiryDate:"2024-02-03",status:"active",priority:1,queuePosition:1,notificationSent:!1,notes:"Needed for upcoming exam"},{id:"2",reservationNumber:"RES-2024-002",itemId:"BOOK-002",itemTitle:"Physics Fundamentals",itemType:"book",isbn:"978-0-987654-32-1",memberId:"FAC-001",memberName:"Dr. Emily Davis",memberType:"faculty",reservationDate:"2024-01-18",expiryDate:"2024-02-01",status:"active",priority:2,queuePosition:1,notificationSent:!0,notes:"Research reference material"},{id:"3",reservationNumber:"RES-2024-003",itemId:"JOUR-001",itemTitle:"Scientific Journal of Chemistry",itemType:"journal",isbn:"978-0-789123-45-6",memberId:"STU-003",memberName:"Michael Brown",memberType:"student",reservationDate:"2024-01-15",expiryDate:"2024-01-29",status:"fulfilled",priority:1,queuePosition:0,notificationSent:!0,notes:"Chemistry project research"},{id:"4",reservationNumber:"RES-2024-004",itemId:"BOOK-003",itemTitle:"Digital Learning Platforms",itemType:"ebook",isbn:"978-0-456789-12-3",memberId:"STF-001",memberName:"Sarah Wilson",memberType:"staff",reservationDate:"2024-01-10",expiryDate:"2024-01-24",status:"expired",priority:1,queuePosition:0,notificationSent:!0,notes:"Professional development reading"}],y=e=>{switch(e){case"active":return"bg-blue-100 text-blue-800";case"fulfilled":return"bg-green-100 text-green-800";case"expired":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},g=e=>{switch(e){case"student":return"bg-blue-100 text-blue-800";case"faculty":return"bg-green-100 text-green-800";case"staff":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},N=e=>{switch(e){case 1:return"text-red-600";case 2:return"text-orange-600";case 3:return"text-yellow-600";default:return"text-gray-600"}},C=v.filter(e=>{let t=e.memberName.toLowerCase().includes(m.toLowerCase())||e.itemTitle.toLowerCase().includes(m.toLowerCase())||e.reservationNumber.toLowerCase().includes(m.toLowerCase()),s="all"===u||e.memberType===u,r="all"===c||"active"===c&&"active"===e.status||"fulfilled"===c&&"fulfilled"===e.status||"expired"===c&&"expired"===e.status;return t&&s&&r}),w=e=>{let t=new Date(e),s=new Date;return Math.ceil((t.getTime()-s.getTime())/864e5)};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Reservation System"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage book reservations and holds"})]}),(0,r.jsx)("button",{onClick:s,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Create Reservation"})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,r.jsxs)(a.Zb,{children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Active Reservations"})}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:v.filter(e=>"active"===e.status).length})})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Fulfilled Today"})}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"5"})})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Expired"})}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:v.filter(e=>"expired"===e.status).length})})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Expiring Soon"})}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:v.filter(e=>"active"===e.status&&3>=w(e.expiryDate)).length})})]})]}),(0,r.jsx)(a.Zb,{children:(0,r.jsx)(a.aY,{className:"pt-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,r.jsx)("input",{type:"text",placeholder:"Search by member name, item title, or reservation number",value:m,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Member Type"}),(0,r.jsxs)("select",{value:u,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Members"}),(0,r.jsx)("option",{value:"student",children:"Students"}),(0,r.jsx)("option",{value:"faculty",children:"Faculty"}),(0,r.jsx)("option",{value:"staff",children:"Staff"}),(0,r.jsx)("option",{value:"guest",children:"Guests"})]})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsx)("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Reservations"})})]})})}),(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,r.jsxs)("button",{onClick:()=>o("active"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("active"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83D\uDCCB Active (",v.filter(e=>"active"===e.status).length,")"]}),(0,r.jsxs)("button",{onClick:()=>o("fulfilled"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("fulfilled"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["✅ Fulfilled (",v.filter(e=>"fulfilled"===e.status).length,")"]}),(0,r.jsxs)("button",{onClick:()=>o("expired"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("expired"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["❌ Expired (",v.filter(e=>"expired"===e.status).length,")"]}),(0,r.jsxs)("button",{onClick:()=>o("all"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("all"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83D\uDCDA All Reservations (",v.length,")"]})]})}),(0,r.jsx)("div",{className:"space-y-4",children:C.map(e=>(0,r.jsx)(a.Zb,{children:(0,r.jsxs)(a.aY,{className:"pt-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-blue-600 font-medium",children:e.memberName.split(" ").map(e=>e[0]).join("")})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-lg",children:e.itemTitle}),(0,r.jsxs)("p",{className:"text-gray-600",children:[e.memberName," (",e.memberId,")"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(g(e.memberType)),children:e.memberType.toUpperCase()}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(y(e.status)),children:e.status.toUpperCase()}),(0,r.jsx)("span",{className:"text-xs font-medium ".concat(N(e.priority)),children:1===e.priority?"HIGH PRIORITY":2===e.priority?"MEDIUM PRIORITY":"LOW PRIORITY"})]})]})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsxs)("div",{children:["Reserved: ",e.reservationDate]}),(0,r.jsxs)("div",{children:["Expires: ",e.expiryDate]}),"active"===e.status&&(0,r.jsx)("div",{className:3>=w(e.expiryDate)?"text-red-600 font-medium":"text-gray-600",children:w(e.expiryDate)>0?"".concat(w(e.expiryDate)," days left"):"Expired"}),e.queuePosition>0&&(0,r.jsxs)("div",{className:"text-blue-600 font-medium",children:["Queue position: ",e.queuePosition]})]})})]}),(0,r.jsxs)("div",{className:"mt-4 flex justify-end space-x-2",children:[(0,r.jsx)("button",{onClick:()=>f(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"}),"active"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>null==l?void 0:l(e.id),className:"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700",children:"Fulfill"}),(0,r.jsx)("button",{onClick:()=>null==n?void 0:n(e.id),className:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700",children:"Cancel"})]})]})]})},e.id))}),p&&(0,r.jsx)(()=>p?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold",children:"Reservation Details"}),(0,r.jsx)("button",{onClick:()=>f(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-3",children:"Reservation Information"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Reservation Number:"})," ",p.reservationNumber]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Item Title:"})," ",p.itemTitle]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Item Type:"})," ",p.itemType]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"ISBN:"})," ",p.isbn]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Reservation Date:"})," ",p.reservationDate]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Expiry Date:"})," ",p.expiryDate]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Status:"}),(0,r.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(y(p.status)),children:p.status.toUpperCase()})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-3",children:"Member Information"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Member Name:"})," ",p.memberName]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Member ID:"})," ",p.memberId]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Member Type:"}),(0,r.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(g(p.memberType)),children:p.memberType.toUpperCase()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Priority:"}),(0,r.jsx)("span",{className:"ml-2 font-medium ".concat(N(p.priority)),children:1===p.priority?"High":2===p.priority?"Medium":"Low"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Queue Position:"})," ",p.queuePosition||"N/A"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Notification Sent:"}),(0,r.jsx)("span",{className:p.notificationSent?"text-green-600":"text-red-600",children:p.notificationSent?"Yes":"No"})]})]})]})]}),p.notes&&(0,r.jsxs)(a.Zb,{className:"mb-6",children:[(0,r.jsx)(a.Ol,{children:(0,r.jsx)(a.ll,{className:"text-lg",children:"Notes"})}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("p",{className:"text-sm text-gray-600",children:p.notes})})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:["active"===p.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>null==l?void 0:l(p.id),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Fulfill Reservation"}),(0,r.jsx)("button",{onClick:()=>null==d?void 0:d(p.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Extend Reservation"}),(0,r.jsx)("button",{onClick:()=>null==n?void 0:n(p.id),className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700",children:"Cancel Reservation"})]}),(0,r.jsx)("button",{className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Send Notification"})]})]})}):null,{})]})}function n(){return(0,r.jsx)(l,{tenantId:"demo-tenant-uuid-1234567890",onCreateReservation:()=>{console.log("Create new reservation")},onFulfillReservation:e=>{console.log("Fulfill reservation:",e),alert("Reservation ".concat(e," fulfilled successfully"))},onCancelReservation:e=>{console.log("Cancel reservation:",e),confirm("Are you sure you want to cancel this reservation?")&&alert("Reservation ".concat(e," cancelled successfully"))},onExtendReservation:e=>{console.log("Extend reservation:",e),alert("Reservation ".concat(e," extended successfully"))}})}},9197:function(e,t,s){"use strict";s.d(t,{Zb:function(){return d},aY:function(){return x},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var r=s(7437),i=s(2265),a=s(7042),l=s(4769);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,l.m6)((0,a.W)(t))}let d=i.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)("div",{ref:t,className:n("rounded-lg border bg-card text-card-foreground shadow-sm",s),...i})});d.displayName="Card";let c=i.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)("div",{ref:t,className:n("flex flex-col space-y-1.5 p-6",s),...i})});c.displayName="CardHeader";let o=i.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)("h3",{ref:t,className:n("text-2xl font-semibold leading-none tracking-tight",s),...i})});o.displayName="CardTitle";let m=i.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)("p",{ref:t,className:n("text-sm text-muted-foreground",s),...i})});m.displayName="CardDescription";let x=i.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)("div",{ref:t,className:n("p-6 pt-0",s),...i})});x.displayName="CardContent",i.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)("div",{ref:t,className:n("flex items-center p-6 pt-0",s),...i})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=5381)}),_N_E=e.O()}]);