(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7789],{4608:function(e,s,t){Promise.resolve().then(t.bind(t,9609))},9609:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return i}});var r=t(7437),n=t(2265),a=t(9197);function l(e){let{tenantId:s,onAddMember:t,onEditMember:l,onSuspendMember:i,onActivateMember:d}=e,[c,o]=(0,n.useState)(""),[m,x]=(0,n.useState)("all"),[u,h]=(0,n.useState)("all"),[p,j]=(0,n.useState)(null),[b,f]=(0,n.useState)(!1),g=[{id:"1",memberId:"STU-001",name:"<PERSON>",email:"<EMAIL>",phone:"******-567-8901",type:"student",department:"Computer Science",grade:"Grade 11",rollNumber:"11A001",membershipDate:"2024-01-15",expiryDate:"2024-12-31",status:"active",borrowingLimits:{books:5,journals:3,dvds:2,ebooks:10},currentBorrowedItems:3,totalBorrowedItems:25,overdueItems:0,fineAmount:0,address:"123 Main Street, City, State 12345",emergencyContact:{name:"Jane Smith",phone:"******-567-8902",relationship:"Mother"},notes:"Regular library user, good borrowing history"},{id:"2",memberId:"FAC-001",name:"Dr. Emily Davis",email:"<EMAIL>",phone:"******-567-8903",type:"faculty",department:"Mathematics",employeeId:"EMP-001",membershipDate:"2023-08-01",expiryDate:"2025-07-31",status:"active",borrowingLimits:{books:10,journals:8,dvds:5,ebooks:20},currentBorrowedItems:6,totalBorrowedItems:150,overdueItems:1,fineAmount:5.5,address:"456 Faculty Lane, City, State 12345",emergencyContact:{name:"Michael Davis",phone:"******-567-8904",relationship:"Spouse"},notes:"Department head, research focus on advanced mathematics"},{id:"3",memberId:"STF-001",name:"Sarah Johnson",email:"<EMAIL>",phone:"******-567-8905",type:"staff",department:"Administration",employeeId:"EMP-002",membershipDate:"2023-09-01",expiryDate:"2024-08-31",status:"active",borrowingLimits:{books:3,journals:2,dvds:1,ebooks:5},currentBorrowedItems:1,totalBorrowedItems:12,overdueItems:0,fineAmount:0,address:"789 Staff Road, City, State 12345",emergencyContact:{name:"Robert Johnson",phone:"******-567-8906",relationship:"Husband"}},{id:"4",memberId:"GST-001",name:"Michael Brown",email:"<EMAIL>",phone:"******-567-8907",type:"guest",department:"Visitor",membershipDate:"2024-01-20",expiryDate:"2024-02-20",status:"expired",borrowingLimits:{books:2,journals:1,dvds:0,ebooks:2},currentBorrowedItems:0,totalBorrowedItems:3,overdueItems:0,fineAmount:0,address:"321 Guest Avenue, City, State 12345",emergencyContact:{name:"Lisa Brown",phone:"******-567-8908",relationship:"Sister"},notes:"Visiting researcher, temporary access"}],v=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"inactive":default:return"bg-gray-100 text-gray-800";case"suspended":return"bg-red-100 text-red-800";case"expired":return"bg-orange-100 text-orange-800"}},N=e=>{switch(e){case"student":return"bg-blue-100 text-blue-800";case"faculty":return"bg-green-100 text-green-800";case"staff":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},y=g.filter(e=>{let s=e.name.toLowerCase().includes(c.toLowerCase())||e.memberId.toLowerCase().includes(c.toLowerCase())||e.email.toLowerCase().includes(c.toLowerCase()),t="all"===m||e.type===m,r="all"===u||e.status===u;return s&&t&&r});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Member Management"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage library members and memberships"})]}),(0,r.jsx)("button",{onClick:t,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Add Member"})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,r.jsxs)(a.Zb,{children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Total Members"})}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:g.length})})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Active Members"})}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:g.filter(e=>"active"===e.status).length})})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Overdue Items"})}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:g.reduce((e,s)=>e+s.overdueItems,0)})})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsx)(a.Ol,{className:"pb-3",children:(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Total Fines"})}),(0,r.jsx)(a.aY,{children:(0,r.jsxs)("div",{className:"text-2xl font-bold text-orange-600",children:["$",g.reduce((e,s)=>e+s.fineAmount,0).toFixed(2)]})})]})]}),(0,r.jsx)(a.Zb,{children:(0,r.jsx)(a.aY,{className:"pt-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,r.jsx)("input",{type:"text",placeholder:"Search by name, member ID, or email",value:c,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Member Type"}),(0,r.jsxs)("select",{value:m,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Types"}),(0,r.jsx)("option",{value:"student",children:"Students"}),(0,r.jsx)("option",{value:"faculty",children:"Faculty"}),(0,r.jsx)("option",{value:"staff",children:"Staff"}),(0,r.jsx)("option",{value:"guest",children:"Guests"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,r.jsxs)("select",{value:u,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Status"}),(0,r.jsx)("option",{value:"active",children:"Active"}),(0,r.jsx)("option",{value:"inactive",children:"Inactive"}),(0,r.jsx)("option",{value:"suspended",children:"Suspended"}),(0,r.jsx)("option",{value:"expired",children:"Expired"})]})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsx)("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Members"})})]})})}),(0,r.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:y.map(e=>(0,r.jsxs)(a.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(a.ll,{className:"text-lg",children:e.name}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(v(e.status)),children:e.status.toUpperCase()})]}),(0,r.jsxs)(a.SZ,{children:[e.memberId," • ",e.department]})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Type:"}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(N(e.type)),children:e.type.toUpperCase()})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Current Borrowed:"}),(0,r.jsx)("span",{className:"font-medium",children:e.currentBorrowedItems})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Overdue:"}),(0,r.jsx)("span",{className:e.overdueItems>0?"text-red-600 font-medium":"text-green-600",children:e.overdueItems})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Fine:"}),(0,r.jsxs)("span",{className:e.fineAmount>0?"text-red-600 font-medium":"text-green-600",children:["$",e.fineAmount.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Expires:"}),(0,r.jsx)("span",{className:"font-medium",children:e.expiryDate})]})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["Member since: ",e.membershipDate]}),(0,r.jsx)("button",{onClick:()=>j(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"})]})]})]},e.id))}),p&&(0,r.jsx)(()=>p?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold",children:p.name}),(0,r.jsx)("button",{onClick:()=>j(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-3",children:"Personal Information"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Member ID:"})," ",p.memberId]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Email:"})," ",p.email]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Phone:"})," ",p.phone]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Type:"}),(0,r.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(N(p.type)),children:p.type.toUpperCase()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Department:"})," ",p.department]}),p.grade&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Grade:"})," ",p.grade]}),p.rollNumber&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Roll Number:"})," ",p.rollNumber]}),p.employeeId&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Employee ID:"})," ",p.employeeId]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Address:"})," ",p.address]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-3",children:"Membership Details"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Membership Date:"})," ",p.membershipDate]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Expiry Date:"})," ",p.expiryDate]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Status:"}),(0,r.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(v(p.status)),children:p.status.toUpperCase()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Current Borrowed:"})," ",p.currentBorrowedItems]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Total Borrowed:"})," ",p.totalBorrowedItems]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Overdue Items:"}),(0,r.jsx)("span",{className:p.overdueItems>0?"text-red-600 font-medium":"text-green-600",children:p.overdueItems})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Fine Amount:"}),(0,r.jsxs)("span",{className:p.fineAmount>0?"text-red-600 font-medium":"text-green-600",children:["$",p.fineAmount.toFixed(2)]})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)(a.Zb,{children:[(0,r.jsx)(a.Ol,{children:(0,r.jsx)(a.ll,{className:"text-lg",children:"Borrowing Limits"})}),(0,r.jsx)(a.aY,{children:(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Books:"}),(0,r.jsx)("span",{className:"font-medium",children:p.borrowingLimits.books})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Journals:"}),(0,r.jsx)("span",{className:"font-medium",children:p.borrowingLimits.journals})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"DVDs:"}),(0,r.jsx)("span",{className:"font-medium",children:p.borrowingLimits.dvds})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"E-books:"}),(0,r.jsx)("span",{className:"font-medium",children:p.borrowingLimits.ebooks})]})]})})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsx)(a.Ol,{children:(0,r.jsx)(a.ll,{className:"text-lg",children:"Emergency Contact"})}),(0,r.jsx)(a.aY,{children:(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Name:"})," ",p.emergencyContact.name]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Phone:"})," ",p.emergencyContact.phone]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Relationship:"})," ",p.emergencyContact.relationship]})]})})]})]}),p.notes&&(0,r.jsxs)(a.Zb,{className:"mb-6",children:[(0,r.jsx)(a.Ol,{children:(0,r.jsx)(a.ll,{className:"text-lg",children:"Notes"})}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("p",{className:"text-sm text-gray-600",children:p.notes})})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:()=>null==l?void 0:l(p.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Edit Member"}),"active"===p.status?(0,r.jsx)("button",{onClick:()=>null==i?void 0:i(p.id),className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700",children:"Suspend Member"}):(0,r.jsx)("button",{onClick:()=>null==d?void 0:d(p.id),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Activate Member"}),(0,r.jsx)("button",{className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700",children:"View History"})]})]})}):null,{})]})}function i(){return(0,r.jsx)(l,{tenantId:"demo-tenant-uuid-1234567890",onAddMember:()=>{console.log("Add new library member")},onEditMember:e=>{console.log("Edit member:",e)},onSuspendMember:e=>{console.log("Suspend member:",e),confirm("Are you sure you want to suspend this member?")&&alert("Member ".concat(e," suspended successfully"))},onActivateMember:e=>{console.log("Activate member:",e),alert("Member ".concat(e," activated successfully"))}})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return d},aY:function(){return x},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var r=t(7437),n=t(2265),a=t(7042),l=t(4769);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,l.m6)((0,a.W)(s))}let d=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});d.displayName="Card";let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",t),...n})});c.displayName="CardHeader";let o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",t),...n})});o.displayName="CardTitle";let m=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",t),...n})});m.displayName="CardDescription";let x=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:i("p-6 pt-0",t),...n})});x.displayName="CardContent",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=4608)}),_N_E=e.O()}]);