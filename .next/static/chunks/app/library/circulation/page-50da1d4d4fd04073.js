(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1818],{4595:function(e,t,r){Promise.resolve().then(r.bind(r,5074))},5074:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return c}});var s=r(7437),n=r(2265),a=r(9197);function l(e){let{tenantId:t,onCheckout:r,onCheckin:l,onRenewal:c}=e,[d,i]=(0,n.useState)("active"),[o,u]=(0,n.useState)(""),[m,x]=(0,n.useState)("all"),[h,b]=(0,n.useState)({type:"checkout",memberId:"",itemId:""}),f=[{id:"1",transactionType:"checkout",itemId:"BOOK-001",itemTitle:"Advanced Mathematics for Engineers",itemType:"book",isbn:"978-0-123456-78-9",memberId:"STU-001",memberName:"<PERSON>",memberType:"student",checkoutDate:"2024-01-15",dueDate:"2024-02-15",renewalCount:0,maxRenewals:2,fineAmount:0,status:"active",notes:"Regular checkout"},{id:"2",transactionType:"checkout",itemId:"BOOK-002",itemTitle:"Physics Fundamentals",itemType:"book",isbn:"978-0-987654-32-1",memberId:"STU-002",memberName:"Sarah Johnson",memberType:"student",checkoutDate:"2024-01-10",dueDate:"2024-01-25",renewalCount:1,maxRenewals:2,fineAmount:5.5,status:"overdue",notes:"Overdue by 5 days"},{id:"3",transactionType:"checkout",itemId:"JOUR-001",itemTitle:"Scientific Journal of Chemistry",itemType:"journal",isbn:"978-0-789123-45-6",memberId:"FAC-001",memberName:"Dr. Emily Davis",memberType:"faculty",checkoutDate:"2024-01-20",dueDate:"2024-03-20",returnDate:"2024-01-28",renewalCount:0,maxRenewals:3,fineAmount:0,status:"returned"}],p=e=>{switch(e){case"active":return"bg-blue-100 text-blue-800";case"returned":return"bg-green-100 text-green-800";case"overdue":return"bg-red-100 text-red-800";case"lost":return"bg-orange-100 text-orange-800";case"damaged":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},g=e=>{switch(e){case"student":return"bg-blue-100 text-blue-800";case"faculty":return"bg-green-100 text-green-800";case"staff":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},j=f.filter(e=>{let t=e.memberName.toLowerCase().includes(o.toLowerCase())||e.itemTitle.toLowerCase().includes(o.toLowerCase())||e.memberId.toLowerCase().includes(o.toLowerCase()),r="all"===m||e.memberType===m,s="all"===d||"active"===d&&"active"===e.status||"overdue"===d&&"overdue"===e.status||"returned"===d&&"returned"===e.status;return t&&r&&s}),y=e=>{let t=new Date(e),r=Math.ceil((new Date().getTime()-t.getTime())/864e5);return r>0?r:0};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Circulation Management"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage book check-in, check-out, and renewals"})]}),(0,s.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Circulation Report"})]}),(0,s.jsxs)(a.Zb,{children:[(0,s.jsxs)(a.Ol,{children:[(0,s.jsx)(a.ll,{children:"Quick Actions"}),(0,s.jsx)(a.SZ,{children:"Perform quick check-in, check-out, or renewal operations"})]}),(0,s.jsx)(a.aY,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Action Type"}),(0,s.jsxs)("select",{value:h.type,onChange:e=>b({...h,type:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"checkout",children:"Check Out"}),(0,s.jsx)("option",{value:"checkin",children:"Check In"}),(0,s.jsx)("option",{value:"renewal",children:"Renewal"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Member ID"}),(0,s.jsx)("input",{type:"text",placeholder:"Enter member ID",value:h.memberId,onChange:e=>b({...h,memberId:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Item ID / ISBN"}),(0,s.jsx)("input",{type:"text",placeholder:"Enter item ID or ISBN",value:h.itemId,onChange:e=>b({...h,itemId:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{onClick:()=>{if(!h.memberId||!h.itemId){alert("Please enter both Member ID and Item ID");return}switch(h.type){case"checkout":null==r||r(h.memberId,h.itemId);break;case"checkin":let e=f.find(e=>e.memberId===h.memberId&&e.itemId===h.itemId&&"active"===e.status);e&&(null==l||l(e.id));break;case"renewal":let t=f.find(e=>e.memberId===h.memberId&&e.itemId===h.itemId&&"active"===e.status);t&&(null==c||c(t.id))}b({type:"checkout",memberId:"",itemId:""})},className:"w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"checkout"===h.type?"Check Out":"checkin"===h.type?"Check In":"Renew"})}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Scan Barcode"})})]})})]}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,s.jsxs)(a.Zb,{children:[(0,s.jsx)(a.Ol,{className:"pb-3",children:(0,s.jsx)(a.ll,{className:"text-sm font-medium",children:"Active Checkouts"})}),(0,s.jsx)(a.aY,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:f.filter(e=>"active"===e.status).length})})]}),(0,s.jsxs)(a.Zb,{children:[(0,s.jsx)(a.Ol,{className:"pb-3",children:(0,s.jsx)(a.ll,{className:"text-sm font-medium",children:"Overdue Items"})}),(0,s.jsx)(a.aY,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:f.filter(e=>"overdue"===e.status).length})})]}),(0,s.jsxs)(a.Zb,{children:[(0,s.jsx)(a.Ol,{className:"pb-3",children:(0,s.jsx)(a.ll,{className:"text-sm font-medium",children:"Total Fines"})}),(0,s.jsx)(a.aY,{children:(0,s.jsxs)("div",{className:"text-2xl font-bold text-orange-600",children:["$",f.reduce((e,t)=>e+t.fineAmount,0).toFixed(2)]})})]}),(0,s.jsxs)(a.Zb,{children:[(0,s.jsx)(a.Ol,{className:"pb-3",children:(0,s.jsx)(a.ll,{className:"text-sm font-medium",children:"Today's Returns"})}),(0,s.jsx)(a.aY,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"8"})})]})]}),(0,s.jsx)(a.Zb,{children:(0,s.jsx)(a.aY,{className:"pt-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,s.jsx)("input",{type:"text",placeholder:"Search by member name, item title, or member ID",value:o,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Member Type"}),(0,s.jsxs)("select",{value:m,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Members"}),(0,s.jsx)("option",{value:"student",children:"Students"}),(0,s.jsx)("option",{value:"faculty",children:"Faculty"}),(0,s.jsx)("option",{value:"staff",children:"Staff"}),(0,s.jsx)("option",{value:"guest",children:"Guests"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Records"})})]})})}),(0,s.jsx)("div",{className:"border-b border-gray-200",children:(0,s.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,s.jsxs)("button",{onClick:()=>i("active"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("active"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83D\uDCDA Active Checkouts (",f.filter(e=>"active"===e.status).length,")"]}),(0,s.jsxs)("button",{onClick:()=>i("overdue"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("overdue"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["⚠️ Overdue (",f.filter(e=>"overdue"===e.status).length,")"]}),(0,s.jsxs)("button",{onClick:()=>i("returned"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("returned"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["✅ Returned (",f.filter(e=>"returned"===e.status).length,")"]}),(0,s.jsxs)("button",{onClick:()=>i("all"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("all"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83D\uDCCB All Records (",f.length,")"]})]})}),(0,s.jsx)("div",{className:"space-y-4",children:j.map(e=>(0,s.jsx)(a.Zb,{children:(0,s.jsxs)(a.aY,{className:"pt-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-blue-600 font-medium",children:e.memberName.split(" ").map(e=>e[0]).join("")})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-lg",children:e.itemTitle}),(0,s.jsxs)("p",{className:"text-gray-600",children:[e.memberName," (",e.memberId,")"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,s.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(g(e.memberType)),children:e.memberType.toUpperCase()}),(0,s.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(p(e.status)),children:e.status.toUpperCase()})]})]})]}),(0,s.jsx)("div",{className:"text-right",children:(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsxs)("div",{children:["Checked out: ",e.checkoutDate]}),(0,s.jsxs)("div",{children:["Due: ",e.dueDate]}),e.returnDate&&(0,s.jsxs)("div",{children:["Returned: ",e.returnDate]}),"overdue"===e.status&&(0,s.jsxs)("div",{className:"text-red-600 font-medium",children:["Overdue by ",y(e.dueDate)," days"]}),e.fineAmount>0&&(0,s.jsxs)("div",{className:"text-orange-600 font-medium",children:["Fine: $",e.fineAmount.toFixed(2)]})]})})]}),(0,s.jsxs)("div",{className:"mt-4 flex justify-end space-x-2",children:["active"===e.status&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>null==l?void 0:l(e.id),className:"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700",children:"Check In"}),e.renewalCount<e.maxRenewals&&(0,s.jsxs)("button",{onClick:()=>null==c?void 0:c(e.id),className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:["Renew (",e.renewalCount,"/",e.maxRenewals,")"]})]}),(0,s.jsx)("button",{className:"text-gray-600 hover:text-gray-800 text-sm font-medium",children:"View Details"})]})]})},e.id))})]})}function c(){return(0,s.jsx)(l,{tenantId:"demo-tenant-uuid-1234567890",onCheckout:(e,t)=>{console.log("Checkout item:",{memberId:e,itemId:t}),alert("Item ".concat(t," checked out to member ").concat(e))},onCheckin:e=>{console.log("Check in transaction:",e),alert("Transaction ".concat(e," checked in successfully"))},onRenewal:e=>{console.log("Renew transaction:",e),alert("Transaction ".concat(e," renewed successfully"))}})}},9197:function(e,t,r){"use strict";r.d(t,{Zb:function(){return d},aY:function(){return m},SZ:function(){return u},Ol:function(){return i},ll:function(){return o}});var s=r(7437),n=r(2265),a=r(7042),l=r(4769);function c(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,l.m6)((0,a.W)(t))}let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:c("rounded-lg border bg-card text-card-foreground shadow-sm",r),...n})});d.displayName="Card";let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:c("flex flex-col space-y-1.5 p-6",r),...n})});i.displayName="CardHeader";let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("h3",{ref:t,className:c("text-2xl font-semibold leading-none tracking-tight",r),...n})});o.displayName="CardTitle";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("p",{ref:t,className:c("text-sm text-muted-foreground",r),...n})});u.displayName="CardDescription";let m=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:c("p-6 pt-0",r),...n})});m.displayName="CardContent",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:c("flex items-center p-6 pt-0",r),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=4595)}),_N_E=e.O()}]);