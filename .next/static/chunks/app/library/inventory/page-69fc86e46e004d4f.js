(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4487],{3476:function(e,t,s){Promise.resolve().then(s.bind(s,8321))},8321:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return n}});var a=s(7437),l=s(2265),i=s(9197);function r(e){let{tenantId:t,onStartAudit:s,onUpdateInventory:r,onGenerateReport:n}=e,[d,o]=(0,l.useState)("inventory"),[c,x]=(0,l.useState)(""),[u,m]=(0,l.useState)("all"),[h,p]=(0,l.useState)("all"),[j,g]=(0,l.useState)("all"),b=[{id:"1",itemId:"BOOK-001",title:"Advanced Mathematics for Engineers",author:"<PERSON>. <PERSON>",isbn:"978-0-123456-78-9",category:"Mathematics",type:"book",location:"Main Library",shelfNumber:"MAT-001-A",totalCopies:5,availableCopies:3,checkedOutCopies:2,reservedCopies:0,damagedCopies:0,lostCopies:0,lastAuditDate:"2024-01-15",acquisitionDate:"2023-08-15",cost:89.99,condition:"excellent",status:"active"},{id:"2",itemId:"BOOK-002",title:"Physics Fundamentals",author:"Dr. Sarah Wilson",isbn:"978-0-987654-32-1",category:"Physics",type:"book",location:"Science Section",shelfNumber:"PHY-002-B",totalCopies:8,availableCopies:5,checkedOutCopies:2,reservedCopies:0,damagedCopies:1,lostCopies:0,lastAuditDate:"2024-01-10",acquisitionDate:"2023-09-01",cost:75.5,condition:"good",status:"active"},{id:"3",itemId:"JOUR-001",title:"Scientific Journal of Chemistry",author:"Various Authors",isbn:"978-0-789123-45-6",category:"Chemistry",type:"journal",location:"Periodicals Section",shelfNumber:"JOU-CHE-001",totalCopies:12,availableCopies:8,checkedOutCopies:3,reservedCopies:0,damagedCopies:0,lostCopies:1,lastAuditDate:"2024-01-05",acquisitionDate:"2023-01-01",cost:120,condition:"good",status:"active"}],y=[{id:"1",auditDate:"2024-01-15",auditedBy:"Library Staff",itemsAudited:150,discrepanciesFound:3,status:"completed",notes:"Quarterly audit completed. Minor discrepancies in periodicals section."},{id:"2",auditDate:"2024-01-01",auditedBy:"Head Librarian",itemsAudited:500,discrepanciesFound:8,status:"completed",notes:"Annual inventory audit. Several items need condition updates."}],f=e=>{switch(e){case"excellent":return"bg-green-100 text-green-800";case"good":return"bg-blue-100 text-blue-800";case"fair":return"bg-yellow-100 text-yellow-800";case"poor":return"bg-orange-100 text-orange-800";case"damaged":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},v=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"inactive":default:return"bg-gray-100 text-gray-800";case"withdrawn":return"bg-red-100 text-red-800"}},N=b.filter(e=>{let t=e.title.toLowerCase().includes(c.toLowerCase())||e.author.toLowerCase().includes(c.toLowerCase())||e.isbn.toLowerCase().includes(c.toLowerCase())||e.itemId.toLowerCase().includes(c.toLowerCase()),s="all"===u||e.category===u,a="all"===h||e.condition===h,l="all"===j||e.location===j;return t&&s&&a&&l});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Tracking"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Track library resources and manage inventory"})]}),(0,a.jsx)("button",{onClick:()=>null==n?void 0:n("full_inventory"),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Generate Full Report"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{className:"pb-3",children:(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Total Items"})}),(0,a.jsxs)(i.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:b.reduce((e,t)=>e+t.totalCopies,0)}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[b.length," unique titles"]})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{className:"pb-3",children:(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Available"})}),(0,a.jsxs)(i.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:b.reduce((e,t)=>e+t.availableCopies,0)}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Ready for checkout"})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{className:"pb-3",children:(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Issues"})}),(0,a.jsxs)(i.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:b.reduce((e,t)=>e+t.damagedCopies+t.lostCopies,0)}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Damaged or lost"})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{className:"pb-3",children:(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Total Value"})}),(0,a.jsxs)(i.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:["$",b.reduce((e,t)=>e+t.cost*t.totalCopies,0).toFixed(2)]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Collection value"})]})]})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,a.jsxs)("button",{onClick:()=>o("inventory"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("inventory"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83D\uDCDA Inventory (",b.length,")"]}),(0,a.jsxs)("button",{onClick:()=>o("audit"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("audit"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83D\uDD0D Audit History (",y.length,")"]})]})}),(0,a.jsxs)("div",{children:["inventory"===d&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by title, author, ISBN, or item ID",value:c,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),(0,a.jsxs)("select",{value:u,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Categories"}),(0,a.jsx)("option",{value:"Mathematics",children:"Mathematics"}),(0,a.jsx)("option",{value:"Physics",children:"Physics"}),(0,a.jsx)("option",{value:"Chemistry",children:"Chemistry"}),(0,a.jsx)("option",{value:"Literature",children:"Literature"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Condition"}),(0,a.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Conditions"}),(0,a.jsx)("option",{value:"excellent",children:"Excellent"}),(0,a.jsx)("option",{value:"good",children:"Good"}),(0,a.jsx)("option",{value:"fair",children:"Fair"}),(0,a.jsx)("option",{value:"poor",children:"Poor"}),(0,a.jsx)("option",{value:"damaged",children:"Damaged"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Location"}),(0,a.jsxs)("select",{value:j,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Locations"}),(0,a.jsx)("option",{value:"Main Library",children:"Main Library"}),(0,a.jsx)("option",{value:"Science Section",children:"Science Section"}),(0,a.jsx)("option",{value:"Periodicals Section",children:"Periodicals Section"}),(0,a.jsx)("option",{value:"Digital Library",children:"Digital Library"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)("button",{onClick:()=>null==n?void 0:n("inventory"),className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Report"})})]})})}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsx)(i.ll,{children:"Inventory Items"}),(0,a.jsxs)(i.SZ,{children:["Showing ",N.length," of ",b.length," items"]})]}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full table-auto",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Item"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Location"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Copies"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Condition"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Value"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Last Audit"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:N.map(e=>(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.author}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.itemId," • ",e.isbn]})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.location}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.shelfNumber})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{children:["Total: ",(0,a.jsx)("span",{className:"font-medium",children:e.totalCopies})]}),(0,a.jsxs)("div",{children:["Available: ",(0,a.jsx)("span",{className:"text-green-600",children:e.availableCopies})]}),(0,a.jsxs)("div",{children:["Checked Out: ",(0,a.jsx)("span",{className:"text-blue-600",children:e.checkedOutCopies})]}),e.damagedCopies>0&&(0,a.jsxs)("div",{children:["Damaged: ",(0,a.jsx)("span",{className:"text-red-600",children:e.damagedCopies})]}),e.lostCopies>0&&(0,a.jsxs)("div",{children:["Lost: ",(0,a.jsx)("span",{className:"text-orange-600",children:e.lostCopies})]})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(f(e.condition)),children:e.condition.toUpperCase()})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:["$",(e.cost*e.totalCopies).toFixed(2)]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["$",e.cost.toFixed(2)," each"]})]})}),(0,a.jsx)("td",{className:"py-3 px-4 text-sm",children:e.lastAuditDate}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>null==r?void 0:r(e.id,{}),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"Update"}),(0,a.jsx)("button",{className:"text-green-600 hover:text-green-800 text-sm font-medium",children:"Audit"})]})})]},e.id))})]})})})]})]}),"audit"===d&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Audit History"}),(0,a.jsx)("button",{onClick:s,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Start New Audit"})]}),(0,a.jsx)("div",{className:"space-y-4",children:y.map(e=>(0,a.jsx)(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-semibold text-lg",children:["Audit - ",e.auditDate]}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Conducted by: ",e.auditedBy]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-sm",children:[(0,a.jsxs)("span",{children:["Items Audited: ",(0,a.jsx)("span",{className:"font-medium",children:e.itemsAudited})]}),(0,a.jsxs)("span",{children:["Discrepancies: ",(0,a.jsx)("span",{className:"font-medium text-red-600",children:e.discrepanciesFound})]})]})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(v(e.status)),children:e.status.replace("_"," ").toUpperCase()})})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.notes})})]})},e.id))})]})]})]})}function n(){return(0,a.jsx)(r,{tenantId:"demo-tenant-uuid-1234567890",onStartAudit:()=>{console.log("Start new inventory audit"),alert("Starting new inventory audit...")},onUpdateInventory:(e,t)=>{console.log("Update inventory item:",{itemId:e,updates:t}),alert("Inventory item ".concat(e," updated successfully"))},onGenerateReport:e=>{console.log("Generate inventory report:",e),alert("Generating ".concat(e," report..."))}})}},9197:function(e,t,s){"use strict";s.d(t,{Zb:function(){return d},aY:function(){return u},SZ:function(){return x},Ol:function(){return o},ll:function(){return c}});var a=s(7437),l=s(2265),i=s(7042),r=s(4769);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.m6)((0,i.W)(t))}let d=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:n("rounded-lg border bg-card text-card-foreground shadow-sm",s),...l})});d.displayName="Card";let o=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:n("flex flex-col space-y-1.5 p-6",s),...l})});o.displayName="CardHeader";let c=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("h3",{ref:t,className:n("text-2xl font-semibold leading-none tracking-tight",s),...l})});c.displayName="CardTitle";let x=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("p",{ref:t,className:n("text-sm text-muted-foreground",s),...l})});x.displayName="CardDescription";let u=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:n("p-6 pt-0",s),...l})});u.displayName="CardContent",l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:n("flex items-center p-6 pt-0",s),...l})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=3476)}),_N_E=e.O()}]);