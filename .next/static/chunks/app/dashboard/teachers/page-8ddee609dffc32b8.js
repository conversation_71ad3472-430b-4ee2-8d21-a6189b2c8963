(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4367],{9481:function(e,s,t){Promise.resolve().then(t.bind(t,4236))},4236:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return c}});var a=t(7437);t(2265);var r=t(9197),l=t(1396),n=t.n(l);function i(e){let{tenantId:s}=e,t={totalTeachers:85,activeTeachers:82,onLeave:3,newHires:5,avgExperience:8.5,certificationsDue:12};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Teacher Management"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Comprehensive teacher profiling and performance management"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90",children:"Generate Report"}),(0,a.jsx)("button",{className:"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90",children:"Add Teacher"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-6",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Total Teachers"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC68‍\uD83C\uDFEB"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.totalTeachers}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Faculty members"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Active"}),(0,a.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.activeTeachers}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently teaching"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"On Leave"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFD6️"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.onLeave}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Temporary absence"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"New Hires"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDD95"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.newHires}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"This semester"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Avg Experience"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC8"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.avgExperience," yrs"]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Teaching experience"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Cert. Due"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDC"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.certificationsDue}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Renewal needed"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsxs)(r.Zb,{className:"lg:col-span-2",children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"Recent Activity"}),(0,a.jsx)(r.SZ,{children:"Latest teacher management activities"})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{type:"hire",teacher:"Dr. Sarah Wilson",department:"Mathematics",date:"2024-01-15"},{type:"certification",teacher:"Prof. John Smith",certification:"Advanced Physics",date:"2024-01-14"},{type:"evaluation",teacher:"Ms. Emily Davis",rating:"Excellent",date:"2024-01-13"},{type:"training",teacher:"Mr. Michael Brown",program:"Digital Teaching",date:"2024-01-12"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-sm",children:["hire"===e.type&&"\uD83D\uDC4B","certification"===e.type&&"\uD83D\uDCDC","evaluation"===e.type&&"⭐","training"===e.type&&"\uD83D\uDCDA"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.teacher}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.department||e.certification||e.rating||e.program})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm font-medium capitalize",children:e.type}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.date})]})]},s))})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"Top Performers"}),(0,a.jsx)(r.SZ,{children:"Highest rated teachers"})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{name:"Dr. Sarah Wilson",department:"Mathematics",rating:4.9,students:120},{name:"Prof. John Smith",department:"Physics",rating:4.8,students:95},{name:"Ms. Emily Davis",department:"English",rating:4.7,students:110},{name:"Mr. Robert Johnson",department:"Chemistry",rating:4.6,students:88}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.department}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.students," students"]})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e.rating}),(0,a.jsx)("span",{className:"text-yellow-500",children:"⭐"})]})})]},s))})})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"Upcoming Evaluations"}),(0,a.jsx)(r.SZ,{children:"Scheduled teacher performance reviews"})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full table-auto",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Teacher"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Department"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Type"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Due Date"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:[{teacher:"Ms. Lisa Garcia",department:"Biology",dueDate:"2024-01-20",type:"Annual Review"},{teacher:"Mr. David Miller",department:"History",dueDate:"2024-01-22",type:"Mid-Year Review"},{teacher:"Dr. Maria Rodriguez",department:"Chemistry",dueDate:"2024-01-25",type:"Probation Review"},{teacher:"Prof. James Wilson",department:"Mathematics",dueDate:"2024-01-28",type:"Annual Review"}].map((e,s)=>(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"font-medium text-gray-900",children:e.teacher})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"text-gray-900",children:e.department})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"text-gray-900",children:e.type})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"text-gray-900",children:e.dueDate})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"text-primary hover:text-primary/80 text-sm font-medium",children:"Schedule"}),(0,a.jsx)("button",{className:"text-gray-600 hover:text-gray-800 text-sm font-medium",children:"View Profile"})]})})]},s))})]})})})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(r.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(r.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC65"}),(0,a.jsx)(r.ll,{className:"text-lg",children:"Teacher Profiles"})]})}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage teacher information"}),(0,a.jsx)("button",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Profiles →"})]})]}),(0,a.jsxs)(r.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(r.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"⭐"}),(0,a.jsx)(r.ll,{className:"text-lg",children:"Evaluations"})]})}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Performance evaluations"}),(0,a.jsx)("button",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Evaluations →"})]})]}),(0,a.jsxs)(r.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(r.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDA"}),(0,a.jsx)(r.ll,{className:"text-lg",children:"Training"})]})}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Professional development"}),(0,a.jsx)("button",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Training →"})]})]}),(0,a.jsxs)(r.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(r.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,a.jsx)(r.ll,{className:"text-lg",children:"Reports"})]})}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Teacher analytics and reports"}),(0,a.jsx)("button",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Reports →"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsx)(n(),{href:"/dashboard/teachers/profiles",children:(0,a.jsxs)(r.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(r.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC68‍\uD83C\uDFEB"}),(0,a.jsx)(r.ll,{className:"text-lg",children:"Teacher Profiles"})]})}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Faculty information & credentials"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Profiles →"})]})]})}),(0,a.jsx)(n(),{href:"/dashboard/teachers/evaluations",children:(0,a.jsxs)(r.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(r.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,a.jsx)(r.ll,{className:"text-lg",children:"Performance"})]})}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Teacher assessments & reviews"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Evaluations →"})]})]})}),(0,a.jsx)(n(),{href:"/dashboard/teachers/training",children:(0,a.jsxs)(r.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(r.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"}),(0,a.jsx)(r.ll,{className:"text-lg",children:"Training"})]})}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Professional development"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Programs →"})]})]})}),(0,a.jsx)(n(),{href:"/dashboard/teachers/schedules",children:(0,a.jsxs)(r.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(r.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC5"}),(0,a.jsx)(r.ll,{className:"text-lg",children:"Schedules"})]})}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Teaching assignments & timetables"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Schedules →"})]})]})})]})]})}function c(){return(0,a.jsx)(i,{tenantId:"demo-tenant-uuid-1234567890"})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return c},aY:function(){return h},SZ:function(){return m},Ol:function(){return d},ll:function(){return x}});var a=t(7437),r=t(2265),l=t(7042),n=t(4769);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,l.W)(s))}let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});c.displayName="Card";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",t),...r})});d.displayName="CardHeader";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",t),...r})});x.displayName="CardTitle";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",t),...r})});m.displayName="CardDescription";let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:i("p-6 pt-0",t),...r})});h.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},1396:function(e,s,t){e.exports=t(5250)}},function(e){e.O(0,[7895,5250,2971,4938,1744],function(){return e(e.s=9481)}),_N_E=e.O()}]);