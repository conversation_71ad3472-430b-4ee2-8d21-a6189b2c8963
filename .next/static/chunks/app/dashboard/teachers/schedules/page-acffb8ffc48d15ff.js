(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1851],{1532:function(e,s,a){Promise.resolve().then(a.bind(a,4409))},4409:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return d}});var t=a(7437),r=a(2265);function i(e){let{tenantId:s,onUpdateSchedule:a,onApproveRequest:i,onRejectRequest:d,onCreateSubstitution:l}=e,[n,c]=(0,r.useState)("schedules"),[o,u]=(0,r.useState)(""),[m,h]=(0,r.useState)("all"),[x,p]=(0,r.useState)("monday"),[y,b]=(0,r.useState)("all"),[g,j]=(0,r.useState)(null),f=e=>{switch(e){case"teaching":return"bg-blue-100 text-blue-800 border-blue-200";case"supervision":return"bg-green-100 text-green-800 border-green-200";case"meeting":return"bg-purple-100 text-purple-800 border-purple-200";case"free":default:return"bg-gray-100 text-gray-800 border-gray-200";case"break":return"bg-yellow-100 text-yellow-800 border-yellow-200"}},v=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"completed":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},N=e=>{switch(e){case"high":return"bg-red-100 text-red-800";case"medium":return"bg-yellow-100 text-yellow-800";case"low":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},S=[{id:"1",teacherId:"TCH-2024-001",teacherName:"Dr. Priya Sharma",department:"Mathematics",academicYear:"2023-24",semester:"Spring",totalWorkingHours:40,teachingLoad:24,nonTeachingLoad:16,weeklySchedule:{monday:[{timeSlot:"09:00-10:00",subject:"Algebra",class:"Grade 10A",room:"Room 201",type:"teaching",duration:60},{timeSlot:"10:00-11:00",subject:"Calculus",class:"Grade 12B",room:"Room 201",type:"teaching",duration:60},{timeSlot:"11:00-11:15",subject:"Break",class:"",room:"Staff Room",type:"break",duration:15},{timeSlot:"11:15-12:15",subject:"Statistics",class:"Grade 11A",room:"Room 201",type:"teaching",duration:60},{timeSlot:"12:15-13:15",subject:"Free Period",class:"",room:"Staff Room",type:"free",duration:60},{timeSlot:"13:15-14:15",subject:"Lunch Break",class:"",room:"Cafeteria",type:"break",duration:60},{timeSlot:"14:15-15:15",subject:"Department Meeting",class:"",room:"Conference Room",type:"meeting",duration:60}],tuesday:[{timeSlot:"09:00-10:00",subject:"Geometry",class:"Grade 9A",room:"Room 201",type:"teaching",duration:60},{timeSlot:"10:00-11:00",subject:"Algebra",class:"Grade 10B",room:"Room 201",type:"teaching",duration:60}]},substitutions:[{date:"2024-02-15",originalTeacher:"Prof. Rajesh Kumar",subject:"Physics",class:"Grade 11B",room:"Lab 1",reason:"Medical leave",status:"completed"}],extraDuties:[{duty:"Exam Supervision",description:"Supervise final examinations",schedule:"March 15-25, 2024",location:"Exam Hall A",duration:3},{duty:"Student Counseling",description:"Academic counseling for struggling students",schedule:"Every Friday 3:00-4:00 PM",location:"Counseling Room",duration:1}],availability:{preferredSlots:["09:00-12:00","14:00-16:00"],unavailableSlots:["07:00-09:00","17:00-18:00"],maxHoursPerDay:8,workingDays:["Monday","Tuesday","Wednesday","Thursday","Friday"]},conflicts:[],lastUpdated:"2024-02-01"},{id:"2",teacherId:"TCH-2024-002",teacherName:"Prof. Rajesh Kumar",department:"Science",academicYear:"2023-24",semester:"Spring",totalWorkingHours:42,teachingLoad:20,nonTeachingLoad:22,weeklySchedule:{monday:[{timeSlot:"09:00-10:00",subject:"Physics",class:"Grade 11A",room:"Lab 1",type:"teaching",duration:60},{timeSlot:"10:00-11:00",subject:"Physics",class:"Grade 12A",room:"Lab 1",type:"teaching",duration:60}],tuesday:[{timeSlot:"09:00-10:00",subject:"General Science",class:"Grade 8A",room:"Room 301",type:"teaching",duration:60}]},substitutions:[],extraDuties:[{duty:"Science Fair Coordinator",description:"Organize annual science fair",schedule:"April 1-15, 2024",location:"Science Block",duration:4}],availability:{preferredSlots:["09:00-13:00"],unavailableSlots:["16:00-18:00"],maxHoursPerDay:8,workingDays:["Monday","Tuesday","Wednesday","Thursday","Friday"]},conflicts:[{type:"overload",description:"Exceeding maximum teaching hours per week",severity:"medium",resolution:"Redistribute some classes to other teachers"}],lastUpdated:"2024-02-05"}].filter(e=>{let s=e.teacherName.toLowerCase().includes(o.toLowerCase())||e.teacherId.toLowerCase().includes(o.toLowerCase()),a="all"===m||e.department===m,t="all"===y||e.teacherId===y;return s&&a&&t});return[{id:"1",requestId:"REQ-2024-001",requestType:"substitution",requestedBy:"Prof. Rajesh Kumar",teacherId:"TCH-2024-001",teacherName:"Dr. Priya Sharma",details:{date:"2024-02-20",timeSlot:"10:00-11:00",subject:"Physics",class:"Grade 11B",room:"Lab 1",reason:"Medical appointment",urgency:"medium"},status:"pending",submittedDate:"2024-02-15"},{id:"2",requestId:"REQ-2024-002",requestType:"schedule_change",requestedBy:"Dr. Priya Sharma",teacherId:"TCH-2024-001",teacherName:"Dr. Priya Sharma",details:{reason:"Conflict with parent-teacher meeting",urgency:"low"},status:"approved",approvedBy:"Dr. Meera Patel (Principal)",approvalDate:"2024-02-10",submittedDate:"2024-02-08"}].filter(e=>e.teacherName.toLowerCase().includes(o.toLowerCase())||e.requestId.toLowerCase().includes(o.toLowerCase())||e.requestedBy.toLowerCase().includes(o.toLowerCase())),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Schedule Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage teacher schedules, substitutions, and workload"})]}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Create Schedule"})]}),(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{id:"schedules",name:"Schedules",icon:"\uD83D\uDCC5"},{id:"requests",name:"Requests",icon:"\uD83D\uDCDD"},{id:"conflicts",name:"Conflicts",icon:"⚠️"},{id:"workload",name:"Workload",icon:"\uD83D\uDCCA"}].map(e=>(0,t.jsxs)("button",{onClick:()=>c(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(n===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,t.jsx)("span",{className:"mr-2",children:e.icon}),e.name]},e.id))})}),(0,t.jsx)("div",{children:"schedules"===n&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,t.jsx)("div",{className:"flex-1 min-w-64",children:(0,t.jsx)("input",{type:"text",placeholder:"Search teachers...",value:o,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})}),(0,t.jsxs)("select",{value:m,onChange:e=>h(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Departments"}),(0,t.jsx)("option",{value:"mathematics",children:"Mathematics"}),(0,t.jsx)("option",{value:"science",children:"Science"}),(0,t.jsx)("option",{value:"english",children:"English"}),(0,t.jsx)("option",{value:"social-studies",children:"Social Studies"})]}),(0,t.jsxs)("select",{value:x,onChange:e=>p(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"monday",children:"Monday"}),(0,t.jsx)("option",{value:"tuesday",children:"Tuesday"}),(0,t.jsx)("option",{value:"wednesday",children:"Wednesday"}),(0,t.jsx)("option",{value:"thursday",children:"Thursday"}),(0,t.jsx)("option",{value:"friday",children:"Friday"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:S.map(e=>(0,t.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer",onClick:()=>j(e),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:e.teacherName}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:e.department})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"font-medium",children:"Teaching Load:"})," ",e.teachingLoad,"h/week"]}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"font-medium",children:"Total Hours:"})," ",e.totalWorkingHours,"h/week"]}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"font-medium",children:"Extra Duties:"})," ",e.extraDuties.length]})]})]},e.id))})]})}),g&&(0,t.jsx)(()=>g?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold",children:["Schedule - ",g.teacherName]}),(0,t.jsx)("button",{onClick:()=>j(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Teacher Information"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Teacher ID:"})," ",g.teacherId]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Department:"})," ",g.department]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Academic Year:"})," ",g.academicYear]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Semester:"})," ",g.semester]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Workload Summary"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Total Hours:"})," ",g.totalWorkingHours,"/week"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Teaching Load:"})," ",g.teachingLoad," hours"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Non-Teaching:"})," ",g.nonTeachingLoad," hours"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Max Hours/Day:"})," ",g.availability.maxHoursPerDay]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Schedule Status"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Conflicts:"})," ",g.conflicts.length]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Substitutions:"})," ",g.substitutions.length]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Extra Duties:"})," ",g.extraDuties.length]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Last Updated:"})," ",g.lastUpdated]})]})]})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Weekly Schedule"}),(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full border-collapse border border-gray-300",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"bg-gray-50",children:[(0,t.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Time"}),(0,t.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Monday"}),(0,t.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Tuesday"}),(0,t.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Wednesday"}),(0,t.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Thursday"}),(0,t.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Friday"})]})}),(0,t.jsx)("tbody",{children:["09:00-10:00","10:00-11:00","11:00-11:15","11:15-12:15","12:15-13:15","13:15-14:15","14:15-15:15"].map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"border border-gray-300 px-3 py-2 font-medium text-sm",children:e}),["monday","tuesday","wednesday","thursday","friday"].map(s=>{var a;let r=null===(a=g.weeklySchedule[s])||void 0===a?void 0:a.find(s=>s.timeSlot===e);return(0,t.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:r?(0,t.jsxs)("div",{className:"p-2 rounded border text-xs ".concat(f(r.type)),children:[(0,t.jsx)("div",{className:"font-medium",children:r.subject}),r.class&&(0,t.jsx)("div",{children:r.class}),r.room&&(0,t.jsx)("div",{children:r.room})]}):(0,t.jsx)("div",{className:"text-gray-400 text-xs",children:"Free"})},s)})]},e))})]})})]}),g.conflicts.length>0&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Schedule Conflicts"}),(0,t.jsx)("div",{className:"space-y-2",children:g.conflicts.map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("span",{className:"font-medium text-sm",children:e.type.replace("_"," ").toUpperCase()}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(N(e.severity)),children:e.severity.toUpperCase()})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,t.jsxs)("p",{className:"text-sm text-blue-600",children:["Resolution: ",e.resolution]})]},s))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Extra Duties"}),(0,t.jsx)("div",{className:"space-y-2",children:g.extraDuties.map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-2",children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:e.duty}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:e.description}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[e.schedule," • ",e.location," • ",e.duration,"h"]})]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Recent Substitutions"}),(0,t.jsx)("div",{className:"space-y-2",children:g.substitutions.map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-2",children:[(0,t.jsxs)("div",{className:"font-medium text-sm",children:[e.subject," - ",e.class]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["For: ",e.originalTeacher]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[e.date," • ",e.room," • ",e.reason]}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(v(e.status)),children:e.status.toUpperCase()})]},s))})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{onClick:()=>null==a?void 0:a(g.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Edit Schedule"}),(0,t.jsx)("button",{onClick:()=>null==l?void 0:l(g.teacherId),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Create Substitution"}),(0,t.jsx)("button",{className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700",children:"Generate Timetable"})]})]})}):null,{})]})}function d(){return(0,t.jsx)(i,{tenantId:"demo-tenant-uuid-1234567890",onUpdateSchedule:e=>{console.log("Update schedule:",e)},onApproveRequest:e=>{console.log("Approve schedule request:",e),alert("Schedule request ".concat(e," approved"))},onRejectRequest:(e,s)=>{console.log("Reject schedule request:",{requestId:e,reason:s}),alert("Schedule request ".concat(e," rejected: ").concat(s))},onCreateSubstitution:e=>{console.log("Create substitution for teacher:",e),alert("Substitution request created for teacher ".concat(e))}})}},622:function(e,s,a){"use strict";var t=a(2265),r=Symbol.for("react.element"),i=Symbol.for("react.fragment"),d=Object.prototype.hasOwnProperty,l=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,n={key:!0,ref:!0,__self:!0,__source:!0};function c(e,s,a){var t,i={},c=null,o=null;for(t in void 0!==a&&(c=""+a),void 0!==s.key&&(c=""+s.key),void 0!==s.ref&&(o=s.ref),s)d.call(s,t)&&!n.hasOwnProperty(t)&&(i[t]=s[t]);if(e&&e.defaultProps)for(t in s=e.defaultProps)void 0===i[t]&&(i[t]=s[t]);return{$$typeof:r,type:e,key:c,ref:o,props:i,_owner:l.current}}s.Fragment=i,s.jsx=c,s.jsxs=c},7437:function(e,s,a){"use strict";e.exports=a(622)}},function(e){e.O(0,[2971,4938,1744],function(){return e(e.s=1532)}),_N_E=e.O()}]);