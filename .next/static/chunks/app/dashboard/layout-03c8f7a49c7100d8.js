(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7663],{2875:function(e,a,s){Promise.resolve().then(s.bind(s,7108))},7108:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return c}});var r=s(7437),n=s(1396),i=s.n(n),t=s(4033),o=s(2265);function c(e){let{children:a}=e,s=(0,t.usePathname)(),[n,c]=(0,o.useState)(null);(0,o.useEffect)(()=>{let e=l.find(e=>s.startsWith(e.href)&&"/dashboard"!==e.href);e&&c(e.name)},[s]);let d=(e,a,s,r)=>{a&&(r.preventDefault(),c(n===e?null:e))},l=[{name:"Dashboard",href:"/dashboard",icon:"\uD83C\uDFE0"},{name:"Student Admissions",href:"/dashboard/admissions",icon:"\uD83D\uDC65",subItems:[{name:"Applications",href:"/dashboard/admissions/applications",icon:"\uD83D\uDCCB"},{name:"Interviews",href:"/dashboard/admissions/interviews",icon:"\uD83C\uDFAF"},{name:"Decisions",href:"/dashboard/admissions/decisions",icon:"✅"},{name:"Class Allocation",href:"/dashboard/admissions/classes",icon:"\uD83C\uDFEB"},{name:"Enrollment",href:"/dashboard/admissions/enrollment",icon:"\uD83C\uDF93"},{name:"Reports",href:"/dashboard/admissions/reports",icon:"\uD83D\uDCCA"}]},{name:"Academic Management",href:"/dashboard/academic",icon:"\uD83D\uDCDA",subItems:[{name:"Curriculum",href:"/dashboard/academic/curriculum",icon:"\uD83D\uDCDA"},{name:"Schedule",href:"/dashboard/academic/schedule",icon:"\uD83D\uDCC5"},{name:"Assessments",href:"/dashboard/academic/assessments",icon:"\uD83D\uDCDD"},{name:"Grades",href:"/dashboard/academic/grades",icon:"\uD83D\uDCCA"},{name:"Attendance",href:"/dashboard/academic/attendance",icon:"✅"},{name:"Progress Reports",href:"/dashboard/academic/progress",icon:"\uD83D\uDCC8"}]},{name:"Student Financials",href:"/dashboard/financials",icon:"\uD83D\uDCB0",subItems:[{name:"Fee Structure",href:"/dashboard/financials/fee-structure",icon:"\uD83D\uDCB0"},{name:"Billing",href:"/dashboard/financials/billing",icon:"\uD83D\uDCC4"},{name:"Payments",href:"/dashboard/financials/payments",icon:"\uD83D\uDCB3"},{name:"Financial Aid",href:"/dashboard/financials/financial-aid",icon:"\uD83C\uDF93"},{name:"Reports",href:"/dashboard/financials/reports",icon:"\uD83D\uDCCA"},{name:"Payment History",href:"/dashboard/financials/history",icon:"\uD83D\uDCCB"}]},{name:"Library Management",href:"/dashboard/library",icon:"\uD83D\uDCD6",subItems:[{name:"Catalog",href:"/dashboard/library/catalog",icon:"\uD83D\uDCDA"},{name:"Circulation",href:"/dashboard/library/circulation",icon:"\uD83D\uDD04"},{name:"Members",href:"/dashboard/library/members",icon:"\uD83D\uDC65"},{name:"Reservations",href:"/dashboard/library/reservations",icon:"\uD83D\uDCCB"},{name:"Inventory",href:"/dashboard/library/inventory",icon:"\uD83D\uDCE6"},{name:"Digital Library",href:"/dashboard/library/digital",icon:"\uD83D\uDCBB"}]},{name:"Alumni Engagement",href:"/dashboard/alumni",icon:"\uD83C\uDF93",subItems:[{name:"Directory",href:"/dashboard/alumni/directory",icon:"\uD83D\uDC65"},{name:"Events",href:"/dashboard/alumni/events",icon:"\uD83C\uDF89"},{name:"Job Board",href:"/dashboard/alumni/jobs",icon:"\uD83D\uDCBC"},{name:"Donations",href:"/dashboard/alumni/donations",icon:"\uD83D\uDCB0"},{name:"Networking",href:"/dashboard/alumni/networking",icon:"\uD83E\uDD1D"},{name:"Achievements",href:"/dashboard/alumni/achievements",icon:"\uD83C\uDFC6"}]},{name:"Hostel Management",href:"/dashboard/hostel",icon:"\uD83C\uDFE0",subItems:[{name:"Room Allocation",href:"/dashboard/hostel/rooms",icon:"\uD83C\uDFE0"},{name:"Residents",href:"/dashboard/hostel/residents",icon:"\uD83D\uDC65"},{name:"Maintenance",href:"/dashboard/hostel/maintenance",icon:"\uD83D\uDD27"},{name:"Visitors",href:"/dashboard/hostel/visitors",icon:"\uD83D\uDEAA"},{name:"Facilities",href:"/dashboard/hostel/facilities",icon:"\uD83C\uDFE2"}]},{name:"Teacher Management",href:"/dashboard/teachers",icon:"\uD83D\uDC68‍\uD83C\uDFEB",subItems:[{name:"Teacher Profiles",href:"/dashboard/teachers/profiles",icon:"\uD83D\uDC68‍\uD83C\uDFEB"},{name:"Performance Evaluations",href:"/dashboard/teachers/evaluations",icon:"\uD83D\uDCCA"},{name:"Training Programs",href:"/dashboard/teachers/training",icon:"\uD83C\uDF93"},{name:"Schedule Management",href:"/dashboard/teachers/schedules",icon:"\uD83D\uDCC5"}]},{name:"Transport Management",href:"/dashboard/transport",icon:"\uD83D\uDE8C",subItems:[{name:"Vehicle Fleet",href:"/dashboard/transport/vehicles",icon:"\uD83D\uDE8C"},{name:"Route Planning",href:"/dashboard/transport/routes",icon:"\uD83D\uDDFA️"},{name:"Driver Management",href:"/dashboard/transport/drivers",icon:"\uD83D\uDC68‍✈️"},{name:"Student Transport",href:"/dashboard/transport/students",icon:"\uD83C\uDF92"},{name:"Safety & Tracking",href:"/dashboard/transport/safety",icon:"\uD83D\uDEE1️"}]}];return(0,r.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[(0,r.jsxs)("div",{className:"w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col",children:[(0,r.jsx)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"EMS"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"font-semibold text-gray-900",children:"EMS"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Demo School"})]})]})}),(0,r.jsx)("nav",{className:"flex-1 px-2 py-4 space-y-1",children:l.map(e=>{let a=s===e.href||e.subItems&&e.subItems.some(e=>s===e.href),t=n===e.name;return(0,r.jsxs)("div",{children:[e.subItems?(0,r.jsxs)("button",{onClick:a=>d(e.name,!0,e.href,a),className:"group flex items-center justify-between w-full px-2 py-2 text-sm font-medium rounded-md transition-colors ".concat(a?"bg-blue-600 text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"),children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"mr-3 text-lg",children:e.icon}),(0,r.jsx)("span",{children:e.name})]}),(0,r.jsx)("span",{className:"transition-transform ".concat(t?"rotate-90":""),children:"▶"})]}):(0,r.jsxs)(i(),{href:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ".concat(a?"bg-blue-600 text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"),children:[(0,r.jsx)("span",{className:"mr-3 text-lg",children:e.icon}),(0,r.jsx)("span",{children:e.name})]}),e.subItems&&t&&(0,r.jsx)("div",{className:"ml-6 mt-1 space-y-1",children:e.subItems.map(e=>{let a=s===e.href;return(0,r.jsxs)(i(),{href:e.href,className:"group flex items-center px-2 py-1 text-sm rounded-md transition-colors ".concat(a?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"),children:[(0,r.jsx)("span",{className:"mr-2 text-sm",children:e.icon}),(0,r.jsx)("span",{children:e.name})]},e.name)})})]},e.name)})}),(0,r.jsx)("div",{className:"border-t border-gray-200 p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"DU"})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Demo User"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Admin"})]})]})})]}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsx)("div",{className:"flex-1 max-w-lg",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)("span",{className:"text-gray-400",children:"\uD83D\uDD0D"})}),(0,r.jsx)("input",{type:"text",placeholder:"Search...",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("button",{className:"p-2 text-gray-400 hover:text-gray-500",children:(0,r.jsx)("span",{className:"text-xl",children:"\uD83D\uDD14"})}),(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"DU"})})]})]})}),(0,r.jsx)("main",{className:"flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6",children:a})]})]})}},622:function(e,a,s){"use strict";var r=s(2265),n=Symbol.for("react.element"),i=Symbol.for("react.fragment"),t=Object.prototype.hasOwnProperty,o=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function d(e,a,s){var r,i={},d=null,l=null;for(r in void 0!==s&&(d=""+s),void 0!==a.key&&(d=""+a.key),void 0!==a.ref&&(l=a.ref),a)t.call(a,r)&&!c.hasOwnProperty(r)&&(i[r]=a[r]);if(e&&e.defaultProps)for(r in a=e.defaultProps)void 0===i[r]&&(i[r]=a[r]);return{$$typeof:n,type:e,key:d,ref:l,props:i,_owner:o.current}}a.Fragment=i,a.jsx=d,a.jsxs=d},7437:function(e,a,s){"use strict";e.exports=s(622)},1396:function(e,a,s){e.exports=s(5250)},4033:function(e,a,s){e.exports=s(5313)}},function(e){e.O(0,[5250,2971,4938,1744],function(){return e(e.s=2875)}),_N_E=e.O()}]);