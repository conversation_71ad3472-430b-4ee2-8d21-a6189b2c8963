(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6657],{8611:function(e,s,l){Promise.resolve().then(l.bind(l,7468))},7468:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return r}});var a=l(7437);l(2265);var n=l(9197);function r(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Alumni Directory"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Browse and search alumni profiles and contact information"})]}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Add Alumni"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Alumni"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC65"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"5,234"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"All time"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Active Profiles"}),(0,a.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"3,456"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Updated profiles"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Recent Graduates"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"234"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Class of 2024"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Countries"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF0D"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"45"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Global presence"})]})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Search Alumni"}),(0,a.jsx)(n.SZ,{children:"Find alumni by name, batch, profession, or location"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Search Name"}),(0,a.jsx)("input",{type:"text",placeholder:"Enter name...",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Graduation Year"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"",children:"All Years"}),(0,a.jsx)("option",{value:"2024",children:"2024"}),(0,a.jsx)("option",{value:"2023",children:"2023"}),(0,a.jsx)("option",{value:"2022",children:"2022"}),(0,a.jsx)("option",{value:"2021",children:"2021"}),(0,a.jsx)("option",{value:"2020",children:"2020"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Industry"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"",children:"All Industries"}),(0,a.jsx)("option",{value:"technology",children:"Technology"}),(0,a.jsx)("option",{value:"finance",children:"Finance"}),(0,a.jsx)("option",{value:"healthcare",children:"Healthcare"}),(0,a.jsx)("option",{value:"education",children:"Education"}),(0,a.jsx)("option",{value:"consulting",children:"Consulting"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Location"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"",children:"All Locations"}),(0,a.jsx)("option",{value:"india",children:"India"}),(0,a.jsx)("option",{value:"usa",children:"United States"}),(0,a.jsx)("option",{value:"uk",children:"United Kingdom"}),(0,a.jsx)("option",{value:"canada",children:"Canada"}),(0,a.jsx)("option",{value:"australia",children:"Australia"})]})]})]}),(0,a.jsxs)("div",{className:"mt-4 flex space-x-2",children:[(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Search"}),(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Clear Filters"})]})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Alumni Profiles"}),(0,a.jsx)(n.SZ,{children:"Browse alumni directory"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"space-y-4",children:[{name:"Rajesh Kumar",batch:"2018",degree:"B.Tech Computer Science",company:"Google",position:"Senior Software Engineer",location:"Bangalore, India",email:"<EMAIL>",linkedin:"linkedin.com/in/rajeshkumar",image:"\uD83D\uDC68‍\uD83D\uDCBB"},{name:"Priya Sharma",batch:"2019",degree:"MBA Finance",company:"Goldman Sachs",position:"Investment Analyst",location:"Mumbai, India",email:"<EMAIL>",linkedin:"linkedin.com/in/priyasharma",image:"\uD83D\uDC69‍\uD83D\uDCBC"},{name:"Amit Patel",batch:"2020",degree:"M.Tech AI/ML",company:"Microsoft",position:"Data Scientist",location:"Seattle, USA",email:"<EMAIL>",linkedin:"linkedin.com/in/amitpatel",image:"\uD83D\uDC68‍\uD83D\uDD2C"},{name:"Sneha Gupta",batch:"2021",degree:"B.Tech Electronics",company:"Tesla",position:"Hardware Engineer",location:"California, USA",email:"<EMAIL>",linkedin:"linkedin.com/in/snehagupta",image:"\uD83D\uDC69‍\uD83D\uDD27"}].map((e,s)=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center text-2xl",children:e.image}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.degree," • Class of ",e.batch]}),(0,a.jsx)("p",{className:"text-sm font-medium text-blue-600",children:e.position}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.company," • ",e.location]}),(0,a.jsxs)("div",{className:"mt-2 flex space-x-4 text-sm",children:[(0,a.jsx)("a",{href:"mailto:".concat(e.email),className:"text-blue-600 hover:underline",children:"\uD83D\uDCE7 Email"}),(0,a.jsx)("a",{href:"https://".concat(e.linkedin),target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"\uD83D\uDCBC LinkedIn"})]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm",children:"View Profile"}),(0,a.jsx)("button",{className:"text-green-600 hover:text-green-800 text-sm",children:"Connect"})]})]})},s))}),(0,a.jsxs)("div",{className:"mt-6 flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Showing 1-4 of 5,234 alumni"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50",children:"Previous"}),(0,a.jsx)("button",{className:"px-3 py-1 bg-blue-600 text-white rounded-md",children:"1"}),(0,a.jsx)("button",{className:"px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50",children:"2"}),(0,a.jsx)("button",{className:"px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50",children:"3"}),(0,a.jsx)("button",{className:"px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50",children:"Next"})]})]})]})]})]})}},9197:function(e,s,l){"use strict";l.d(s,{Zb:function(){return d},aY:function(){return m},SZ:function(){return x},Ol:function(){return c},ll:function(){return o}});var a=l(7437),n=l(2265),r=l(7042),t=l(4769);function i(){for(var e=arguments.length,s=Array(e),l=0;l<e;l++)s[l]=arguments[l];return(0,t.m6)((0,r.W)(s))}let d=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,a.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",l),...n})});d.displayName="Card";let c=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,a.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",l),...n})});c.displayName="CardHeader";let o=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,a.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",l),...n})});o.displayName="CardTitle";let x=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,a.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",l),...n})});x.displayName="CardDescription";let m=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,a.jsx)("div",{ref:s,className:i("p-6 pt-0",l),...n})});m.displayName="CardContent",n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,a.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",l),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=8611)}),_N_E=e.O()}]);