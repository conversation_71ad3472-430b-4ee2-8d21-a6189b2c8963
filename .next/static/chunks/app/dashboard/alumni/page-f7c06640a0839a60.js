(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9082],{6802:function(e,s,a){Promise.resolve().then(a.bind(a,5946))},5946:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return i}});var r=a(7437);a(2265);var t=a(1396),l=a.n(t),n=a(9197);function i(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Alumni Engagement"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Connect with alumni, manage events, and foster community"})]})}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Alumni"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC65"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"5,234"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Across all batches"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ll,{className:"text-sm font-medium",children:"Active Members"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF89"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"1,456"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Engaged alumni"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ll,{className:"text-sm font-medium",children:"Upcoming Events"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCBC"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"8"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"This quarter"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ll,{className:"text-sm font-medium",children:"Donations"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"₹2.1M"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"This year"})]})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsx)(l(),{href:"/dashboard/alumni/directory",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC65"}),(0,r.jsx)(n.ll,{className:"text-lg",children:"Directory"})]})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Browse alumni directory and profiles"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Browse Directory →"})]})]})}),(0,r.jsx)(l(),{href:"/dashboard/alumni/events",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF89"}),(0,r.jsx)(n.ll,{className:"text-lg",children:"Events"})]})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Organize and manage alumni events"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Events →"})]})]})}),(0,r.jsx)(l(),{href:"/dashboard/alumni/jobs",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCBC"}),(0,r.jsx)(n.ll,{className:"text-lg",children:"Job Board"})]})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Career opportunities and job postings"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Jobs →"})]})]})}),(0,r.jsx)(l(),{href:"/dashboard/alumni/donations",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"}),(0,r.jsx)(n.ll,{className:"text-lg",children:"Donations"})]})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage donations and fundraising"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Donations →"})]})]})}),(0,r.jsx)(l(),{href:"/dashboard/alumni/networking",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83E\uDD1D"}),(0,r.jsx)(n.ll,{className:"text-lg",children:"Networking"})]})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Connect alumni for networking"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Network →"})]})]})}),(0,r.jsx)(l(),{href:"/dashboard/alumni/achievements",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFC6"}),(0,r.jsx)(n.ll,{className:"text-lg",children:"Achievements"})]})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Showcase alumni achievements"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Achievements →"})]})]})})]})]})}},9197:function(e,s,a){"use strict";a.d(s,{Zb:function(){return c},aY:function(){return o},SZ:function(){return m},Ol:function(){return d},ll:function(){return x}});var r=a(7437),t=a(2265),l=a(7042),n=a(4769);function i(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,n.m6)((0,l.W)(s))}let c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",a),...t})});c.displayName="Card";let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",a),...t})});d.displayName="CardHeader";let x=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",a),...t})});x.displayName="CardTitle";let m=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",a),...t})});m.displayName="CardDescription";let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:i("p-6 pt-0",a),...t})});o.displayName="CardContent",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",a),...t})}).displayName="CardFooter"},1396:function(e,s,a){e.exports=a(5250)}},function(e){e.O(0,[7895,5250,2971,4938,1744],function(){return e(e.s=6802)}),_N_E=e.O()}]);