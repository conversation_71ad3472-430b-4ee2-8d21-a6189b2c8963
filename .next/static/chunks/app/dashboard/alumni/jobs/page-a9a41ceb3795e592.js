(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8221],{664:function(e,s,a){Promise.resolve().then(a.bind(a,4254))},4254:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return n}});var t=a(7437);a(2265);var l=a(9197);function n(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Alumni Job Board"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Career opportunities and job postings from alumni network"})]}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Post Job"})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ll,{className:"text-sm font-medium",children:"Active Jobs"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCBC"})]}),(0,t.jsxs)(l.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"45"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently open"})]})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ll,{className:"text-sm font-medium",children:"Applications"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"})]}),(0,t.jsxs)(l.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"234"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ll,{className:"text-sm font-medium",children:"Placements"}),(0,t.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,t.jsxs)(l.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"89"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Successful hires"})]})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ll,{className:"text-sm font-medium",children:"Companies"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFE2"})]}),(0,t.jsxs)(l.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"28"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Hiring partners"})]})]})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{children:[(0,t.jsx)(l.ll,{children:"Latest Job Opportunities"}),(0,t.jsx)(l.SZ,{children:"Recent job postings from alumni and partner companies"})]}),(0,t.jsx)(l.aY,{children:(0,t.jsx)("div",{className:"space-y-4",children:[{title:"Senior Software Engineer",company:"Google",location:"Bangalore, India",type:"Full-time",experience:"3-5 years",salary:"₹25-35 LPA",postedBy:"Rajesh Kumar (Class of 2018)",postedDate:"2 days ago",applications:23,skills:["React","Node.js","Python","AWS"]},{title:"Product Manager",company:"Microsoft",location:"Hyderabad, India",type:"Full-time",experience:"4-6 years",salary:"₹30-45 LPA",postedBy:"Priya Sharma (Class of 2017)",postedDate:"5 days ago",applications:18,skills:["Product Strategy","Analytics","Agile","Leadership"]},{title:"Data Scientist",company:"Amazon",location:"Remote",type:"Full-time",experience:"2-4 years",salary:"₹20-30 LPA",postedBy:"Amit Patel (Class of 2019)",postedDate:"1 week ago",applications:31,skills:["Python","Machine Learning","SQL","Statistics"]},{title:"UX Designer",company:"Flipkart",location:"Bangalore, India",type:"Full-time",experience:"2-3 years",salary:"₹15-25 LPA",postedBy:"Sneha Gupta (Class of 2020)",postedDate:"1 week ago",applications:15,skills:["Figma","User Research","Prototyping","Design Systems"]}].map((e,s)=>(0,t.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:e.title}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e.type})]}),(0,t.jsx)("p",{className:"text-blue-600 font-medium",children:e.company}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCCD Location:"})," ",e.location]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCBC Experience:"})," ",e.experience]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCB0 Salary:"})," ",e.salary]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCDD Applications:"})," ",e.applications]})]})]}),(0,t.jsxs)("div",{className:"mt-3",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:[(0,t.jsx)("span",{className:"font-medium",children:"Posted by:"})," ",e.postedBy," • ",e.postedDate]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.skills.map((e,s)=>(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded",children:e},s))})]})]}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"Apply Now"}),(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"Save Job"}),(0,t.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm",children:"View Details"})]})]})},s))})})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{children:[(0,t.jsx)(l.ll,{children:"Job Categories"}),(0,t.jsx)(l.SZ,{children:"Jobs by industry"})]}),(0,t.jsx)(l.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{name:"Technology",count:18,color:"bg-blue-100 text-blue-800"},{name:"Finance",count:12,color:"bg-green-100 text-green-800"},{name:"Consulting",count:8,color:"bg-purple-100 text-purple-800"},{name:"Healthcare",count:5,color:"bg-red-100 text-red-800"},{name:"Education",count:2,color:"bg-yellow-100 text-yellow-800"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:e.name}),(0,t.jsxs)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(e.color),children:[e.count," jobs"]})]},s))})})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{children:[(0,t.jsx)(l.ll,{children:"Top Hiring Companies"}),(0,t.jsx)(l.SZ,{children:"Companies with most job postings"})]}),(0,t.jsx)(l.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{name:"Google",jobs:8,alumni:12},{name:"Microsoft",jobs:6,alumni:9},{name:"Amazon",jobs:5,alumni:15},{name:"Flipkart",jobs:4,alumni:7},{name:"Zomato",jobs:3,alumni:5}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:e.name}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.alumni," alumni working"]})]}),(0,t.jsxs)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full",children:[e.jobs," jobs"]})]},s))})})]})]})]})}},9197:function(e,s,a){"use strict";a.d(s,{Zb:function(){return c},aY:function(){return m},SZ:function(){return x},Ol:function(){return d},ll:function(){return o}});var t=a(7437),l=a(2265),n=a(7042),r=a(4769);function i(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.m6)((0,n.W)(s))}let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",a),...l})});c.displayName="Card";let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",a),...l})});d.displayName="CardHeader";let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",a),...l})});o.displayName="CardTitle";let x=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",a),...l})});x.displayName="CardDescription";let m=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:i("p-6 pt-0",a),...l})});m.displayName="CardContent",l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",a),...l})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=664)}),_N_E=e.O()}]);