(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9818],{5036:function(e,s,l){Promise.resolve().then(l.bind(l,4211))},4211:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return r}});var t=l(7437),n=l(9197);function r(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Hostel Billing"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage hostel fees, payments, and billing records"})]}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Generate Bill"})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Monthly Revenue"})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"₹8.4L"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"This month"})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Pending Payments"})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:"₹1.2L"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"45 residents"})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Overdue"})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:"₹45K"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"12 residents"})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Collection Rate"})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"87%"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"This month"})]})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsx)(n.ll,{children:"Billing Management"}),(0,t.jsx)(n.SZ,{children:"Hostel billing functionality is under development"})]}),(0,t.jsx)(n.aY,{children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCB0"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Billing Module Coming Soon"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"This section will include fee management, payment tracking, and billing reports."}),(0,t.jsxs)("div",{className:"space-y-2 text-sm text-gray-500",children:[(0,t.jsx)("p",{children:"• Monthly fee collection"}),(0,t.jsx)("p",{children:"• Payment history tracking"}),(0,t.jsx)("p",{children:"• Automated billing generation"}),(0,t.jsx)("p",{children:"• Financial reports and analytics"})]})]})})]})]})}},9197:function(e,s,l){"use strict";l.d(s,{Zb:function(){return d},aY:function(){return m},SZ:function(){return o},Ol:function(){return c},ll:function(){return x}});var t=l(7437),n=l(2265),r=l(7042),a=l(4769);function i(){for(var e=arguments.length,s=Array(e),l=0;l<e;l++)s[l]=arguments[l];return(0,a.m6)((0,r.W)(s))}let d=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,t.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",l),...n})});d.displayName="Card";let c=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,t.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",l),...n})});c.displayName="CardHeader";let x=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,t.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",l),...n})});x.displayName="CardTitle";let o=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,t.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",l),...n})});o.displayName="CardDescription";let m=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,t.jsx)("div",{ref:s,className:i("p-6 pt-0",l),...n})});m.displayName="CardContent",n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,t.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",l),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=5036)}),_N_E=e.O()}]);