(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[764],{6999:function(e,t,n){Promise.resolve().then(n.bind(n,160))},160:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return a}});var o=n(7437),s=n(1939);function a(){return(0,o.jsx)(s.z,{tenantId:"demo-tenant-uuid-1234567890",onCreateRequest:()=>{console.log("Create new maintenance request")},onAssignRequest:(e,t)=>{console.log("Assign request to staff:",{requestId:e,staffId:t}),alert("Request ".concat(e," assigned to staff ").concat(t))},onUpdateStatus:(e,t)=>{console.log("Update request status:",{requestId:e,status:t}),alert("Request ".concat(e," status updated to ").concat(t))},onAddFeedback:(e,t,n)=>{console.log("Add feedback:",{requestId:e,rating:t,comment:n}),alert("Feedback added for request ".concat(e))}})}}},function(e){e.O(0,[7895,1939,2971,4938,1744],function(){return e(e.s=6999)}),_N_E=e.O()}]);