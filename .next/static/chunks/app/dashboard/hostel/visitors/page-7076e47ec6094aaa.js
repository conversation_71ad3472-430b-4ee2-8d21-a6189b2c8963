(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4879],{3385:function(n,o,e){Promise.resolve().then(e.bind(e,9966))},9966:function(n,o,e){"use strict";e.r(o),e.d(o,{default:function(){return i}});var t=e(7437),c=e(6273);function i(){return(0,t.jsx)(c.Z,{tenantId:"demo-tenant-uuid-1234567890",onCheckInVisitor:()=>{console.log("Check in new visitor")},onCheckOutVisitor:n=>{console.log("Check out visitor:",n),alert("Visitor ".concat(n," checked out"))},onDenyEntry:(n,o)=>{console.log("Deny entry:",{visitorId:n,reason:o}),alert("Entry denied for visitor ".concat(n,": ").concat(o))},onUpdatePolicy:n=>{console.log("Update policy:",n)}})}}},function(n){n.O(0,[7895,6273,2971,4938,1744],function(){return n(n.s=3385)}),_N_E=n.O()}]);