(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2259],{9562:function(e,n,o){Promise.resolve().then(o.bind(o,6951))},6951:function(e,n,o){"use strict";o.r(n),o.d(n,{default:function(){return c}});var t=o(7437),i=o(2952);function c(){return(0,t.jsx)(i.s,{tenantId:"demo-tenant-uuid-1234567890",onViewResident:e=>{console.log("View resident:",e)},onEditResident:e=>{console.log("Edit resident:",e)},onCheckOut:e=>{console.log("Check out resident:",e),alert("Resident ".concat(e," checked out"))},onAddDisciplinaryRecord:e=>{console.log("Add disciplinary record for resident:",e)}})}}},function(e){e.O(0,[7895,2952,2971,4938,1744],function(){return e(e.s=9562)}),_N_E=e.O()}]);