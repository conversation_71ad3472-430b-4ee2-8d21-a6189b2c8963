(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4965],{3642:function(e,o,t){Promise.resolve().then(t.bind(t,8546))},8546:function(e,o,t){"use strict";t.r(o),t.d(o,{default:function(){return a}});var n=t(7437),c=t(8487);function a(){return(0,n.jsx)(c.n,{tenantId:"demo-tenant-uuid-1234567890",onAllocateRoom:(e,o)=>{console.log("Allocate room:",{requestId:e,roomId:o}),alert("Room ".concat(o," allocated for request ").concat(e))},onDeallocateRoom:(e,o)=>{console.log("Deallocate room:",{roomId:e,studentId:o}),alert("Student ".concat(o," deallocated from room ").concat(e))},onApproveRequest:e=>{console.log("Approve request:",e),alert("Request ".concat(e," approved"))},onRejectRequest:(e,o)=>{console.log("Reject request:",{requestId:e,reason:o}),alert("Request ".concat(e," rejected: ").concat(o))}})}}},function(e){e.O(0,[7895,8487,2971,4938,1744],function(){return e(e.s=3642)}),_N_E=e.O()}]);