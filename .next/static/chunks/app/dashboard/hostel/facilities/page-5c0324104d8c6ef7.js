(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1339],{2510:function(n,e,o){Promise.resolve().then(o.bind(o,5449))},5449:function(n,e,o){"use strict";o.r(e),o.d(e,{default:function(){return i}});var t=o(7437),c=o(663);function i(){return(0,t.jsx)(c.N,{tenantId:"demo-tenant-uuid-1234567890",onBookFacility:n=>{console.log("Book facility:",n),alert("Facility ".concat(n," booking initiated"))},onCancelBooking:n=>{console.log("Cancel booking:",n),alert("Booking ".concat(n," cancelled"))},onUpdateFacility:n=>{console.log("Update facility:",n)},onScheduleMaintenance:n=>{console.log("Schedule maintenance for facility:",n),alert("Maintenance scheduled for facility ".concat(n))}})}}},function(n){n.O(0,[7895,663,2971,4938,1744],function(){return n(n.s=2510)}),_N_E=n.O()}]);