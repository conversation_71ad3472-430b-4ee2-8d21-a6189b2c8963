(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3254],{1331:function(e,s,a){Promise.resolve().then(a.bind(a,1066))},1066:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return i}});var t=a(7437);a(2265);var l=a(1396),r=a.n(l),n=a(9197);function i(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Hostel Management"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage room allocation, residents, facilities, and hostel operations"})]})}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Rooms"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFE0"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"240"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Across 4 blocks"})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Occupancy Rate"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC65"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"87%"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"418 residents"})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Maintenance Requests"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD27"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"12"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Pending"})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Monthly Revenue"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"₹8.4L"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[(0,t.jsx)(r(),{href:"/dashboard/hostel/rooms",children:(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFE0"}),(0,t.jsx)(n.ll,{className:"text-lg",children:"Room Allocation"})]})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage room assignments and availability"}),(0,t.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Rooms →"})]})]})}),(0,t.jsx)(r(),{href:"/dashboard/hostel/residents",children:(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC65"}),(0,t.jsx)(n.ll,{className:"text-lg",children:"Residents"})]})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage resident information and records"}),(0,t.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Residents →"})]})]})}),(0,t.jsx)(r(),{href:"/dashboard/hostel/maintenance",children:(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD27"}),(0,t.jsx)(n.ll,{className:"text-lg",children:"Maintenance"})]})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Track and manage maintenance requests"}),(0,t.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Maintenance →"})]})]})}),(0,t.jsx)(r(),{href:"/dashboard/hostel/visitors",children:(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDEAA"}),(0,t.jsx)(n.ll,{className:"text-lg",children:"Visitors"})]})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage visitor registration and access"}),(0,t.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Visitors →"})]})]})}),(0,t.jsx)(r(),{href:"/dashboard/hostel/facilities",children:(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFE2"}),(0,t.jsx)(n.ll,{className:"text-lg",children:"Facilities"})]})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage hostel facilities and amenities"}),(0,t.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Facilities →"})]})]})}),(0,t.jsx)(r(),{href:"/dashboard/hostel/billing",children:(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"}),(0,t.jsx)(n.ll,{className:"text-lg",children:"Billing"})]})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage hostel fees and billing"}),(0,t.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Billing →"})]})]})})]})]})}},9197:function(e,s,a){"use strict";a.d(s,{Zb:function(){return c},aY:function(){return o},SZ:function(){return m},Ol:function(){return d},ll:function(){return x}});var t=a(7437),l=a(2265),r=a(7042),n=a(4769);function i(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,n.m6)((0,r.W)(s))}let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",a),...l})});c.displayName="Card";let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",a),...l})});d.displayName="CardHeader";let x=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",a),...l})});x.displayName="CardTitle";let m=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",a),...l})});m.displayName="CardDescription";let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:i("p-6 pt-0",a),...l})});o.displayName="CardContent",l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",a),...l})}).displayName="CardFooter"},1396:function(e,s,a){e.exports=a(5250)}},function(e){e.O(0,[7895,5250,2971,4938,1744],function(){return e(e.s=1331)}),_N_E=e.O()}]);