(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7038],{2727:function(e,s,r){Promise.resolve().then(r.bind(r,4894))},4894:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return i}});var a=r(7437);r(2265);var t=r(1396),l=r.n(t),n=r(9197);function i(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Library Management"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage books, circulation, members, and digital resources"})]})}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Books"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDA"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"12,456"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+234 this year"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Books Issued"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD04"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"1,234"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently borrowed"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Active Members"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC65"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"856"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Students & faculty"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Overdue Books"}),(0,a.jsx)("span",{className:"text-2xl",children:"⏰"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"23"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Need follow-up"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsx)(l(),{href:"/dashboard/library/catalog",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDA"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Catalog"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Browse and manage book catalog"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Browse Catalog →"})]})]})}),(0,a.jsx)(l(),{href:"/dashboard/library/circulation",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD04"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Circulation"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Issue and return books"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Circulation →"})]})]})}),(0,a.jsx)(l(),{href:"/dashboard/library/members",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC65"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Members"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage library members"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Members →"})]})]})}),(0,a.jsx)(l(),{href:"/dashboard/library/reservations",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCB"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Reservations"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Handle book reservations"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Reservations →"})]})]})}),(0,a.jsx)(l(),{href:"/dashboard/library/inventory",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCE6"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Inventory"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage book inventory and stock"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Inventory →"})]})]})}),(0,a.jsx)(l(),{href:"/dashboard/library/digital",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCBB"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Digital Library"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Access digital resources and e-books"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Access Digital →"})]})]})})]})]})}},9197:function(e,s,r){"use strict";r.d(s,{Zb:function(){return c},aY:function(){return o},SZ:function(){return m},Ol:function(){return d},ll:function(){return x}});var a=r(7437),t=r(2265),l=r(7042),n=r(4769);function i(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,n.m6)((0,l.W)(s))}let c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",r),...t})});c.displayName="Card";let d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",r),...t})});d.displayName="CardHeader";let x=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",r),...t})});x.displayName="CardTitle";let m=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",r),...t})});m.displayName="CardDescription";let o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:i("p-6 pt-0",r),...t})});o.displayName="CardContent",t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",r),...t})}).displayName="CardFooter"},1396:function(e,s,r){e.exports=r(5250)}},function(e){e.O(0,[7895,5250,2971,4938,1744],function(){return e(e.s=2727)}),_N_E=e.O()}]);