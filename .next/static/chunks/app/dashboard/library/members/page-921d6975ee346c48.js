(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8913],{5310:function(e,s,r){Promise.resolve().then(r.bind(r,1371))},1371:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return l}});var t=r(7437);r(2265);var a=r(9197);function l(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Library Members"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage library memberships, student and faculty accounts"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Export Members"}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Add Member"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Total Members"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC65"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"856"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active memberships"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Students"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"734"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Student members"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Faculty"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC68‍\uD83C\uDFEB"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"89"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Faculty members"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Staff"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC68‍\uD83D\uDCBC"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"33"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Staff members"})]})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Search Members"}),(0,t.jsx)(a.SZ,{children:"Find library members by name, ID, or membership type"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Search"}),(0,t.jsx)("input",{type:"text",placeholder:"Name or Member ID...",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Member Type"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"All Members"}),(0,t.jsx)("option",{value:"student",children:"Students"}),(0,t.jsx)("option",{value:"faculty",children:"Faculty"}),(0,t.jsx)("option",{value:"staff",children:"Staff"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Status"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"All Status"}),(0,t.jsx)("option",{value:"active",children:"Active"}),(0,t.jsx)("option",{value:"suspended",children:"Suspended"}),(0,t.jsx)("option",{value:"expired",children:"Expired"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Class/Department"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"All"}),(0,t.jsx)("option",{value:"grade-9",children:"Grade 9"}),(0,t.jsx)("option",{value:"grade-10",children:"Grade 10"}),(0,t.jsx)("option",{value:"grade-11",children:"Grade 11"}),(0,t.jsx)("option",{value:"grade-12",children:"Grade 12"}),(0,t.jsx)("option",{value:"mathematics",children:"Mathematics Dept"}),(0,t.jsx)("option",{value:"science",children:"Science Dept"})]})]})]}),(0,t.jsxs)("div",{className:"mt-4 flex space-x-2",children:[(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Search"}),(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Clear Filters"})]})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Library Members"}),(0,t.jsx)(a.SZ,{children:"Manage member accounts and privileges"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"space-y-4",children:[{id:"LIB-2024-001",name:"Aarav Sharma",type:"Student",class:"Grade 10-A",rollNumber:"10A001",memberSince:"2023-04-15",status:"Active",booksIssued:3,maxBooks:5,overdueBooks:0,fineAmount:0,phone:"+91-**********",email:"<EMAIL>",lastVisit:"2024-02-15"},{id:"LIB-2024-002",name:"Dr. Priya Patel",type:"Faculty",class:"Mathematics Department",rollNumber:"FAC-MATH-001",memberSince:"2020-06-01",status:"Active",booksIssued:8,maxBooks:15,overdueBooks:1,fineAmount:50,phone:"+91-**********",email:"<EMAIL>",lastVisit:"2024-02-14"},{id:"LIB-2024-003",name:"Arjun Singh",type:"Student",class:"Grade 12-B",rollNumber:"12B015",memberSince:"2021-04-10",status:"Suspended",booksIssued:2,maxBooks:5,overdueBooks:2,fineAmount:150,phone:"+91-**********",email:"<EMAIL>",lastVisit:"2024-01-28"},{id:"LIB-2024-004",name:"Ms. Kavya Gupta",type:"Staff",class:"Administration",rollNumber:"STAFF-ADM-003",memberSince:"2022-08-15",status:"Active",booksIssued:2,maxBooks:10,overdueBooks:0,fineAmount:0,phone:"+91-9876543213",email:"<EMAIL>",lastVisit:"2024-02-12"}].map((e,s)=>(0,t.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:e.name}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Student"===e.type?"bg-blue-100 text-blue-800":"Faculty"===e.type?"bg-green-100 text-green-800":"bg-purple-100 text-purple-800"),children:e.type}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Active"===e.status?"bg-green-100 text-green-800":"Suspended"===e.status?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:e.status}),e.overdueBooks>0&&(0,t.jsxs)("span",{className:"px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full",children:[e.overdueBooks," Overdue"]})]}),(0,t.jsx)("p",{className:"text-blue-600 font-medium",children:e.id}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83C\uDF93 Class/Dept:"})," ",e.class]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCE7 Email:"})," ",e.email]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCDE Phone:"})," ",e.phone]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCDA Books Issued:"})," ",e.booksIssued,"/",e.maxBooks]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCC5 Member Since:"})," ",e.memberSince]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDD52 Last Visit:"})," ",e.lastVisit]})]})]}),(0,t.jsxs)("div",{className:"mt-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm mb-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Book Limit Usage"}),(0,t.jsxs)("span",{className:"text-gray-600",children:[e.booksIssued,"/",e.maxBooks]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full ".concat(e.booksIssued/e.maxBooks>.8?"bg-red-600":e.booksIssued/e.maxBooks>.6?"bg-yellow-600":"bg-green-600"),style:{width:"".concat(e.booksIssued/e.maxBooks*100,"%")}})})]}),e.fineAmount>0&&(0,t.jsx)("div",{className:"mt-2 p-2 bg-red-50 border border-red-200 rounded",children:(0,t.jsxs)("p",{className:"text-sm text-red-800",children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCB0 Outstanding Fine:"})," ₹",e.fineAmount]})})]}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"View Profile"}),(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"Issue Book"}),e.fineAmount>0&&(0,t.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm",children:"Collect Fine"}),"Suspended"===e.status?(0,t.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm",children:"Activate"}):(0,t.jsx)("button",{className:"text-red-600 hover:text-red-800 text-sm",children:"Suspend"})]})]})},s))}),(0,t.jsxs)("div",{className:"mt-6 flex items-center justify-between",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Showing 1-4 of 856 members"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50",children:"Previous"}),(0,t.jsx)("button",{className:"px-3 py-1 bg-blue-600 text-white rounded-md",children:"1"}),(0,t.jsx)("button",{className:"px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50",children:"2"}),(0,t.jsx)("button",{className:"px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50",children:"3"}),(0,t.jsx)("button",{className:"px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50",children:"Next"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Membership Statistics"}),(0,t.jsx)(a.SZ,{children:"Member distribution and activity"})]}),(0,t.jsx)(a.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{category:"Active Members",count:823,percentage:96,color:"bg-green-100 text-green-800"},{category:"Suspended Members",count:23,percentage:3,color:"bg-red-100 text-red-800"},{category:"Expired Memberships",count:10,percentage:1,color:"bg-yellow-100 text-yellow-800"},{category:"New This Month",count:45,percentage:5,color:"bg-blue-100 text-blue-800"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:e.category}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(e.color),children:[e.count," (",e.percentage,"%)"]})})]},s))})})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Outstanding Fines"}),(0,t.jsx)(a.SZ,{children:"Members with pending fine payments"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"space-y-3",children:[{name:"Arjun Singh",amount:150,days:15,books:2},{name:"Ravi Kumar",amount:100,days:10,books:1},{name:"Dr. Priya Patel",amount:50,days:5,books:1},{name:"Sneha Gupta",amount:75,days:8,books:1}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between border-b pb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-sm",children:e.name}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.books," overdue book(s) • ",e.days," days"]})]}),(0,t.jsxs)("span",{className:"px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full",children:["₹",e.amount]})]},s))}),(0,t.jsx)("div",{className:"mt-4 pt-3 border-t",children:(0,t.jsxs)("div",{className:"flex items-center justify-between font-medium",children:[(0,t.jsx)("span",{children:"Total Outstanding"}),(0,t.jsx)("span",{className:"text-red-600",children:"₹375"})]})})]})]})]})]})}},9197:function(e,s,r){"use strict";r.d(s,{Zb:function(){return i},aY:function(){return x},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var t=r(7437),a=r(2265),l=r(7042),d=r(4769);function n(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,d.m6)((0,l.W)(s))}let i=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:n("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:n("flex flex-col space-y-1.5 p-6",r),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h3",{ref:s,className:n("text-2xl font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let m=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("p",{ref:s,className:n("text-sm text-muted-foreground",r),...a})});m.displayName="CardDescription";let x=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:n("p-6 pt-0",r),...a})});x.displayName="CardContent",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:n("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=5310)}),_N_E=e.O()}]);