(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7855],{2762:function(e,s,t){Promise.resolve().then(t.bind(t,4673))},4673:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return c}});var r=t(7437);t(2265);var a=t(1396),n=t.n(a),l=t(9197);function i(e){let{tenantId:s}=e,t={totalVehicles:20,activeVehicles:18,totalRoutes:12,activeRoutes:10,totalDrivers:15,activeDrivers:14,studentsTransported:450,safetyScore:87,onTimePerformance:92,fuelEfficiency:8.5},a=[{type:"maintenance",message:"Vehicle DL-03-EF-9012 due for service in 2 days",severity:"medium"},{type:"license",message:"Driver license renewal required for <PERSON><PERSON> <PERSON>",severity:"high"},{type:"route",message:"Route C experiencing delays due to construction",severity:"low"}],i=e=>{switch(e){case"vehicle_maintenance":return"\uD83D\uDD27";case"route_update":return"\uD83D\uDDFA️";case"driver_assignment":return"\uD83D\uDC68‍✈️";case"safety_incident":return"⚠️";default:return"\uD83D\uDCCB"}},c=e=>{switch(e){case"high":return"bg-red-100 text-red-800 border-red-200";case"medium":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"low":return"bg-blue-100 text-blue-800 border-blue-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Transport Management"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your school transportation system efficiently and safely"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Safety Score: ",t.safetyScore,"%"]})})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-5",children:[(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ll,{className:"text-sm font-medium",children:"Total Vehicles"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDE8C"})]}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.totalVehicles}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[t.activeVehicles," active"]})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ll,{className:"text-sm font-medium",children:"Active Routes"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDDFA️"})]}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.activeRoutes}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["of ",t.totalRoutes," total"]})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ll,{className:"text-sm font-medium",children:"Active Drivers"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC68‍✈️"})]}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.activeDrivers}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["of ",t.totalDrivers," total"]})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ll,{className:"text-sm font-medium",children:"Students"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF92"})]}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.studentsTransported}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"transported daily"})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(l.ll,{className:"text-sm font-medium",children:"On-Time Performance"}),(0,r.jsx)("span",{className:"text-2xl",children:"⏰"})]}),(0,r.jsxs)(l.aY,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[t.onTimePerformance,"%"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"this month"})]})]})]}),a.length>0&&(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsx)(l.ll,{children:"Active Alerts"}),(0,r.jsx)(l.SZ,{children:"Important notifications requiring attention"})]}),(0,r.jsx)(l.aY,{children:(0,r.jsx)("div",{className:"space-y-3",children:a.map((e,s)=>(0,r.jsx)("div",{className:"p-3 rounded-lg border ".concat(c(e.severity)),children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-sm font-medium",children:["maintenance"===e.type&&"\uD83D\uDD27","license"===e.type&&"\uD83D\uDCC4","route"===e.type&&"\uD83D\uDEA7"]}),(0,r.jsx)("span",{className:"text-sm",children:e.message})]}),(0,r.jsx)("span",{className:"text-xs font-medium",children:e.severity.toUpperCase()})]})},s))})})]}),(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)(l.Zb,{className:"lg:col-span-2",children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsx)(l.ll,{children:"Recent Activity"}),(0,r.jsx)(l.SZ,{children:"Latest transport management activities"})]}),(0,r.jsx)(l.aY,{children:(0,r.jsx)("div",{className:"space-y-4",children:[{type:"vehicle_maintenance",vehicle:"DL-01-AB-1234",description:"Scheduled maintenance completed",time:"2 hours ago"},{type:"route_update",route:"Route A - Central Delhi",description:"Route optimized for better efficiency",time:"4 hours ago"},{type:"driver_assignment",driver:"Rajesh Kumar",description:"Assigned to Route B - South Delhi",time:"6 hours ago"},{type:"safety_incident",vehicle:"DL-02-CD-5678",description:"Minor breakdown resolved",time:"1 day ago"}].map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm",children:i(e.type)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:e.vehicle||e.route||e.driver}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:e.time})]},s))})})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsx)(l.ll,{children:"Quick Actions"}),(0,r.jsx)(l.SZ,{children:"Common transport management tasks"})]}),(0,r.jsxs)(l.aY,{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full text-left p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{children:"\uD83D\uDE8C"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Add New Vehicle"})]})}),(0,r.jsx)("button",{className:"w-full text-left p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{children:"\uD83D\uDDFA️"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Create Route"})]})}),(0,r.jsx)("button",{className:"w-full text-left p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{children:"\uD83D\uDC68‍✈️"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Register Driver"})]})}),(0,r.jsx)("button",{className:"w-full text-left p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{children:"⚠️"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Report Incident"})]})}),(0,r.jsx)("button",{className:"w-full text-left p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{children:"\uD83D\uDCCA"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Generate Report"})]})})]})]})]}),(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsx)(l.ll,{children:"Safety Metrics"}),(0,r.jsx)(l.SZ,{children:"Transport safety performance indicators"})]}),(0,r.jsx)(l.aY,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Overall Safety Score"}),(0,r.jsxs)("span",{className:"text-lg font-bold text-green-600",children:[t.safetyScore,"%"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Incidents This Month"}),(0,r.jsx)("span",{className:"text-lg font-bold text-blue-600",children:"2"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Safety Training Compliance"}),(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:"95%"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Vehicle Fitness Rate"}),(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:"90%"})]})]})})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsx)(l.ll,{children:"Operational Metrics"}),(0,r.jsx)(l.SZ,{children:"Transport operational performance"})]}),(0,r.jsx)(l.aY,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"On-Time Performance"}),(0,r.jsxs)("span",{className:"text-lg font-bold text-green-600",children:[t.onTimePerformance,"%"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Fuel Efficiency"}),(0,r.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[t.fuelEfficiency," km/l"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Route Utilization"}),(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:"83%"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Student Satisfaction"}),(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:"4.3/5"})]})]})})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-5",children:[(0,r.jsx)(n(),{href:"/dashboard/transport/vehicles",children:(0,r.jsxs)(l.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(l.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDE8C"}),(0,r.jsx)(l.ll,{className:"text-lg",children:"Vehicle Fleet"})]})}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Fleet management & maintenance"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Vehicles →"})]})]})}),(0,r.jsx)(n(),{href:"/dashboard/transport/routes",children:(0,r.jsxs)(l.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(l.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDDFA️"}),(0,r.jsx)(l.ll,{className:"text-lg",children:"Route Planning"})]})}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Route optimization & scheduling"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Plan Routes →"})]})]})}),(0,r.jsx)(n(),{href:"/dashboard/transport/drivers",children:(0,r.jsxs)(l.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(l.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC68‍✈️"}),(0,r.jsx)(l.ll,{className:"text-lg",children:"Driver Management"})]})}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Driver profiles & performance"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Drivers →"})]})]})}),(0,r.jsx)(n(),{href:"/dashboard/transport/students",children:(0,r.jsxs)(l.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(l.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF92"}),(0,r.jsx)(l.ll,{className:"text-lg",children:"Student Transport"})]})}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Student assignments & tracking"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Students →"})]})]})}),(0,r.jsx)(n(),{href:"/dashboard/transport/safety",children:(0,r.jsxs)(l.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(l.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDEE1️"}),(0,r.jsx)(l.ll,{className:"text-lg",children:"Safety & Tracking"})]})}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Real-time tracking & incidents"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Safety →"})]})]})})]})]})}function c(){return(0,r.jsx)(i,{tenantId:"demo-tenant-uuid-1234567890"})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return c},aY:function(){return o},SZ:function(){return m},Ol:function(){return d},ll:function(){return x}});var r=t(7437),a=t(2265),n=t(7042),l=t(4769);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,l.m6)((0,n.W)(s))}let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});c.displayName="Card";let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",t),...a})});d.displayName="CardHeader";let x=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",t),...a})});x.displayName="CardTitle";let m=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",t),...a})});m.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:i("p-6 pt-0",t),...a})});o.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},1396:function(e,s,t){e.exports=t(5250)}},function(e){e.O(0,[7895,5250,2971,4938,1744],function(){return e(e.s=2762)}),_N_E=e.O()}]);