(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[72],{2659:function(e,s,a){Promise.resolve().then(a.bind(a,617))},617:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return l}});var t=a(7437),n=a(2265),i=a(9197);function r(e){let{tenantId:s,onAddVehicle:a,onEditVehicle:r,onScheduleMaintenance:l,onUpdateStatus:c}=e,[d,m]=(0,n.useState)(""),[o,u]=(0,n.useState)("all"),[x,p]=(0,n.useState)("all"),[h,f]=(0,n.useState)("all"),[g,j]=(0,n.useState)(null),v=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"maintenance":return"bg-yellow-100 text-yellow-800";case"out_of_service":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},y=e=>{switch(e){case"bus":return"bg-blue-100 text-blue-800";case"van":return"bg-green-100 text-green-800";case"car":return"bg-purple-100 text-purple-800";case"mini_bus":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},N=e=>{switch(e){case"diesel":return"bg-yellow-100 text-yellow-800";case"petrol":return"bg-red-100 text-red-800";case"cng":return"bg-green-100 text-green-800";case"electric":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},b=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"expired":return"bg-red-100 text-red-800";case"pending_renewal":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"fit":return"bg-green-100 text-green-800";case"unfit":return"bg-red-100 text-red-800";case"pending_inspection":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},C=[{id:"1",vehicleId:"VEH-2024-001",registrationNumber:"DL-01-AB-1234",vehicleType:"bus",make:"Tata",model:"Starbus",year:2022,capacity:45,currentOccupancy:38,fuelType:"diesel",engineNumber:"ENG123456789",chassisNumber:"CHS987654321",color:"Yellow",purchaseDate:"2022-03-15",purchasePrice:25e5,currentValue:22e5,insurance:{provider:"National Insurance Company",policyNumber:"POL-2024-001",startDate:"2024-01-01",endDate:"2024-12-31",premium:45e3,coverage:["Third Party","Comprehensive","Passenger Coverage"],status:"active"},permits:[{type:"School Bus Permit",number:"SBP-2024-001",issuedBy:"Transport Department",issueDate:"2024-01-15",expiryDate:"2025-01-14",status:"valid"},{type:"Pollution Certificate",number:"PUC-2024-001",issuedBy:"Pollution Control Board",issueDate:"2024-02-01",expiryDate:"2024-08-01",status:"valid"}],maintenance:{lastService:"2024-01-15",nextService:"2024-04-15",serviceInterval:5e3,totalMileage:45e3,fuelEfficiency:8.5,maintenanceCost:125e3,warrantyStatus:"active",warrantyEndDate:"2025-03-15"},safety:{lastInspection:"2024-01-10",nextInspection:"2024-07-10",safetyRating:4.5,fitnessStatus:"fit",emergencyEquipment:["Fire Extinguisher","First Aid Kit","Emergency Exit Hammer"],gpsInstalled:!0,cameraInstalled:!0,firstAidKit:!0},assignment:{routeId:"RT-001",routeName:"Route A - Central Delhi",driverId:"DRV-001",driverName:"Rajesh Kumar",conductorId:"CON-001",conductorName:"Suresh Singh",shift:"both"},status:"active",documents:[{type:"Registration Certificate",fileName:"rc_dl01ab1234.pdf",uploadDate:"2022-03-15",verified:!0},{type:"Insurance Policy",fileName:"insurance_2024.pdf",uploadDate:"2024-01-01",expiryDate:"2024-12-31",verified:!0}],fuelRecords:[{date:"2024-02-01",quantity:50,cost:4500,mileage:44500,fuelStation:"HP Petrol Pump"},{date:"2024-01-25",quantity:45,cost:4050,mileage:44100,fuelStation:"Indian Oil Station"}],incidentHistory:[{date:"2024-01-20",type:"breakdown",description:"Engine overheating issue resolved",severity:"medium",resolved:!0}],lastUpdated:"2024-02-05",createdDate:"2022-03-15"},{id:"2",vehicleId:"VEH-2024-002",registrationNumber:"DL-02-CD-5678",vehicleType:"van",make:"Mahindra",model:"Bolero",year:2023,capacity:12,currentOccupancy:10,fuelType:"diesel",engineNumber:"ENG987654321",chassisNumber:"CHS123456789",color:"White",purchaseDate:"2023-06-10",purchasePrice:12e5,currentValue:11e5,insurance:{provider:"ICICI Lombard",policyNumber:"POL-2024-002",startDate:"2024-01-01",endDate:"2024-12-31",premium:25e3,coverage:["Third Party","Comprehensive"],status:"active"},permits:[{type:"Commercial Vehicle Permit",number:"CVP-2024-002",issuedBy:"Transport Department",issueDate:"2024-01-15",expiryDate:"2025-01-14",status:"valid"}],maintenance:{lastService:"2024-01-20",nextService:"2024-04-20",serviceInterval:1e4,totalMileage:25e3,fuelEfficiency:12,maintenanceCost:45e3,warrantyStatus:"active",warrantyEndDate:"2026-06-10"},safety:{lastInspection:"2024-01-15",nextInspection:"2024-07-15",safetyRating:4.2,fitnessStatus:"fit",emergencyEquipment:["Fire Extinguisher","First Aid Kit"],gpsInstalled:!0,cameraInstalled:!1,firstAidKit:!0},assignment:{routeId:"RT-002",routeName:"Route B - South Delhi",driverId:"DRV-002",driverName:"Amit Sharma",shift:"morning"},status:"active",documents:[{type:"Registration Certificate",fileName:"rc_dl02cd5678.pdf",uploadDate:"2023-06-10",verified:!0}],fuelRecords:[{date:"2024-02-02",quantity:30,cost:2700,mileage:24800,fuelStation:"Bharat Petroleum"}],incidentHistory:[],lastUpdated:"2024-02-03",createdDate:"2023-06-10"}].filter(e=>{var s,a;let t=e.registrationNumber.toLowerCase().includes(d.toLowerCase())||e.vehicleId.toLowerCase().includes(d.toLowerCase())||e.make.toLowerCase().includes(d.toLowerCase())||e.model.toLowerCase().includes(d.toLowerCase())||(null===(s=e.assignment.routeName)||void 0===s?void 0:s.toLowerCase().includes(d.toLowerCase()))||(null===(a=e.assignment.driverName)||void 0===a?void 0:a.toLowerCase().includes(d.toLowerCase())),n="all"===o||e.vehicleType===o,i="all"===x||e.status===x,r="all"===h||e.fuelType===h;return t&&n&&i&&r});return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Vehicle Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage your fleet vehicles and their maintenance"})]}),(0,t.jsx)("button",{type:"button",onClick:()=>null==a?void 0:a(),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Add Vehicle"})]}),(0,t.jsx)(i.Zb,{children:(0,t.jsx)(i.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsx)("div",{children:(0,t.jsx)("input",{type:"text",placeholder:"Search vehicles...",value:d,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})}),(0,t.jsx)("div",{children:(0,t.jsxs)("select",{"aria-label":"Filter by vehicle type",value:o,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Types"}),(0,t.jsx)("option",{value:"bus",children:"Bus"}),(0,t.jsx)("option",{value:"van",children:"Van"}),(0,t.jsx)("option",{value:"car",children:"Car"}),(0,t.jsx)("option",{value:"mini_bus",children:"Mini Bus"})]})}),(0,t.jsx)("div",{children:(0,t.jsxs)("select",{"aria-label":"Filter by status",value:x,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Status"}),(0,t.jsx)("option",{value:"active",children:"Active"}),(0,t.jsx)("option",{value:"maintenance",children:"Maintenance"}),(0,t.jsx)("option",{value:"out_of_service",children:"Out of Service"}),(0,t.jsx)("option",{value:"retired",children:"Retired"})]})}),(0,t.jsx)("div",{children:(0,t.jsxs)("select",{"aria-label":"Filter by fuel type",value:h,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Fuel Types"}),(0,t.jsx)("option",{value:"diesel",children:"Diesel"}),(0,t.jsx)("option",{value:"petrol",children:"Petrol"}),(0,t.jsx)("option",{value:"cng",children:"CNG"}),(0,t.jsx)("option",{value:"electric",children:"Electric"})]})}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[C.length," vehicle",1!==C.length?"s":""]})})]})})}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:C.map(e=>(0,t.jsxs)(i.Zb,{className:"hover:shadow-lg transition-shadow",children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(i.ll,{className:"text-lg",children:e.registrationNumber}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(v(e.status)),children:e.status.replace("_"," ").toUpperCase()})]}),(0,t.jsxs)(i.SZ,{children:[e.make," ",e.model," (",e.year,")"]})]}),(0,t.jsxs)(i.aY,{children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Type:"}),(0,t.jsx)("span",{className:"font-medium",children:e.vehicleType.replace("_"," ").toUpperCase()})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Capacity:"}),(0,t.jsxs)("span",{className:"font-medium",children:[e.capacity," passengers"]})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Current Occupancy:"}),(0,t.jsxs)("span",{className:"font-medium",children:[e.currentOccupancy,"/",e.capacity]})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Fuel Type:"}),(0,t.jsx)("span",{className:"font-medium",children:e.fuelType.toUpperCase()})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Route:"}),(0,t.jsx)("span",{className:"font-medium",children:e.assignment.routeName||"Unassigned"})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Driver:"}),(0,t.jsx)("span",{className:"font-medium",children:e.assignment.driverName||"Unassigned"})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Next Service:"}),(0,t.jsx)("span",{className:"font-medium",children:e.maintenance.nextService})]})]}),(0,t.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex space-x-2",children:[e.safety.gpsInstalled&&(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full",children:"GPS"}),e.safety.cameraInstalled&&(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:"Camera"}),"active"===e.insurance.status&&(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full",children:"Insured"})]}),(0,t.jsx)("button",{type:"button",onClick:()=>j(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"})]})]})]},e.id))}),g&&(0,t.jsx)(()=>g?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold",children:g.registrationNumber}),(0,t.jsx)("button",{onClick:()=>j(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Vehicle Information"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Vehicle ID:"})," ",g.vehicleId]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Registration:"})," ",g.registrationNumber]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Make & Model:"})," ",g.make," ",g.model]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Year:"})," ",g.year]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Type:"}),(0,t.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(y(g.vehicleType)),children:g.vehicleType.replace("_"," ").toUpperCase()})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Fuel Type:"}),(0,t.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(N(g.fuelType)),children:g.fuelType.toUpperCase()})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Capacity:"})," ",g.capacity," passengers"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Current Occupancy:"})," ",g.currentOccupancy]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Color:"})," ",g.color]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Technical Details"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Engine Number:"})," ",g.engineNumber]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Chassis Number:"})," ",g.chassisNumber]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Purchase Date:"})," ",g.purchaseDate]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Purchase Price:"})," ₹",g.purchasePrice.toLocaleString()]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Current Value:"})," ₹",g.currentValue.toLocaleString()]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Total Mileage:"})," ",g.maintenance.totalMileage.toLocaleString()," km"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Fuel Efficiency:"})," ",g.maintenance.fuelEfficiency," km/l"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Status:"}),(0,t.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(v(g.status)),children:g.status.replace("_"," ").toUpperCase()})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Current Assignment"}),(0,t.jsx)("div",{className:"space-y-2 text-sm",children:g.assignment.routeName?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Route:"})," ",g.assignment.routeName]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Driver:"})," ",g.assignment.driverName]}),g.assignment.conductorName&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Conductor:"})," ",g.assignment.conductorName]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Shift:"})," ",g.assignment.shift.replace("_"," ").toUpperCase()]})]}):(0,t.jsx)("div",{className:"text-gray-500",children:"No current assignment"})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Insurance Details"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm bg-gray-50 p-3 rounded",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Provider:"})," ",g.insurance.provider]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Policy Number:"})," ",g.insurance.policyNumber]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Period:"})," ",g.insurance.startDate," to ",g.insurance.endDate]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Premium:"})," ₹",g.insurance.premium.toLocaleString()]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Status:"}),(0,t.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(b(g.insurance.status)),children:g.insurance.status.replace("_"," ").toUpperCase()})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Coverage:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:g.insurance.coverage.map((e,s)=>(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e},s))})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Safety & Fitness"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm bg-gray-50 p-3 rounded",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Last Inspection:"})," ",g.safety.lastInspection]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Next Inspection:"})," ",g.safety.nextInspection]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Safety Rating:"})," ⭐ ",g.safety.safetyRating,"/5"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Fitness Status:"}),(0,t.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(w(g.safety.fitnessStatus)),children:g.safety.fitnessStatus.replace("_"," ").toUpperCase()})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"GPS Installed:"})," ",g.safety.gpsInstalled?"✅":"❌"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Camera Installed:"})," ",g.safety.cameraInstalled?"✅":"❌"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"First Aid Kit:"})," ",g.safety.firstAidKit?"✅":"❌"]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Permits & Licenses"}),(0,t.jsx)("div",{className:"space-y-2",children:g.permits.map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-2",children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:e.type}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Number: ",e.number]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Issued by: ",e.issuedBy]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Valid until: ",e.expiryDate]}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("valid"===e.status?"bg-green-100 text-green-800":"expired"===e.status?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:e.status.replace("_"," ").toUpperCase()})]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Maintenance Summary"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm bg-gray-50 p-3 rounded",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Last Service:"})," ",g.maintenance.lastService]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Next Service:"})," ",g.maintenance.nextService]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Service Interval:"})," ",g.maintenance.serviceInterval.toLocaleString()," km"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Maintenance Cost:"})," ₹",g.maintenance.maintenanceCost.toLocaleString()]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Warranty Status:"}),(0,t.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat("active"===g.maintenance.warrantyStatus?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:g.maintenance.warrantyStatus.toUpperCase()})]}),g.maintenance.warrantyEndDate&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Warranty Until:"})," ",g.maintenance.warrantyEndDate]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Emergency Equipment"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:g.safety.emergencyEquipment.map((e,s)=>(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full",children:e},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Recent Fuel Records"}),(0,t.jsx)("div",{className:"space-y-2",children:g.fuelRecords.slice(0,3).map((e,s)=>(0,t.jsxs)("div",{className:"text-sm border rounded p-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:e.date}),(0,t.jsxs)("span",{className:"font-medium",children:["₹",e.cost]})]}),(0,t.jsxs)("div",{className:"text-gray-600",children:[e.quantity,"L at ",e.fuelStation]}),(0,t.jsxs)("div",{className:"text-gray-600",children:["Mileage: ",e.mileage.toLocaleString()," km"]})]},s))})]})]}),g.incidentHistory.length>0&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Incident History"}),(0,t.jsx)("div",{className:"space-y-2",children:g.incidentHistory.map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("span",{className:"font-medium text-sm",children:e.type.replace("_"," ").toUpperCase()}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("high"===e.severity?"bg-red-100 text-red-800":"medium"===e.severity?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:e.severity.toUpperCase()}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(e.resolved?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.resolved?"RESOLVED":"PENDING"})]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Date: ",e.date]})]},s))})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{onClick:()=>null==r?void 0:r(g.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Edit Vehicle"}),(0,t.jsx)("button",{onClick:()=>null==l?void 0:l(g.id),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Schedule Maintenance"}),(0,t.jsx)("button",{className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700",children:"Generate Report"})]})]})}):null,{})]})}function l(){return(0,t.jsx)(r,{tenantId:"demo-tenant-uuid-1234567890",onAddVehicle:()=>{console.log("Add new vehicle")},onEditVehicle:e=>{console.log("Edit vehicle:",e)},onScheduleMaintenance:e=>{console.log("Schedule maintenance for vehicle:",e),alert("Maintenance scheduled for vehicle ".concat(e))},onUpdateStatus:(e,s)=>{console.log("Update vehicle status:",{vehicleId:e,status:s}),alert("Vehicle ".concat(e," status updated to ").concat(s))}})}},9197:function(e,s,a){"use strict";a.d(s,{Zb:function(){return c},aY:function(){return u},SZ:function(){return o},Ol:function(){return d},ll:function(){return m}});var t=a(7437),n=a(2265),i=a(7042),r=a(4769);function l(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.m6)((0,i.W)(s))}let c=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:s,className:l("rounded-lg border bg-card text-card-foreground shadow-sm",a),...n})});c.displayName="Card";let d=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:s,className:l("flex flex-col space-y-1.5 p-6",a),...n})});d.displayName="CardHeader";let m=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("h3",{ref:s,className:l("text-2xl font-semibold leading-none tracking-tight",a),...n})});m.displayName="CardTitle";let o=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("p",{ref:s,className:l("text-sm text-muted-foreground",a),...n})});o.displayName="CardDescription";let u=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:s,className:l("p-6 pt-0",a),...n})});u.displayName="CardContent",n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:s,className:l("flex items-center p-6 pt-0",a),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=2659)}),_N_E=e.O()}]);