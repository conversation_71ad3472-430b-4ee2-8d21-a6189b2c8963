(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4857],{9794:function(e,s,t){Promise.resolve().then(t.bind(t,1293))},1293:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return r}});var a=t(7437);t(2265);var n=t(9197);function r(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Payment History"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Complete payment transaction history and audit trail"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Export History"}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Advanced Search"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Transactions"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"2,456"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"This academic year"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Amount"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"₹39.2M"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Collected to date"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"This Month"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC5"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"₹5.9M"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"234 transactions"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Average Transaction"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC8"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"₹15,960"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Per transaction"})]})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Search Payment History"}),(0,a.jsx)(n.SZ,{children:"Filter and search through payment transactions"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Student name, receipt no...",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Date Range"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"",children:"All Dates"}),(0,a.jsx)("option",{value:"today",children:"Today"}),(0,a.jsx)("option",{value:"this-week",children:"This Week"}),(0,a.jsx)("option",{value:"this-month",children:"This Month"}),(0,a.jsx)("option",{value:"last-month",children:"Last Month"}),(0,a.jsx)("option",{value:"custom",children:"Custom Range"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Payment Method"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"",children:"All Methods"}),(0,a.jsx)("option",{value:"cash",children:"Cash"}),(0,a.jsx)("option",{value:"cheque",children:"Cheque"}),(0,a.jsx)("option",{value:"online",children:"Online Banking"}),(0,a.jsx)("option",{value:"card",children:"Credit/Debit Card"}),(0,a.jsx)("option",{value:"upi",children:"UPI"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Amount Range"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"",children:"All Amounts"}),(0,a.jsx)("option",{value:"0-10000",children:"₹0 - ₹10,000"}),(0,a.jsx)("option",{value:"10000-25000",children:"₹10,000 - ₹25,000"}),(0,a.jsx)("option",{value:"25000-50000",children:"₹25,000 - ₹50,000"}),(0,a.jsx)("option",{value:"50000+",children:"₹50,000+"})]})]})]}),(0,a.jsxs)("div",{className:"mt-4 flex space-x-2",children:[(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Search"}),(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Clear Filters"}),(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-800",children:"Export Results"})]})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Payment Transactions"}),(0,a.jsx)(n.SZ,{children:"Complete payment history with transaction details"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"space-y-4",children:[{receiptNo:"RCP-2024-001234",date:"2024-02-16",time:"10:30 AM",studentName:"Aarav Sharma",rollNo:"9A001",grade:"Grade 9",amount:29500,method:"Online Banking",transactionId:"TXN789456123",billNo:"BILL-2024-001234",status:"Confirmed",processedBy:"Finance Admin"},{receiptNo:"RCP-2024-001235",date:"2024-02-16",time:"11:15 AM",studentName:"Kavya Gupta",rollNo:"9A002",grade:"Grade 9",amount:41500,method:"Credit Card",transactionId:"TXN789456124",billNo:"BILL-2024-001235",status:"Confirmed",processedBy:"System Auto"},{receiptNo:"RCP-2024-001236",date:"2024-02-15",time:"02:45 PM",studentName:"Riya Patel",rollNo:"6B004",grade:"Grade 6",amount:25750,method:"Cash",transactionId:"CASH-001236",billNo:"BILL-2024-001237",status:"Confirmed",processedBy:"Cashier"},{receiptNo:"RCP-2024-001237",date:"2024-02-15",time:"09:20 AM",studentName:"Vikram Singh",rollNo:"11A005",grade:"Grade 11",amount:33250,method:"Cheque",transactionId:"CHQ-456789",billNo:"BILL-2024-001238",status:"Cleared",processedBy:"Finance Admin"},{receiptNo:"RCP-2024-001238",date:"2024-02-14",time:"04:10 PM",studentName:"Ananya Gupta",rollNo:"10B012",grade:"Grade 10",amount:31200,method:"UPI",transactionId:"UPI789123456",billNo:"BILL-2024-001239",status:"Confirmed",processedBy:"System Auto"},{receiptNo:"RCP-2024-001239",date:"2024-02-14",time:"01:30 PM",studentName:"Rohit Kumar",rollNo:"12A008",grade:"Grade 12",amount:35800,method:"Online Banking",transactionId:"TXN789456125",billNo:"BILL-2024-001240",status:"Confirmed",processedBy:"System Auto"}].map((e,s)=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.studentName}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e.grade}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Confirmed"===e.status?"bg-green-100 text-green-800":"Cleared"===e.status?"bg-blue-100 text-blue-800":"Pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.status})]}),(0,a.jsx)("p",{className:"text-blue-600 font-medium",children:e.receiptNo}),(0,a.jsxs)("p",{className:"text-gray-600 text-sm",children:[e.rollNo," • Bill: ",e.billNo]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCB0 Amount:"})," ₹",e.amount.toLocaleString()]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCC5 Date:"})," ",e.date," at ",e.time]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCB3 Method:"})," ",e.method]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDD22 Transaction ID:"})," ",e.transactionId]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDC64 Processed By:"})," ",e.processedBy]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"View Receipt"}),(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"Print Receipt"}),(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm",children:"Transaction Details"})]})]})},s))}),(0,a.jsxs)("div",{className:"mt-6 flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Showing 1-6 of 2,456 transactions"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50",children:"Previous"}),(0,a.jsx)("button",{className:"px-3 py-1 bg-blue-600 text-white rounded-md",children:"1"}),(0,a.jsx)("button",{className:"px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50",children:"2"}),(0,a.jsx)("button",{className:"px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50",children:"3"}),(0,a.jsx)("button",{className:"px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50",children:"Next"})]})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Payment Trends"}),(0,a.jsx)(n.SZ,{children:"Monthly payment volume and trends"})]}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{month:"October 2023",transactions:189,amount:"₹3.2M",avgAmount:"₹16,931"},{month:"November 2023",transactions:234,amount:"₹4.1M",avgAmount:"₹17,521"},{month:"December 2023",transactions:298,amount:"₹5.8M",avgAmount:"₹19,463"},{month:"January 2024",transactions:312,amount:"₹6.5M",avgAmount:"₹20,833"},{month:"February 2024",transactions:289,amount:"₹5.9M",avgAmount:"₹20,415"}].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:e.month}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-600",children:e.amount})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs text-gray-600",children:[(0,a.jsxs)("div",{children:["Transactions: ",e.transactions]}),(0,a.jsxs)("div",{children:["Avg: ",e.avgAmount]})]})]},s))})})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Payment Method Distribution"}),(0,a.jsx)(n.SZ,{children:"Historical payment method usage"})]}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{method:"Online Banking",transactions:1234,percentage:50,amount:"₹19.6M"},{method:"Credit/Debit Card",transactions:678,percentage:28,amount:"₹11.0M"},{method:"UPI",transactions:345,percentage:14,amount:"₹5.5M"},{method:"Cash",transactions:156,percentage:6,amount:"₹2.4M"},{method:"Cheque",transactions:43,percentage:2,amount:"₹0.7M"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e.method}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.transactions," transactions"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e.amount}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.percentage,"%")}})}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[e.percentage,"%"]})]})]})]},s))})})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Quick Actions"}),(0,a.jsx)(n.SZ,{children:"Common payment history operations"})]}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[{action:"Export All Data",description:"Download complete payment history",icon:"\uD83D\uDCE5"},{action:"Generate Report",description:"Create payment summary report",icon:"\uD83D\uDCCA"},{action:"Audit Trail",description:"View detailed audit information",icon:"\uD83D\uDD0D"},{action:"Reconciliation",description:"Match payments with bank records",icon:"⚖️"}].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-lg p-4 text-center hover:shadow-md transition-shadow",children:[(0,a.jsx)("div",{className:"text-3xl mb-2",children:e.icon}),(0,a.jsx)("h3",{className:"font-semibold mb-1",children:e.action}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Execute"})]},s))})})]})]})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return i},aY:function(){return x},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var a=t(7437),n=t(2265),r=t(7042),l=t(4769);function d(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,l.m6)((0,r.W)(s))}let i=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});i.displayName="Card";let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:d("flex flex-col space-y-1.5 p-6",t),...n})});c.displayName="CardHeader";let o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("h3",{ref:s,className:d("text-2xl font-semibold leading-none tracking-tight",t),...n})});o.displayName="CardTitle";let m=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("p",{ref:s,className:d("text-sm text-muted-foreground",t),...n})});m.displayName="CardDescription";let x=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:d("p-6 pt-0",t),...n})});x.displayName="CardContent",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:d("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=9794)}),_N_E=e.O()}]);