(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7669],{6449:function(e,s,t){Promise.resolve().then(t.bind(t,8586))},8586:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return l}});var a=t(7437);t(2265);var n=t(9197);function l(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Payment Management"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Track and manage student fee payments and transactions"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Payment Report"}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Record Payment"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Today's Collections"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"₹2.4M"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"156 transactions"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Monthly Collections"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"₹36.8M"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+12% from last month"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Pending Payments"}),(0,a.jsx)("span",{className:"text-2xl",children:"⏳"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"₹8.2M"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"234 students"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Collection Rate"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC8"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"87%"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"This term"})]})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Recent Payments"}),(0,a.jsx)(n.SZ,{children:"Latest fee payments received"})]}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{receiptNo:"RCP-2024-001234",studentName:"Aarav Sharma",rollNo:"9A001",grade:"Grade 9",amount:29500,paymentDate:"2024-02-16",paymentMethod:"Online Banking",transactionId:"TXN789456123",status:"Confirmed",billNo:"BILL-2024-001234"},{receiptNo:"RCP-2024-001235",studentName:"Kavya Gupta",rollNo:"9A002",grade:"Grade 9",amount:41500,paymentDate:"2024-02-16",paymentMethod:"Credit Card",transactionId:"TXN789456124",status:"Confirmed",billNo:"BILL-2024-001235"},{receiptNo:"RCP-2024-001236",studentName:"Riya Patel",rollNo:"6B004",grade:"Grade 6",amount:25750,paymentDate:"2024-02-15",paymentMethod:"Cash",transactionId:"CASH-001236",status:"Confirmed",billNo:"BILL-2024-001237"},{receiptNo:"RCP-2024-001237",studentName:"Vikram Singh",rollNo:"11A005",grade:"Grade 11",amount:33250,paymentDate:"2024-02-15",paymentMethod:"Cheque",transactionId:"CHQ-456789",status:"Pending Clearance",billNo:"BILL-2024-001238"}].map((e,s)=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.studentName}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e.grade}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Confirmed"===e.status?"bg-green-100 text-green-800":"Pending Clearance"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.status})]}),(0,a.jsx)("p",{className:"text-blue-600 font-medium",children:e.receiptNo}),(0,a.jsxs)("p",{className:"text-gray-600 text-sm",children:[e.rollNo," • Bill: ",e.billNo]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCB0 Amount:"})," ₹",e.amount.toLocaleString()]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCC5 Date:"})," ",e.paymentDate]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCB3 Method:"})," ",e.paymentMethod]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDD22 Transaction ID:"})," ",e.transactionId]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"View Receipt"}),(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"Print Receipt"}),"Pending Clearance"===e.status&&(0,a.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm",children:"Confirm Payment"})]})]})},s))})})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Record New Payment"}),(0,a.jsx)(n.SZ,{children:"Manually record a payment transaction"})]}),(0,a.jsx)(n.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Student"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"",children:"Select Student"}),(0,a.jsx)("option",{value:"aarav",children:"Aarav Sharma (9A001)"}),(0,a.jsx)("option",{value:"kavya",children:"Kavya Gupta (9A002)"}),(0,a.jsx)("option",{value:"arjun",children:"Arjun Singh (11A003)"}),(0,a.jsx)("option",{value:"riya",children:"Riya Patel (6B004)"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Outstanding Bill"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"",children:"Select Bill"}),(0,a.jsx)("option",{value:"bill1",children:"BILL-2024-001234 (₹29,500)"}),(0,a.jsx)("option",{value:"bill2",children:"BILL-2024-001235 (₹41,500)"}),(0,a.jsx)("option",{value:"bill3",children:"BILL-2024-001236 (₹25,750)"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Payment Amount"}),(0,a.jsx)("input",{type:"number",placeholder:"29500",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Payment Method"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"",children:"Select Method"}),(0,a.jsx)("option",{value:"cash",children:"Cash"}),(0,a.jsx)("option",{value:"cheque",children:"Cheque"}),(0,a.jsx)("option",{value:"online",children:"Online Banking"}),(0,a.jsx)("option",{value:"card",children:"Credit/Debit Card"}),(0,a.jsx)("option",{value:"upi",children:"UPI"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Payment Date"}),(0,a.jsx)("input",{type:"date",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Transaction Reference"}),(0,a.jsx)("input",{type:"text",placeholder:"Transaction ID / Cheque No.",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Payment Notes"}),(0,a.jsx)("textarea",{rows:3,placeholder:"Add any additional notes about this payment...",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Record Payment"}),(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Save as Draft"}),(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-800",children:"Generate Receipt"})]})]})})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Payment Methods"}),(0,a.jsx)(n.SZ,{children:"Distribution of payment methods"})]}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{method:"Online Banking",count:456,amount:"₹18.2M",percentage:49},{method:"Credit/Debit Card",count:234,amount:"₹9.8M",percentage:27},{method:"UPI",count:189,amount:"₹6.1M",percentage:16},{method:"Cash",count:89,amount:"₹2.3M",percentage:6},{method:"Cheque",count:23,amount:"₹0.8M",percentage:2}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e.method}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.count," transactions"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e.amount}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.percentage,"%")}})}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[e.percentage,"%"]})]})]})]},s))})})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Payment Status"}),(0,a.jsx)(n.SZ,{children:"Current payment status overview"})]}),(0,a.jsx)(n.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-green-600 mb-1",children:"87%"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Collection Rate"}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mt-2",children:(0,a.jsx)("div",{className:"bg-green-600 h-3 rounded-full",style:{width:"87%"}})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Payments Received"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:"₹36.8M"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Pending Payments"}),(0,a.jsx)("span",{className:"font-medium text-yellow-600",children:"₹5.4M"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Overdue Payments"}),(0,a.jsx)("span",{className:"font-medium text-red-600",children:"₹2.8M"})]})]}),(0,a.jsx)("div",{className:"pt-3 border-t",children:(0,a.jsxs)("div",{className:"flex justify-between font-medium",children:[(0,a.jsx)("span",{children:"Total Expected"}),(0,a.jsx)("span",{className:"text-blue-600",children:"₹45.0M"})]})})]})})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Payment Tools"}),(0,a.jsx)(n.SZ,{children:"Quick actions and utilities for payment management"})]}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[{tool:"Bulk Payment Import",description:"Import multiple payments",icon:"\uD83D\uDCE5"},{tool:"Payment Reconciliation",description:"Match payments with bills",icon:"\uD83D\uDD04"},{tool:"Refund Management",description:"Process payment refunds",icon:"↩️"},{tool:"Payment Gateway",description:"Online payment integration",icon:"\uD83C\uDF10"}].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-lg p-4 text-center hover:shadow-md transition-shadow",children:[(0,a.jsx)("div",{className:"text-3xl mb-2",children:e.icon}),(0,a.jsx)("h3",{className:"font-semibold mb-1",children:e.tool}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Use Tool"})]},s))})})]})]})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return i},aY:function(){return x},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var a=t(7437),n=t(2265),l=t(7042),r=t(4769);function d(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,l.W)(s))}let i=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});i.displayName="Card";let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:d("flex flex-col space-y-1.5 p-6",t),...n})});c.displayName="CardHeader";let o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("h3",{ref:s,className:d("text-2xl font-semibold leading-none tracking-tight",t),...n})});o.displayName="CardTitle";let m=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("p",{ref:s,className:d("text-sm text-muted-foreground",t),...n})});m.displayName="CardDescription";let x=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:d("p-6 pt-0",t),...n})});x.displayName="CardContent",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:d("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=6449)}),_N_E=e.O()}]);