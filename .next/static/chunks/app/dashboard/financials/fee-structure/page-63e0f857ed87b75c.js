(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9396],{7921:function(e,s,t){Promise.resolve().then(t.bind(t,5267))},5267:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return l}});var r=t(7437);t(2265);var a=t(9197);function l(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Fee Structure Management"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Configure and manage fee structures for different grades and categories"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Import Structure"}),(0,r.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Add Fee Category"})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Fee Categories"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCB"})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"12"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active categories"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Total Annual Fees"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"₹2.4M"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Per student average"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Grade Levels"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"8"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Different structures"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Last Updated"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD04"})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"15"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Days ago"})]})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Fee Structure by Grade Level"}),(0,r.jsx)(a.SZ,{children:"Annual fee breakdown for academic year 2024-25"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"space-y-4",children:[{grade:"Nursery - Grade 2",tuitionFee:45e3,admissionFee:5e3,developmentFee:8e3,examFee:2e3,libraryFee:1500,sportsFee:2500,transportFee:12e3,total:76e3},{grade:"Grade 3 - Grade 5",tuitionFee:55e3,admissionFee:5e3,developmentFee:1e4,examFee:2500,libraryFee:2e3,sportsFee:3e3,transportFee:12e3,total:89500},{grade:"Grade 6 - Grade 8",tuitionFee:65e3,admissionFee:5e3,developmentFee:12e3,examFee:3e3,libraryFee:2500,sportsFee:3500,transportFee:12e3,total:103e3},{grade:"Grade 9 - Grade 10",tuitionFee:75e3,admissionFee:5e3,developmentFee:15e3,examFee:4e3,libraryFee:3e3,sportsFee:4e3,transportFee:12e3,total:118e3},{grade:"Grade 11 - Grade 12",tuitionFee:85e3,admissionFee:5e3,developmentFee:18e3,examFee:5e3,libraryFee:3500,sportsFee:4500,transportFee:12e3,total:133e3}].map((e,s)=>(0,r.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:e.grade}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"text-xl font-bold text-blue-600",children:["₹",e.total.toLocaleString()]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Total Annual Fee"})]})]}),(0,r.jsxs)("div",{className:"grid gap-3 md:grid-cols-3 lg:grid-cols-4",children:[(0,r.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,r.jsxs)("div",{className:"text-lg font-semibold text-green-600",children:["₹",e.tuitionFee.toLocaleString()]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Tuition Fee"})]}),(0,r.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,r.jsxs)("div",{className:"text-lg font-semibold text-blue-600",children:["₹",e.admissionFee.toLocaleString()]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Admission Fee"})]}),(0,r.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,r.jsxs)("div",{className:"text-lg font-semibold text-purple-600",children:["₹",e.developmentFee.toLocaleString()]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Development Fee"})]}),(0,r.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,r.jsxs)("div",{className:"text-lg font-semibold text-orange-600",children:["₹",e.examFee.toLocaleString()]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Exam Fee"})]}),(0,r.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,r.jsxs)("div",{className:"text-lg font-semibold text-red-600",children:["₹",e.libraryFee.toLocaleString()]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Library Fee"})]}),(0,r.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,r.jsxs)("div",{className:"text-lg font-semibold text-yellow-600",children:["₹",e.sportsFee.toLocaleString()]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Sports Fee"})]}),(0,r.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,r.jsxs)("div",{className:"text-lg font-semibold text-indigo-600",children:["₹",e.transportFee.toLocaleString()]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Transport Fee"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"(Optional)"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2 mt-4",children:[(0,r.jsx)("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Edit Structure"}),(0,r.jsx)("button",{className:"border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50",children:"View Details"}),(0,r.jsx)("button",{className:"text-green-600 hover:text-green-800 text-sm",children:"Apply to Students"})]})]},s))})})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Fee Categories"}),(0,r.jsx)(a.SZ,{children:"Manage individual fee categories and their configurations"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"space-y-4",children:[{category:"Tuition Fee",description:"Core academic instruction fee",type:"Mandatory",frequency:"Annual",taxable:!0,grades:"All Grades",amount:"₹45,000 - ₹85,000"},{category:"Development Fee",description:"Infrastructure and facility development",type:"Mandatory",frequency:"Annual",taxable:!1,grades:"All Grades",amount:"₹8,000 - ₹18,000"},{category:"Transport Fee",description:"School bus transportation service",type:"Optional",frequency:"Annual",taxable:!0,grades:"All Grades",amount:"₹12,000"},{category:"Late Fee",description:"Penalty for delayed fee payment",type:"Penalty",frequency:"As applicable",taxable:!1,grades:"All Grades",amount:"₹500 per month"}].map((e,s)=>(0,r.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:e.category}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Mandatory"===e.type?"bg-red-100 text-red-800":"Optional"===e.type?"bg-blue-100 text-blue-800":"bg-yellow-100 text-yellow-800"),children:e.type}),e.taxable&&(0,r.jsx)("span",{className:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full",children:"Taxable"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:e.description}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Frequency:"})," ",e.frequency]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Applicable to:"})," ",e.grades]})]}),(0,r.jsx)("div",{children:(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Amount Range:"})," ",e.amount]})})]})]}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,r.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"Edit Category"}),(0,r.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"View Usage"})]})]})},s))})})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Fee Structure Tools"}),(0,r.jsx)(a.SZ,{children:"Tools for managing fee structures"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"space-y-3",children:[{tool:"Bulk Fee Update",description:"Update fees across multiple grades",icon:"\uD83D\uDD04"},{tool:"Fee Calculator",description:"Calculate total fees for students",icon:"\uD83E\uDDEE"},{tool:"Discount Manager",description:"Manage fee discounts and scholarships",icon:"\uD83D\uDCB8"},{tool:"Fee Comparison",description:"Compare fees across academic years",icon:"\uD83D\uDCCA"},{tool:"Structure Validator",description:"Validate fee structure consistency",icon:"✅"}].map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between border rounded p-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-2xl",children:e.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.tool}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:e.description})]})]}),(0,r.jsx)("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Use Tool"})]},s))})})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Fee Structure History"}),(0,r.jsx)(a.SZ,{children:"Recent changes to fee structures"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"space-y-3",children:[{change:"Updated Grade 11-12 tuition fee",amount:"₹85,000",date:"2024-02-01",user:"Finance Admin"},{change:"Added new sports fee category",amount:"₹4,500",date:"2024-01-28",user:"Principal"},{change:"Modified transport fee structure",amount:"₹12,000",date:"2024-01-25",user:"Finance Admin"},{change:"Updated development fee for Grade 9-10",amount:"₹15,000",date:"2024-01-20",user:"Finance Admin"}].map((e,s)=>(0,r.jsx)("div",{className:"border-b pb-2",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.change}),(0,r.jsxs)("p",{className:"text-xs text-gray-600",children:[e.user," • ",e.date]})]}),(0,r.jsx)("span",{className:"text-sm font-medium text-green-600",children:e.amount})]})},s))})})]})]})]})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return i},aY:function(){return m},SZ:function(){return x},Ol:function(){return c},ll:function(){return o}});var r=t(7437),a=t(2265),l=t(7042),n=t(4769);function d(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,l.W)(s))}let i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:d("flex flex-col space-y-1.5 p-6",t),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:d("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let x=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:d("text-sm text-muted-foreground",t),...a})});x.displayName="CardDescription";let m=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:d("p-6 pt-0",t),...a})});m.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:d("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=7921)}),_N_E=e.O()}]);