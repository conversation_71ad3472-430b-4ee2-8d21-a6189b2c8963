(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4807],{5894:function(e,s,t){Promise.resolve().then(t.bind(t,5107))},5107:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return l}});var a=t(7437);t(2265);var i=t(9197);function l(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Financial Aid & Scholarships"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage scholarships, fee waivers, and financial assistance programs"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Aid Report"}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Create Scholarship"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Active Scholarships"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"})]}),(0,a.jsxs)(i.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"89"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Students receiving aid"})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Total Aid Amount"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"})]}),(0,a.jsxs)(i.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"₹12.4M"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"This academic year"})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Pending Applications"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCB"})]}),(0,a.jsxs)(i.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"23"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Under review"})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Aid Programs"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,a.jsxs)(i.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"8"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active programs"})]})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsx)(i.ll,{children:"Scholarship Programs"}),(0,a.jsx)(i.SZ,{children:"Available financial aid and scholarship programs"})]}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{name:"Merit Scholarship",description:"For students with academic excellence (90%+ marks)",type:"Academic Merit",coverage:"50% tuition fee",eligibility:"Grade 9-12, 90%+ marks",recipients:23,budget:"₹2.8M",status:"Active"},{name:"Need-Based Financial Aid",description:"For economically disadvantaged students",type:"Financial Need",coverage:"Up to 100% fee waiver",eligibility:"Family income < ₹3 LPA",recipients:34,budget:"₹4.2M",status:"Active"},{name:"Sports Excellence Scholarship",description:"For outstanding sports achievements",type:"Sports",coverage:"25% tuition fee",eligibility:"State/National level sports",recipients:12,budget:"₹1.1M",status:"Active"},{name:"Sibling Discount",description:"Discount for families with multiple children",type:"Family",coverage:"20% for 2nd child, 30% for 3rd+",eligibility:"Multiple children enrolled",recipients:45,budget:"₹3.8M",status:"Active"},{name:"Staff Ward Concession",description:"Fee concession for staff children",type:"Staff Benefit",coverage:"75% fee waiver",eligibility:"Children of school staff",recipients:18,budget:"₹2.1M",status:"Active"}].map((e,s)=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.name}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Academic Merit"===e.type?"bg-blue-100 text-blue-800":"Financial Need"===e.type?"bg-green-100 text-green-800":"Sports"===e.type?"bg-orange-100 text-orange-800":"Family"===e.type?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"),children:e.type}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Active"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.status})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-2",children:e.description}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCB0 Coverage:"})," ",e.coverage]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCCB Eligibility:"})," ",e.eligibility]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDC65 Recipients:"})," ",e.recipients," students"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCB5 Budget:"})," ",e.budget]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"View Details"}),(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"Edit Program"}),(0,a.jsx)("button",{className:"text-green-600 hover:text-green-800 text-sm",children:"View Recipients"})]})]})},s))})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsx)(i.ll,{children:"Pending Scholarship Applications"}),(0,a.jsx)(i.SZ,{children:"Applications awaiting review and approval"})]}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{applicationId:"SCH-2024-001",studentName:"Priya Sharma",rollNo:"10A015",grade:"Grade 10",program:"Merit Scholarship",appliedDate:"2024-02-10",academicScore:94,familyIncome:"₹4.5 LPA",documents:["Income Certificate","Mark Sheets","Bank Statement"],status:"Under Review"},{applicationId:"SCH-2024-002",studentName:"Rahul Kumar",rollNo:"9B008",grade:"Grade 9",program:"Need-Based Financial Aid",appliedDate:"2024-02-12",academicScore:78,familyIncome:"₹2.1 LPA",documents:["Income Certificate","BPL Card","Mark Sheets"],status:"Documents Pending"},{applicationId:"SCH-2024-003",studentName:"Ananya Patel",rollNo:"11A007",grade:"Grade 11",program:"Sports Excellence Scholarship",appliedDate:"2024-02-08",academicScore:82,familyIncome:"₹6.2 LPA",documents:["Sports Certificates","Mark Sheets","Coach Recommendation"],status:"Ready for Approval"}].map((e,s)=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.studentName}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e.grade}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Ready for Approval"===e.status?"bg-green-100 text-green-800":"Under Review"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.status})]}),(0,a.jsx)("p",{className:"text-blue-600 font-medium",children:e.applicationId}),(0,a.jsxs)("p",{className:"text-gray-600 text-sm",children:[e.rollNo," • Applied for: ",e.program]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCC5 Applied:"})," ",e.appliedDate]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCCA Academic Score:"})," ",e.academicScore,"%"]})]}),(0,a.jsx)("div",{children:(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCB0 Family Income:"})," ",e.familyIncome]})})]}),(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-1",children:"Documents Submitted:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.documents.map((e,s)=>(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded",children:e},s))})]})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"Review Application"}),"Ready for Approval"===e.status&&(0,a.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm",children:"Approve"}),(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"View Documents"})]})]})},s))})})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsx)(i.ll,{children:"Aid Distribution"}),(0,a.jsx)(i.SZ,{children:"Financial aid by program type"})]}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{program:"Need-Based Aid",amount:"₹4.2M",students:34,percentage:34},{program:"Sibling Discount",amount:"₹3.8M",students:45,percentage:31},{program:"Merit Scholarship",amount:"₹2.8M",students:23,percentage:23},{program:"Staff Ward Concession",amount:"₹2.1M",students:18,percentage:17},{program:"Sports Scholarship",amount:"₹1.1M",students:12,percentage:9}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e.program}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.students," students"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e.amount}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.percentage,"%")}})}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[e.percentage,"%"]})]})]})]},s))})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsx)(i.ll,{children:"Aid Impact"}),(0,a.jsx)(i.SZ,{children:"Financial assistance impact metrics"})]}),(0,a.jsx)(i.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-green-600 mb-1",children:"18%"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Students Receiving Aid"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"89 out of 495 students"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Total Aid Budget"}),(0,a.jsx)("span",{className:"font-medium",children:"₹15.0M"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Aid Distributed"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:"₹12.4M"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Remaining Budget"}),(0,a.jsx)("span",{className:"font-medium text-blue-600",children:"₹2.6M"})]})]}),(0,a.jsxs)("div",{className:"pt-3 border-t",children:[(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:"bg-green-600 h-3 rounded-full",style:{width:"83%"}})}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"83% budget utilized"})]})]})})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsx)(i.ll,{children:"Financial Aid Tools"}),(0,a.jsx)(i.SZ,{children:"Tools for managing scholarships and financial assistance"})]}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[{tool:"Eligibility Checker",description:"Check student eligibility",icon:"✅"},{tool:"Aid Calculator",description:"Calculate aid amounts",icon:"\uD83E\uDDEE"},{tool:"Bulk Aid Processing",description:"Process multiple applications",icon:"\uD83D\uDCCB"},{tool:"Aid Reports",description:"Generate aid reports",icon:"\uD83D\uDCCA"}].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-lg p-4 text-center hover:shadow-md transition-shadow",children:[(0,a.jsx)("div",{className:"text-3xl mb-2",children:e.icon}),(0,a.jsx)("h3",{className:"font-semibold mb-1",children:e.tool}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Use Tool"})]},s))})})]})]})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return d},aY:function(){return m},SZ:function(){return x},Ol:function(){return c},ll:function(){return o}});var a=t(7437),i=t(2265),l=t(7042),r=t(4769);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,l.W)(s))}let d=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("div",{ref:s,className:n("rounded-lg border bg-card text-card-foreground shadow-sm",t),...i})});d.displayName="Card";let c=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("div",{ref:s,className:n("flex flex-col space-y-1.5 p-6",t),...i})});c.displayName="CardHeader";let o=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("h3",{ref:s,className:n("text-2xl font-semibold leading-none tracking-tight",t),...i})});o.displayName="CardTitle";let x=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("p",{ref:s,className:n("text-sm text-muted-foreground",t),...i})});x.displayName="CardDescription";let m=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("div",{ref:s,className:n("p-6 pt-0",t),...i})});m.displayName="CardContent",i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("div",{ref:s,className:n("flex items-center p-6 pt-0",t),...i})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=5894)}),_N_E=e.O()}]);