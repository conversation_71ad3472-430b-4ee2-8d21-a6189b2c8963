(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4656],{8580:function(e,s,l){Promise.resolve().then(l.bind(l,6184))},6184:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return r}});var t=l(7437);l(2265);var a=l(9197);function r(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Billing Management"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Generate and manage student fee bills and invoices"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Bulk Generate"}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Create Bill"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Bills Generated"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC4"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"1,234"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Total Billed"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"₹45.6M"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Current term"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Pending Bills"}),(0,t.jsx)("span",{className:"text-2xl",children:"⏳"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"156"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Awaiting generation"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Overdue Bills"}),(0,t.jsx)("span",{className:"text-2xl",children:"⚠️"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"23"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Past due date"})]})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Recent Bills"}),(0,t.jsx)(a.SZ,{children:"Latest generated bills and their status"})]}),(0,t.jsx)(a.aY,{children:(0,t.jsx)("div",{className:"space-y-4",children:[{billNo:"BILL-2024-001234",studentName:"Aarav Sharma",rollNo:"9A001",grade:"Grade 9",billDate:"2024-02-15",dueDate:"2024-03-15",amount:29500,status:"Sent",items:["Tuition Fee","Development Fee","Library Fee"]},{billNo:"BILL-2024-001235",studentName:"Kavya Gupta",rollNo:"9A002",grade:"Grade 9",billDate:"2024-02-15",dueDate:"2024-03-15",amount:41500,status:"Paid",items:["Tuition Fee","Development Fee","Transport Fee"]},{billNo:"BILL-2024-001236",studentName:"Arjun Singh",rollNo:"11A003",grade:"Grade 11",billDate:"2024-02-14",dueDate:"2024-03-14",amount:33250,status:"Overdue",items:["Tuition Fee","Exam Fee","Sports Fee"]},{billNo:"BILL-2024-001237",studentName:"Riya Patel",rollNo:"6B004",grade:"Grade 6",billDate:"2024-02-14",dueDate:"2024-03-14",amount:25750,status:"Draft",items:["Tuition Fee","Development Fee"]}].map((e,s)=>(0,t.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:e.studentName}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e.grade}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Paid"===e.status?"bg-green-100 text-green-800":"Sent"===e.status?"bg-blue-100 text-blue-800":"Overdue"===e.status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:e.status})]}),(0,t.jsx)("p",{className:"text-blue-600 font-medium",children:e.billNo}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.rollNo}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCC5 Bill Date:"})," ",e.billDate]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"⏰ Due Date:"})," ",e.dueDate]})]}),(0,t.jsx)("div",{children:(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCB0 Amount:"})," ₹",e.amount.toLocaleString()]})})]}),(0,t.jsxs)("div",{className:"mt-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-1",children:"Bill Items:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.items.map((e,s)=>(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded",children:e},s))})]})]}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"View Bill"}),"Draft"===e.status&&(0,t.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm",children:"Send Bill"}),"Sent"===e.status&&(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"Resend"}),(0,t.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm",children:"Download PDF"})]})]})},s))})})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Generate New Bill"}),(0,t.jsx)(a.SZ,{children:"Create individual or bulk bills for students"})]}),(0,t.jsx)(a.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Student Selection"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Option"}),(0,t.jsx)("option",{value:"individual",children:"Individual Student"}),(0,t.jsx)("option",{value:"class",children:"Entire Class"}),(0,t.jsx)("option",{value:"grade",children:"Entire Grade"}),(0,t.jsx)("option",{value:"all",children:"All Students"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Bill Type"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Type"}),(0,t.jsx)("option",{value:"term",children:"Term Fee Bill"}),(0,t.jsx)("option",{value:"monthly",children:"Monthly Fee Bill"}),(0,t.jsx)("option",{value:"annual",children:"Annual Fee Bill"}),(0,t.jsx)("option",{value:"custom",children:"Custom Bill"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Due Date"}),(0,t.jsx)("input",{type:"date",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Fee Categories to Include"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2 mt-2",children:["Tuition Fee","Development Fee","Exam Fee","Library Fee","Sports Fee","Transport Fee","Late Fee","Other Charges"].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",className:"rounded",defaultChecked:s<4}),(0,t.jsx)("label",{className:"text-sm",children:e})]},s))})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Bill Template"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Template"}),(0,t.jsx)("option",{value:"standard",children:"Standard Bill Template"}),(0,t.jsx)("option",{value:"detailed",children:"Detailed Bill Template"}),(0,t.jsx)("option",{value:"summary",children:"Summary Bill Template"}),(0,t.jsx)("option",{value:"custom",children:"Custom Template"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Delivery Method"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Method"}),(0,t.jsx)("option",{value:"email",children:"Email to Parents"}),(0,t.jsx)("option",{value:"sms",children:"SMS Notification"}),(0,t.jsx)("option",{value:"portal",children:"Student Portal"}),(0,t.jsx)("option",{value:"print",children:"Print for Distribution"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Additional Notes"}),(0,t.jsx)("textarea",{rows:3,placeholder:"Add any special instructions or notes for this bill...",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Generate Bills"}),(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Preview Template"}),(0,t.jsx)("button",{className:"text-blue-600 hover:text-blue-800",children:"Save as Draft"})]})]})})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Billing Summary"}),(0,t.jsx)(a.SZ,{children:"Current month billing overview"})]}),(0,t.jsx)(a.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{status:"Bills Sent",count:1089,amount:"₹42.3M",color:"bg-blue-100 text-blue-800"},{status:"Bills Paid",count:934,amount:"₹36.8M",color:"bg-green-100 text-green-800"},{status:"Bills Overdue",count:23,amount:"₹1.2M",color:"bg-red-100 text-red-800"},{status:"Bills Draft",count:156,amount:"₹6.1M",color:"bg-gray-100 text-gray-800"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:e.status}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.count," bills"]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(e.color),children:e.amount})})]},s))})})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Billing Tools"}),(0,t.jsx)(a.SZ,{children:"Quick actions and utilities"})]}),(0,t.jsx)(a.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{tool:"Bulk Bill Generator",description:"Generate multiple bills at once",icon:"\uD83D\uDCC4"},{tool:"Payment Reminder",description:"Send payment reminders",icon:"\uD83D\uDCE7"},{tool:"Bill Templates",description:"Manage bill templates",icon:"\uD83D\uDCCB"},{tool:"Late Fee Calculator",description:"Calculate late fees automatically",icon:"\uD83E\uDDEE"},{tool:"Bill Reconciliation",description:"Reconcile bills with payments",icon:"✅"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:"text-2xl",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-sm",children:e.tool}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:e.description})]})]}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Use"})]},s))})})]})]})]})}},9197:function(e,s,l){"use strict";l.d(s,{Zb:function(){return d},aY:function(){return m},SZ:function(){return x},Ol:function(){return c},ll:function(){return o}});var t=l(7437),a=l(2265),r=l(7042),i=l(4769);function n(){for(var e=arguments.length,s=Array(e),l=0;l<e;l++)s[l]=arguments[l];return(0,i.m6)((0,r.W)(s))}let d=a.forwardRef((e,s)=>{let{className:l,...a}=e;return(0,t.jsx)("div",{ref:s,className:n("rounded-lg border bg-card text-card-foreground shadow-sm",l),...a})});d.displayName="Card";let c=a.forwardRef((e,s)=>{let{className:l,...a}=e;return(0,t.jsx)("div",{ref:s,className:n("flex flex-col space-y-1.5 p-6",l),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,s)=>{let{className:l,...a}=e;return(0,t.jsx)("h3",{ref:s,className:n("text-2xl font-semibold leading-none tracking-tight",l),...a})});o.displayName="CardTitle";let x=a.forwardRef((e,s)=>{let{className:l,...a}=e;return(0,t.jsx)("p",{ref:s,className:n("text-sm text-muted-foreground",l),...a})});x.displayName="CardDescription";let m=a.forwardRef((e,s)=>{let{className:l,...a}=e;return(0,t.jsx)("div",{ref:s,className:n("p-6 pt-0",l),...a})});m.displayName="CardContent",a.forwardRef((e,s)=>{let{className:l,...a}=e;return(0,t.jsx)("div",{ref:s,className:n("flex items-center p-6 pt-0",l),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=8580)}),_N_E=e.O()}]);