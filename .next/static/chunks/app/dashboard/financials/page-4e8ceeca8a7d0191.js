(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9319],{615:function(e,s,a){Promise.resolve().then(a.bind(a,2432))},2432:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return i}});var t=a(7437);a(2265);var r=a(1396),l=a.n(r),n=a(9197);function i(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Student Financials"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage fee structure, billing, payments, and financial aid"})]})}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Revenue"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"₹2.4M"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"This academic year"})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Pending Payments"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC4"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"₹180K"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"45 students"})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Collection Rate"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB3"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"92%"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Financial Aid"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"₹320K"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"78 recipients"})]})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[(0,t.jsx)(l(),{href:"/dashboard/financials/fee-structure",children:(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"}),(0,t.jsx)(n.ll,{className:"text-lg",children:"Fee Structure"})]})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Define and manage fee structures"}),(0,t.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Fees →"})]})]})}),(0,t.jsx)(l(),{href:"/dashboard/financials/billing",children:(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC4"}),(0,t.jsx)(n.ll,{className:"text-lg",children:"Billing"})]})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Generate and manage student bills"}),(0,t.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Billing →"})]})]})}),(0,t.jsx)(l(),{href:"/dashboard/financials/payments",children:(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB3"}),(0,t.jsx)(n.ll,{className:"text-lg",children:"Payments"})]})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Process and track payments"}),(0,t.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Payments →"})]})]})}),(0,t.jsx)(l(),{href:"/dashboard/financials/financial-aid",children:(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"}),(0,t.jsx)(n.ll,{className:"text-lg",children:"Financial Aid"})]})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage scholarships and financial aid"}),(0,t.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Aid →"})]})]})}),(0,t.jsx)(l(),{href:"/dashboard/financials/reports",children:(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,t.jsx)(n.ll,{className:"text-lg",children:"Reports"})]})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Generate financial reports and analytics"}),(0,t.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Reports →"})]})]})}),(0,t.jsx)(l(),{href:"/dashboard/financials/history",children:(0,t.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,t.jsx)(n.Ol,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCB"}),(0,t.jsx)(n.ll,{className:"text-lg",children:"Payment History"})]})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"View payment history and transactions"}),(0,t.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View History →"})]})]})})]})]})}},9197:function(e,s,a){"use strict";a.d(s,{Zb:function(){return c},aY:function(){return o},SZ:function(){return m},Ol:function(){return d},ll:function(){return x}});var t=a(7437),r=a(2265),l=a(7042),n=a(4769);function i(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,n.m6)((0,l.W)(s))}let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});c.displayName="Card";let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",a),...r})});d.displayName="CardHeader";let x=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",a),...r})});x.displayName="CardTitle";let m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",a),...r})});m.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:i("p-6 pt-0",a),...r})});o.displayName="CardContent",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",a),...r})}).displayName="CardFooter"},1396:function(e,s,a){e.exports=a(5250)}},function(e){e.O(0,[7895,5250,2971,4938,1744],function(){return e(e.s=615)}),_N_E=e.O()}]);