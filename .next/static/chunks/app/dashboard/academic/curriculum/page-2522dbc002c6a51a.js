(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3887],{9497:function(e,s,t){Promise.resolve().then(t.bind(t,8640))},8640:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return l}});var a=t(7437);t(2265);var r=t(9197);function l(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Curriculum Management"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage course curriculum, syllabus, and learning objectives"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Import Curriculum"}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Add Subject"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Total Subjects"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDA"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"45"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Across all grades"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Learning Objectives"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFAF"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"1,234"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Defined objectives"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Curriculum Updates"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD04"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"12"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"This semester"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Completion Rate"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC8"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"87%"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Average progress"})]})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"Curriculum by Grade Level"}),(0,a.jsx)(r.SZ,{children:"Subject-wise curriculum overview"})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{grade:"Grade 9",subjects:[{name:"Mathematics",chapters:15,completed:12,teacher:"Dr. Priya Patel"},{name:"Science",chapters:18,completed:14,teacher:"Mr. Rajesh Kumar"},{name:"English",chapters:12,completed:10,teacher:"Ms. Sneha Gupta"},{name:"Social Studies",chapters:16,completed:13,teacher:"Dr. Amit Verma"}]},{grade:"Grade 10",subjects:[{name:"Mathematics",chapters:16,completed:11,teacher:"Dr. Priya Patel"},{name:"Physics",chapters:14,completed:9,teacher:"Mr. Rajesh Kumar"},{name:"Chemistry",chapters:12,completed:8,teacher:"Dr. Kavya Singh"},{name:"Biology",chapters:15,completed:10,teacher:"Ms. Riya Sharma"}]}].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:e.grade}),(0,a.jsx)("div",{className:"grid gap-3 md:grid-cols-2",children:e.subjects.map((e,s)=>(0,a.jsxs)("div",{className:"border rounded p-3 hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.name}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[e.completed,"/",e.chapters," chapters"]})]}),(0,a.jsx)("p",{className:"text-sm text-blue-600 mb-2",children:e.teacher}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.completed/e.chapters*100,"%")}})}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:[Math.round(e.completed/e.chapters*100),"% complete"]}),(0,a.jsxs)("span",{children:[e.chapters-e.completed," remaining"]})]}),(0,a.jsxs)("div",{className:"flex space-x-2 mt-2",children:[(0,a.jsx)("button",{className:"bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700",children:"View Syllabus"}),(0,a.jsx)("button",{className:"border border-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-50",children:"Edit"})]})]},s))})]},s))})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"Subject Curriculum Details"}),(0,a.jsx)(r.SZ,{children:"Detailed curriculum breakdown for Mathematics - Grade 9"})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{unit:"Unit 1: Number Systems",chapters:["Real Numbers","Polynomials"],objectives:["Understand rational and irrational numbers","Perform operations on polynomials"],duration:"3 weeks",status:"Completed",assessments:2},{unit:"Unit 2: Algebra",chapters:["Linear Equations","Coordinate Geometry"],objectives:["Solve linear equations in two variables","Plot points on coordinate plane"],duration:"4 weeks",status:"In Progress",assessments:1},{unit:"Unit 3: Geometry",chapters:["Triangles","Quadrilaterals","Circles"],objectives:["Prove triangle congruence","Understand properties of quadrilaterals"],duration:"5 weeks",status:"Upcoming",assessments:3}].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold",children:e.unit}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Completed"===e.status?"bg-green-100 text-green-800":"In Progress"===e.status?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:e.status})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium mb-2",children:"Chapters"}),(0,a.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:e.chapters.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-blue-600 rounded-full"}),(0,a.jsx)("span",{children:e})]},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium mb-2",children:"Learning Objectives"}),(0,a.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:e.objectives.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-green-600 rounded-full"}),(0,a.jsx)("span",{children:e})]},s))})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-4 pt-3 border-t",children:[(0,a.jsxs)("div",{className:"flex space-x-4 text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Duration:"})," ",e.duration]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Assessments:"})," ",e.assessments]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"View Details"}),(0,a.jsx)("button",{className:"border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50",children:"Edit Unit"})]})]})]},s))})})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"Curriculum Tools"}),(0,a.jsx)(r.SZ,{children:"Manage curriculum resources and tools"})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{tool:"Lesson Plan Generator",description:"Create structured lesson plans",icon:"\uD83D\uDCDD"},{tool:"Learning Objective Mapper",description:"Map objectives to assessments",icon:"\uD83C\uDFAF"},{tool:"Progress Tracker",description:"Track curriculum completion",icon:"\uD83D\uDCCA"},{tool:"Resource Library",description:"Access teaching resources",icon:"\uD83D\uDCDA"},{tool:"Standards Alignment",description:"Align with education standards",icon:"✅"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between border rounded p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.tool}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:e.description})]})]}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Use Tool"})]},s))})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"Recent Updates"}),(0,a.jsx)(r.SZ,{children:"Latest curriculum changes and updates"})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{subject:"Mathematics Grade 10",update:"Added new chapter on Statistics",date:"2024-02-15",teacher:"Dr. Priya Patel"},{subject:"Science Grade 9",update:"Updated lab experiments list",date:"2024-02-14",teacher:"Mr. Rajesh Kumar"},{subject:"English Grade 11",update:"Revised literature syllabus",date:"2024-02-13",teacher:"Ms. Sneha Gupta"},{subject:"Social Studies Grade 8",update:"Added current affairs section",date:"2024-02-12",teacher:"Dr. Amit Verma"}].map((e,s)=>(0,a.jsx)("div",{className:"border-b pb-2",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.subject}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:e.update}),(0,a.jsx)("p",{className:"text-xs text-blue-600",children:e.teacher})]}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:e.date})]})},s))})})]})]})]})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return i},aY:function(){return o},SZ:function(){return m},Ol:function(){return d},ll:function(){return x}});var a=t(7437),r=t(2265),l=t(7042),c=t(4769);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,c.m6)((0,l.W)(s))}let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:n("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});i.displayName="Card";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:n("flex flex-col space-y-1.5 p-6",t),...r})});d.displayName="CardHeader";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:n("text-2xl font-semibold leading-none tracking-tight",t),...r})});x.displayName="CardTitle";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:n("text-sm text-muted-foreground",t),...r})});m.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:n("p-6 pt-0",t),...r})});o.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:n("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=9497)}),_N_E=e.O()}]);