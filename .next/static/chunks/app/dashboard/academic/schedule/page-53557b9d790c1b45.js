(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2990],{2527:function(e,s,a){Promise.resolve().then(a.bind(a,4394))},4394:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return d}});var t=a(7437);a(2265);var r=a(9197);function d(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Academic Schedule"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage class schedules, timetables, and academic calendar"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Generate Timetable"}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Add Schedule"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.ll,{className:"text-sm font-medium",children:"Total Classes"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFEB"})]}),(0,t.jsxs)(r.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"156"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Per week"})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.ll,{className:"text-sm font-medium",children:"Active Teachers"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC68‍\uD83C\uDFEB"})]}),(0,t.jsxs)(r.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"24"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Teaching this week"})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.ll,{className:"text-sm font-medium",children:"Classrooms"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDEAA"})]}),(0,t.jsxs)(r.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"18"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available rooms"})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.ll,{className:"text-sm font-medium",children:"Utilization"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,t.jsxs)(r.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"87%"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Room utilization"})]})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{children:[(0,t.jsx)(r.ll,{children:"Weekly Timetable - Grade 9A"}),(0,t.jsx)(r.SZ,{children:"Current week schedule for Grade 9A"})]}),(0,t.jsx)(r.aY,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full border-collapse",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-left p-2 font-medium",children:"Time"}),(0,t.jsx)("th",{className:"text-left p-2 font-medium",children:"Monday"}),(0,t.jsx)("th",{className:"text-left p-2 font-medium",children:"Tuesday"}),(0,t.jsx)("th",{className:"text-left p-2 font-medium",children:"Wednesday"}),(0,t.jsx)("th",{className:"text-left p-2 font-medium",children:"Thursday"}),(0,t.jsx)("th",{className:"text-left p-2 font-medium",children:"Friday"})]})}),(0,t.jsx)("tbody",{children:[{time:"8:00-8:45",monday:{subject:"Mathematics",teacher:"Dr. Priya",room:"Room 101"},tuesday:{subject:"English",teacher:"Ms. Sneha",room:"Room 102"},wednesday:{subject:"Science",teacher:"Mr. Rajesh",room:"Lab 1"},thursday:{subject:"Mathematics",teacher:"Dr. Priya",room:"Room 101"},friday:{subject:"Social Studies",teacher:"Dr. Amit",room:"Room 103"}},{time:"8:45-9:30",monday:{subject:"English",teacher:"Ms. Sneha",room:"Room 102"},tuesday:{subject:"Science",teacher:"Mr. Rajesh",room:"Lab 1"},wednesday:{subject:"Mathematics",teacher:"Dr. Priya",room:"Room 101"},thursday:{subject:"Hindi",teacher:"Mrs. Kavya",room:"Room 104"},friday:{subject:"English",teacher:"Ms. Sneha",room:"Room 102"}},{time:"9:30-9:45",monday:{subject:"Break",teacher:"",room:""},tuesday:{subject:"Break",teacher:"",room:""},wednesday:{subject:"Break",teacher:"",room:""},thursday:{subject:"Break",teacher:"",room:""},friday:{subject:"Break",teacher:"",room:""}},{time:"9:45-10:30",monday:{subject:"Science",teacher:"Mr. Rajesh",room:"Lab 1"},tuesday:{subject:"Mathematics",teacher:"Dr. Priya",room:"Room 101"},wednesday:{subject:"Hindi",teacher:"Mrs. Kavya",room:"Room 104"},thursday:{subject:"English",teacher:"Ms. Sneha",room:"Room 102"},friday:{subject:"Science",teacher:"Mr. Rajesh",room:"Lab 1"}},{time:"10:30-11:15",monday:{subject:"Social Studies",teacher:"Dr. Amit",room:"Room 103"},tuesday:{subject:"Hindi",teacher:"Mrs. Kavya",room:"Room 104"},wednesday:{subject:"PE",teacher:"Mr. Vikram",room:"Playground"},thursday:{subject:"Science",teacher:"Mr. Rajesh",room:"Lab 1"},friday:{subject:"Mathematics",teacher:"Dr. Priya",room:"Room 101"}}].map((e,s)=>(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"p-2 font-medium text-sm",children:e.time}),["monday","tuesday","wednesday","thursday","friday"].map(s=>{let a=e[s];return(0,t.jsx)("td",{className:"p-2",children:"Break"===a.subject?(0,t.jsx)("div",{className:"text-center text-gray-500 text-sm",children:"Break"}):(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("div",{className:"font-medium text-blue-600",children:a.subject}),(0,t.jsx)("div",{className:"text-gray-600",children:a.teacher}),(0,t.jsx)("div",{className:"text-gray-500 text-xs",children:a.room})]})},s)})]},s))})]})})})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{children:[(0,t.jsx)(r.ll,{children:"Class Schedule Overview"}),(0,t.jsx)(r.SZ,{children:"Schedule summary by grade"})]}),(0,t.jsx)(r.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{grade:"Grade 9",sections:4,periods:35,teachers:8,utilization:92},{grade:"Grade 10",sections:4,periods:35,teachers:9,utilization:88},{grade:"Grade 11",sections:3,periods:30,teachers:7,utilization:85},{grade:"Grade 12",sections:3,periods:30,teachers:7,utilization:90}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.grade}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[e.utilization,"% utilized"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-sm text-gray-600",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Sections:"})," ",e.sections]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Periods:"})," ",e.periods,"/week"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Teachers:"})," ",e.teachers]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.utilization,"%")}})})]},s))})})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{children:[(0,t.jsx)(r.ll,{children:"Teacher Schedule"}),(0,t.jsx)(r.SZ,{children:"Teaching load distribution"})]}),(0,t.jsx)(r.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{teacher:"Dr. Priya Patel",subject:"Mathematics",periods:18,grades:["9","10"],load:90},{teacher:"Mr. Rajesh Kumar",subject:"Science",periods:16,grades:["9","11"],load:80},{teacher:"Ms. Sneha Gupta",subject:"English",periods:20,grades:["9","10","11"],load:100},{teacher:"Dr. Amit Verma",subject:"Social Studies",periods:14,grades:["9","10"],load:70}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:e.teacher}),(0,t.jsx)("p",{className:"text-xs text-blue-600",children:e.subject})]}),(0,t.jsxs)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(100===e.load?"bg-red-100 text-red-800":e.load>=80?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:[e.load,"% load"]})]}),(0,t.jsx)("div",{className:"text-xs text-gray-600",children:(0,t.jsxs)("span",{children:[e.periods," periods/week • Grades: ",e.grades.join(", ")]})}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-1 mt-2",children:(0,t.jsx)("div",{className:"h-1 rounded-full ".concat(100===e.load?"bg-red-600":e.load>=80?"bg-yellow-600":"bg-green-600"),style:{width:"".concat(e.load,"%")}})})]},s))})})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsxs)(r.Ol,{children:[(0,t.jsx)(r.ll,{children:"Academic Calendar"}),(0,t.jsx)(r.SZ,{children:"Important academic dates and events"})]}),(0,t.jsx)(r.aY,{children:(0,t.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[{event:"Mid-term Examinations",date:"March 15-25, 2024",type:"Exam",status:"Upcoming",grades:"All Grades"},{event:"Parent-Teacher Meeting",date:"March 30, 2024",type:"Meeting",status:"Scheduled",grades:"All Grades"},{event:"Science Fair",date:"April 10, 2024",type:"Event",status:"Planning",grades:"Grades 6-12"},{event:"Annual Sports Day",date:"April 20, 2024",type:"Event",status:"Planning",grades:"All Grades"},{event:"Final Examinations",date:"May 1-15, 2024",type:"Exam",status:"Scheduled",grades:"All Grades"},{event:"Summer Vacation",date:"May 20 - June 15, 2024",type:"Holiday",status:"Scheduled",grades:"All Grades"}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:e.event}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Exam"===e.type?"bg-red-100 text-red-800":"Meeting"===e.type?"bg-blue-100 text-blue-800":"Event"===e.type?"bg-green-100 text-green-800":"bg-purple-100 text-purple-800"),children:e.type})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:e.date}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:e.grades}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Upcoming"===e.status?"bg-yellow-100 text-yellow-800":"Scheduled"===e.status?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:e.status})]},s))})})]})]})}},9197:function(e,s,a){"use strict";a.d(s,{Zb:function(){return i},aY:function(){return x},SZ:function(){return m},Ol:function(){return n},ll:function(){return o}});var t=a(7437),r=a(2265),d=a(7042),l=a(4769);function c(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,l.m6)((0,d.W)(s))}let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:c("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});i.displayName="Card";let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:c("flex flex-col space-y-1.5 p-6",a),...r})});n.displayName="CardHeader";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h3",{ref:s,className:c("text-2xl font-semibold leading-none tracking-tight",a),...r})});o.displayName="CardTitle";let m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("p",{ref:s,className:c("text-sm text-muted-foreground",a),...r})});m.displayName="CardDescription";let x=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:c("p-6 pt-0",a),...r})});x.displayName="CardContent",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:c("flex items-center p-6 pt-0",a),...r})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=2527)}),_N_E=e.O()}]);