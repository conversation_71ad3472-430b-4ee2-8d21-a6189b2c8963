(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2992],{3122:function(e,s,r){Promise.resolve().then(r.bind(r,1120))},1120:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return l}});var t=r(7437);r(2265);var a=r(9197);function l(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Grade Management"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Record, manage, and analyze student grades and academic performance"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Export Grades"}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Enter Grades"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Pending Grades"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"156"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Awaiting entry"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Class Average"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"78%"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Overall performance"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Top Performers"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFC6"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"23"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Above 90%"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Need Support"}),(0,t.jsx)("span",{className:"text-2xl",children:"⚠️"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"12"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Below 60%"})]})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Grade Entry - Mathematics Grade 9A"}),(0,t.jsx)(a.SZ,{children:"Enter grades for Unit Test 2 - Polynomials"})]}),(0,t.jsx)(a.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Assessment"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Assessment"}),(0,t.jsx)("option",{value:"unit-test-2",children:"Unit Test 2 - Polynomials"}),(0,t.jsx)("option",{value:"mid-term",children:"Mid-term Examination"}),(0,t.jsx)("option",{value:"assignment-1",children:"Assignment 1"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Total Marks"}),(0,t.jsx)("input",{type:"number",value:"100",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Date"}),(0,t.jsx)("input",{type:"date",value:"2024-02-15",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Weightage"}),(0,t.jsx)("input",{type:"number",value:"20",placeholder:"20%",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]})]}),(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full border-collapse",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-left p-2 font-medium",children:"Roll No."}),(0,t.jsx)("th",{className:"text-left p-2 font-medium",children:"Student Name"}),(0,t.jsx)("th",{className:"text-left p-2 font-medium",children:"Marks Obtained"}),(0,t.jsx)("th",{className:"text-left p-2 font-medium",children:"Percentage"}),(0,t.jsx)("th",{className:"text-left p-2 font-medium",children:"Grade"}),(0,t.jsx)("th",{className:"text-left p-2 font-medium",children:"Remarks"})]})}),(0,t.jsx)("tbody",{children:[{rollNo:"9A001",name:"Aarav Sharma",marks:85,percentage:85,grade:"A",remarks:"Excellent"},{rollNo:"9A002",name:"Kavya Gupta",marks:78,percentage:78,grade:"B+",remarks:"Good"},{rollNo:"9A003",name:"Arjun Singh",marks:92,percentage:92,grade:"A+",remarks:"Outstanding"},{rollNo:"9A004",name:"Riya Patel",marks:65,percentage:65,grade:"B",remarks:"Satisfactory"},{rollNo:"9A005",name:"Vikram Kumar",marks:58,percentage:58,grade:"C+",remarks:"Needs improvement"}].map((e,s)=>(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"p-2 font-medium",children:e.rollNo}),(0,t.jsx)("td",{className:"p-2",children:e.name}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)("input",{type:"number",value:e.marks,className:"w-20 px-2 py-1 border border-gray-300 rounded text-sm",max:"100",min:"0"})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsxs)("span",{className:"font-medium ".concat(e.percentage>=90?"text-green-600":e.percentage>=75?"text-blue-600":e.percentage>=60?"text-yellow-600":"text-red-600"),children:[e.percentage,"%"]})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("A+"===e.grade?"bg-green-100 text-green-800":"A"===e.grade?"bg-blue-100 text-blue-800":e.grade.startsWith("B")?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.grade})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)("input",{type:"text",value:e.remarks,className:"w-32 px-2 py-1 border border-gray-300 rounded text-sm",placeholder:"Remarks"})})]},s))})]})}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Save Grades"}),(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Save as Draft"}),(0,t.jsx)("button",{className:"text-blue-600 hover:text-blue-800",children:"Auto Calculate"})]})]})})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Grade Distribution"}),(0,t.jsx)(a.SZ,{children:"Performance distribution across grades"})]}),(0,t.jsx)(a.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{grade:"A+ (90-100%)",count:23,percentage:19,color:"bg-green-600"},{grade:"A (80-89%)",count:45,percentage:37,color:"bg-blue-600"},{grade:"B+ (70-79%)",count:32,percentage:26,color:"bg-yellow-600"},{grade:"B (60-69%)",count:15,percentage:12,color:"bg-orange-600"},{grade:"C+ (50-59%)",count:6,percentage:5,color:"bg-red-600"},{grade:"Below 50%",count:1,percentage:1,color:"bg-gray-600"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded ".concat(e.color)}),(0,t.jsx)("span",{className:"text-sm font-medium",children:e.grade})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[e.count," students"]}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[e.percentage,"%"]})]})]},s))})})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Subject Performance"}),(0,t.jsx)(a.SZ,{children:"Average performance by subject"})]}),(0,t.jsx)(a.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{subject:"Mathematics",average:78,trend:"+2%",students:120},{subject:"Science",average:82,trend:"+5%",students:118},{subject:"English",average:75,trend:"-1%",students:122},{subject:"Social Studies",average:80,trend:"+3%",students:119},{subject:"Hindi",average:77,trend:"0%",students:121}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:e.subject}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-sm font-medium",children:[e.average,"%"]}),(0,t.jsx)("span",{className:"text-xs ".concat(e.trend.startsWith("+")?"text-green-600":e.trend.startsWith("-")?"text-red-600":"text-gray-600"),children:e.trend})]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-1",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.average,"%")}})}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.students," students"]})]},s))})})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Individual Student Performance"}),(0,t.jsx)(a.SZ,{children:"Detailed performance tracking for Grade 9A"})]}),(0,t.jsx)(a.aY,{children:(0,t.jsx)("div",{className:"space-y-4",children:[{name:"Aarav Sharma",rollNo:"9A001",overall:85,subjects:{mathematics:85,science:88,english:82,social:87,hindi:83},trend:"Improving",rank:3},{name:"Kavya Gupta",rollNo:"9A002",overall:78,subjects:{mathematics:78,science:80,english:75,social:79,hindi:78},trend:"Stable",rank:8},{name:"Arjun Singh",rollNo:"9A003",overall:92,subjects:{mathematics:92,science:94,english:89,social:93,hindi:90},trend:"Excellent",rank:1}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.rollNo," • Rank: ",e.rank]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"text-lg font-bold text-blue-600",children:[e.overall,"%"]}),(0,t.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat("Excellent"===e.trend?"bg-green-100 text-green-800":"Improving"===e.trend?"bg-blue-100 text-blue-800":"Stable"===e.trend?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.trend})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-5 gap-2 text-sm",children:Object.entries(e.subjects).map(e=>{let[s,r]=e;return(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-xs text-gray-600 capitalize",children:s}),(0,t.jsxs)("p",{className:"font-medium ".concat(r>=90?"text-green-600":r>=80?"text-blue-600":r>=70?"text-yellow-600":"text-red-600"),children:[r,"%"]})]},s)})})]},s))})})]})]})}},9197:function(e,s,r){"use strict";r.d(s,{Zb:function(){return c},aY:function(){return o},SZ:function(){return m},Ol:function(){return i},ll:function(){return x}});var t=r(7437),a=r(2265),l=r(7042),n=r(4769);function d(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,n.m6)((0,l.W)(s))}let c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});c.displayName="Card";let i=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:d("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let x=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h3",{ref:s,className:d("text-2xl font-semibold leading-none tracking-tight",r),...a})});x.displayName="CardTitle";let m=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("p",{ref:s,className:d("text-sm text-muted-foreground",r),...a})});m.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:d("p-6 pt-0",r),...a})});o.displayName="CardContent",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:d("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=3122)}),_N_E=e.O()}]);