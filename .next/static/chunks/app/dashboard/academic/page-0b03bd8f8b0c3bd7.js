(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2365],{7182:function(e,s,a){Promise.resolve().then(a.bind(a,9618))},9618:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return c}});var r=a(7437);a(2265);var t=a(1396),l=a.n(t),n=a(9197);function c(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Academic Management"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage curriculum, schedules, assessments, and academic progress"})]})}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ll,{className:"text-sm font-medium",children:"Active Courses"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDA"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"45"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"This semester"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ll,{className:"text-sm font-medium",children:"Upcoming Exams"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"12"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Next 2 weeks"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ll,{className:"text-sm font-medium",children:"Average Attendance"}),(0,r.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"92%"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ll,{className:"text-sm font-medium",children:"Grade Reports"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"156"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Pending review"})]})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsx)(l(),{href:"/dashboard/academic/curriculum",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDA"}),(0,r.jsx)(n.ll,{className:"text-lg",children:"Curriculum"})]})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage course curriculum and syllabus"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Curriculum →"})]})]})}),(0,r.jsx)(l(),{href:"/dashboard/academic/schedule",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC5"}),(0,r.jsx)(n.ll,{className:"text-lg",children:"Schedule"})]})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Create and manage class schedules"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Schedule →"})]})]})}),(0,r.jsx)(l(),{href:"/dashboard/academic/assessments",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"}),(0,r.jsx)(n.ll,{className:"text-lg",children:"Assessments"})]})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Create and manage exams and assessments"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Assessments →"})]})]})}),(0,r.jsx)(l(),{href:"/dashboard/academic/grades",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,r.jsx)(n.ll,{className:"text-lg",children:"Grades"})]})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Record and manage student grades"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Grades →"})]})]})}),(0,r.jsx)(l(),{href:"/dashboard/academic/attendance",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"✅"}),(0,r.jsx)(n.ll,{className:"text-lg",children:"Attendance"})]})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Track and manage student attendance"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Attendance →"})]})]})}),(0,r.jsx)(l(),{href:"/dashboard/academic/progress",children:(0,r.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC8"}),(0,r.jsx)(n.ll,{className:"text-lg",children:"Progress Reports"})]})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Generate student progress reports"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Progress →"})]})]})})]})]})}},9197:function(e,s,a){"use strict";a.d(s,{Zb:function(){return d},aY:function(){return o},SZ:function(){return m},Ol:function(){return i},ll:function(){return x}});var r=a(7437),t=a(2265),l=a(7042),n=a(4769);function c(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,n.m6)((0,l.W)(s))}let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:c("rounded-lg border bg-card text-card-foreground shadow-sm",a),...t})});d.displayName="Card";let i=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:c("flex flex-col space-y-1.5 p-6",a),...t})});i.displayName="CardHeader";let x=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("h3",{ref:s,className:c("text-2xl font-semibold leading-none tracking-tight",a),...t})});x.displayName="CardTitle";let m=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("p",{ref:s,className:c("text-sm text-muted-foreground",a),...t})});m.displayName="CardDescription";let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:c("p-6 pt-0",a),...t})});o.displayName="CardContent",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:c("flex items-center p-6 pt-0",a),...t})}).displayName="CardFooter"},1396:function(e,s,a){e.exports=a(5250)}},function(e){e.O(0,[7895,5250,2971,4938,1744],function(){return e(e.s=7182)}),_N_E=e.O()}]);