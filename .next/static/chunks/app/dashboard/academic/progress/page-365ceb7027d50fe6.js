(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5117],{8656:function(e,s,r){Promise.resolve().then(r.bind(r,4075))},4075:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return a}});var t=r(7437);r(2265);var l=r(9197);function a(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Student Progress Reports"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Generate comprehensive progress reports and track student development"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Bulk Generate"}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Generate Report"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ll,{className:"text-sm font-medium",children:"Reports Generated"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,t.jsxs)(l.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"234"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ll,{className:"text-sm font-medium",children:"Improving Students"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC8"})]}),(0,t.jsxs)(l.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"156"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Positive trend"})]})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ll,{className:"text-sm font-medium",children:"Need Support"}),(0,t.jsx)("span",{className:"text-2xl",children:"⚠️"})]}),(0,t.jsxs)(l.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"23"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Declining performance"})]})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ll,{className:"text-sm font-medium",children:"Parent Meetings"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC68‍\uD83D\uDC69‍\uD83D\uDC67‍\uD83D\uDC66"})]}),(0,t.jsxs)(l.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"45"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Scheduled"})]})]})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{children:[(0,t.jsx)(l.ll,{children:"Generate Progress Report"}),(0,t.jsx)(l.SZ,{children:"Create detailed progress reports for students"})]}),(0,t.jsxs)(l.aY,{children:[(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Student"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Student"}),(0,t.jsx)("option",{value:"aarav",children:"Aarav Sharma (9A001)"}),(0,t.jsx)("option",{value:"kavya",children:"Kavya Gupta (9A002)"}),(0,t.jsx)("option",{value:"arjun",children:"Arjun Singh (9A003)"}),(0,t.jsx)("option",{value:"riya",children:"Riya Patel (9A004)"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Report Period"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Period"}),(0,t.jsx)("option",{value:"monthly",children:"Monthly Report"}),(0,t.jsx)("option",{value:"quarterly",children:"Quarterly Report"}),(0,t.jsx)("option",{value:"mid-term",children:"Mid-term Report"}),(0,t.jsx)("option",{value:"annual",children:"Annual Report"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Report Type"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Type"}),(0,t.jsx)("option",{value:"comprehensive",children:"Comprehensive Report"}),(0,t.jsx)("option",{value:"academic-only",children:"Academic Performance Only"}),(0,t.jsx)("option",{value:"behavioral",children:"Behavioral Assessment"}),(0,t.jsx)("option",{value:"parent-summary",children:"Parent Summary"})]})]})]}),(0,t.jsxs)("div",{className:"mt-4 flex space-x-2",children:[(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Generate Report"}),(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Preview"}),(0,t.jsx)("button",{className:"text-blue-600 hover:text-blue-800",children:"Use Template"})]})]})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{children:[(0,t.jsx)(l.ll,{children:"Student Progress Overview - Aarav Sharma (9A001)"}),(0,t.jsx)(l.SZ,{children:"Comprehensive progress tracking for current semester"})]}),(0,t.jsx)(l.aY,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Academic Performance"}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsx)("div",{className:"space-y-3",children:[{subject:"Mathematics",current:85,previous:78,trend:"+7",grade:"A"},{subject:"Science",current:88,previous:85,trend:"+3",grade:"A"},{subject:"English",current:82,previous:80,trend:"+2",grade:"A-"}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.subject}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[e.current,"%"]}),(0,t.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(e.trend.startsWith("+")?"bg-green-100 text-green-800":e.trend.startsWith("-")?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:e.trend}),(0,t.jsxs)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:["Grade ",e.grade]})]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.current,"%")}})}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Previous: ",e.previous,"%"]})]},s))}),(0,t.jsx)("div",{className:"space-y-3",children:[{subject:"Social Studies",current:87,previous:82,trend:"+5",grade:"A"},{subject:"Hindi",current:83,previous:85,trend:"-2",grade:"A-"},{subject:"Physical Education",current:92,previous:90,trend:"+2",grade:"A+"}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.subject}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[e.current,"%"]}),(0,t.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(e.trend.startsWith("+")?"bg-green-100 text-green-800":e.trend.startsWith("-")?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:e.trend}),(0,t.jsxs)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:["Grade ",e.grade]})]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.current,"%")}})}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Previous: ",e.previous,"%"]})]},s))})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Attendance Record"}),(0,t.jsxs)("div",{className:"border rounded p-4",children:[(0,t.jsxs)("div",{className:"text-center mb-3",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-green-600",children:"94%"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Overall Attendance"})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Days Present:"}),(0,t.jsx)("span",{className:"font-medium",children:"47/50"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Days Absent:"}),(0,t.jsx)("span",{className:"font-medium",children:"3"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Late Arrivals:"}),(0,t.jsx)("span",{className:"font-medium",children:"2"})]})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Behavioral Assessment"}),(0,t.jsx)("div",{className:"border rounded p-4",children:(0,t.jsx)("div",{className:"space-y-3",children:[{aspect:"Classroom Participation",rating:4,max:5},{aspect:"Homework Completion",rating:5,max:5},{aspect:"Peer Interaction",rating:4,max:5},{aspect:"Following Instructions",rating:5,max:5}].map((e,s)=>(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:e.aspect}),(0,t.jsxs)("span",{className:"font-medium",children:[e.rating,"/",e.max]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"".concat(e.rating/e.max*100,"%")}})})]},s))})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Teacher Comments"}),(0,t.jsx)("div",{className:"space-y-3",children:[{teacher:"Dr. Priya Patel",subject:"Mathematics",comment:"Aarav shows excellent problem-solving skills and has improved significantly in algebra. Encourage continued practice with geometry."},{teacher:"Mr. Rajesh Kumar",subject:"Science",comment:"Very engaged in laboratory work and asks thoughtful questions. Strong understanding of scientific concepts."},{teacher:"Ms. Sneha Gupta",subject:"English",comment:"Good progress in creative writing. Needs to focus more on grammar and vocabulary building."}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.subject}),(0,t.jsx)("span",{className:"text-sm text-blue-600",children:e.teacher})]}),(0,t.jsx)("p",{className:"text-sm text-gray-700",children:e.comment})]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Recommendations"}),(0,t.jsx)("div",{className:"border rounded p-4 bg-blue-50",children:(0,t.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,t.jsx)("span",{className:"text-blue-600",children:"•"}),(0,t.jsx)("span",{children:"Continue with current study routine as it's showing positive results"})]}),(0,t.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,t.jsx)("span",{className:"text-blue-600",children:"•"}),(0,t.jsx)("span",{children:"Focus on improving Hindi language skills through additional reading"})]}),(0,t.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,t.jsx)("span",{className:"text-blue-600",children:"•"}),(0,t.jsx)("span",{children:"Consider joining the school science club to further develop interest in STEM"})]}),(0,t.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,t.jsx)("span",{className:"text-blue-600",children:"•"}),(0,t.jsx)("span",{children:"Maintain excellent attendance record and punctuality"})]})]})})]})]})})]}),(0,t.jsxs)(l.Zb,{children:[(0,t.jsxs)(l.Ol,{children:[(0,t.jsx)(l.ll,{children:"Class Progress Summary - Grade 9A"}),(0,t.jsx)(l.SZ,{children:"Overall class performance and trends"})]}),(0,t.jsx)(l.aY,{children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"85%"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Class Average"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"78%"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Students Improving"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:"15%"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Need Support"})]})]})})]})]})}},9197:function(e,s,r){"use strict";r.d(s,{Zb:function(){return c},aY:function(){return m},SZ:function(){return o},Ol:function(){return i},ll:function(){return x}});var t=r(7437),l=r(2265),a=r(7042),n=r(4769);function d(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,n.m6)((0,a.W)(s))}let c=l.forwardRef((e,s)=>{let{className:r,...l}=e;return(0,t.jsx)("div",{ref:s,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",r),...l})});c.displayName="Card";let i=l.forwardRef((e,s)=>{let{className:r,...l}=e;return(0,t.jsx)("div",{ref:s,className:d("flex flex-col space-y-1.5 p-6",r),...l})});i.displayName="CardHeader";let x=l.forwardRef((e,s)=>{let{className:r,...l}=e;return(0,t.jsx)("h3",{ref:s,className:d("text-2xl font-semibold leading-none tracking-tight",r),...l})});x.displayName="CardTitle";let o=l.forwardRef((e,s)=>{let{className:r,...l}=e;return(0,t.jsx)("p",{ref:s,className:d("text-sm text-muted-foreground",r),...l})});o.displayName="CardDescription";let m=l.forwardRef((e,s)=>{let{className:r,...l}=e;return(0,t.jsx)("div",{ref:s,className:d("p-6 pt-0",r),...l})});m.displayName="CardContent",l.forwardRef((e,s)=>{let{className:r,...l}=e;return(0,t.jsx)("div",{ref:s,className:d("flex items-center p-6 pt-0",r),...l})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=8656)}),_N_E=e.O()}]);