(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4973],{6284:function(e,s,t){Promise.resolve().then(t.bind(t,9176))},9176:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return l}});var a=t(7437);t(2265);var r=t(9197);function l(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Assessments & Examinations"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Create, schedule, and manage student assessments and examinations"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Question Bank"}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Create Assessment"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Upcoming Exams"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"12"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Next 2 weeks"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Pending Evaluations"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"156"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Answer sheets"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Completed Assessments"}),(0,a.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"89"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ll,{className:"text-sm font-medium",children:"Average Score"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFAF"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"78%"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Class average"})]})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"Upcoming Assessments"}),(0,a.jsx)(r.SZ,{children:"Scheduled exams and tests for the next two weeks"})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{subject:"Mathematics",grade:"Grade 9",type:"Unit Test",date:"2024-02-20",time:"10:00 AM - 12:00 PM",duration:"2 hours",teacher:"Dr. Priya Patel",topics:["Polynomials","Linear Equations"],totalMarks:100,students:120},{subject:"Science",grade:"Grade 10",type:"Practical Exam",date:"2024-02-22",time:"2:00 PM - 4:00 PM",duration:"2 hours",teacher:"Mr. Rajesh Kumar",topics:["Light","Electricity"],totalMarks:50,students:115},{subject:"English",grade:"Grade 11",type:"Mid-term Exam",date:"2024-02-25",time:"9:00 AM - 12:00 PM",duration:"3 hours",teacher:"Ms. Sneha Gupta",topics:["Literature","Grammar","Composition"],totalMarks:150,students:95}].map((e,s)=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.subject}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e.grade}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Mid-term Exam"===e.type?"bg-red-100 text-red-800":"Unit Test"===e.type?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:e.type})]}),(0,a.jsx)("p",{className:"text-blue-600 font-medium",children:e.teacher}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCC5 Date:"})," ",e.date]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"⏰ Time:"})," ",e.time]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"⏱️ Duration:"})," ",e.duration]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCCA Total Marks:"})," ",e.totalMarks]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDC65 Students:"})," ",e.students]})]})]}),(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-1",children:"Topics Covered:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.topics.map((e,s)=>(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded",children:e},s))})]})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"View Details"}),(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"Edit Assessment"}),(0,a.jsx)("button",{className:"text-green-600 hover:text-green-800 text-sm",children:"Generate Paper"})]})]})},s))})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"Create New Assessment"}),(0,a.jsx)(r.SZ,{children:"Set up a new exam or test"})]}),(0,a.jsxs)(r.aY,{children:[(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Subject"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"",children:"Select Subject"}),(0,a.jsx)("option",{value:"mathematics",children:"Mathematics"}),(0,a.jsx)("option",{value:"science",children:"Science"}),(0,a.jsx)("option",{value:"english",children:"English"}),(0,a.jsx)("option",{value:"social-studies",children:"Social Studies"}),(0,a.jsx)("option",{value:"hindi",children:"Hindi"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Grade"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"",children:"Select Grade"}),(0,a.jsx)("option",{value:"grade-9",children:"Grade 9"}),(0,a.jsx)("option",{value:"grade-10",children:"Grade 10"}),(0,a.jsx)("option",{value:"grade-11",children:"Grade 11"}),(0,a.jsx)("option",{value:"grade-12",children:"Grade 12"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Assessment Type"}),(0,a.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,a.jsx)("option",{value:"",children:"Select Type"}),(0,a.jsx)("option",{value:"unit-test",children:"Unit Test"}),(0,a.jsx)("option",{value:"mid-term",children:"Mid-term Exam"}),(0,a.jsx)("option",{value:"final-exam",children:"Final Exam"}),(0,a.jsx)("option",{value:"practical",children:"Practical Exam"}),(0,a.jsx)("option",{value:"assignment",children:"Assignment"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Total Marks"}),(0,a.jsx)("input",{type:"number",placeholder:"100",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Date"}),(0,a.jsx)("input",{type:"date",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Duration (minutes)"}),(0,a.jsx)("input",{type:"number",placeholder:"120",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Topics to Cover"}),(0,a.jsx)("textarea",{rows:3,placeholder:"Enter topics separated by commas...",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Instructions"}),(0,a.jsx)("textarea",{rows:4,placeholder:"Enter exam instructions...",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{className:"mt-4 flex space-x-2",children:[(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Create Assessment"}),(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Save as Draft"}),(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-800",children:"Use Template"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"Recent Assessment Results"}),(0,a.jsx)(r.SZ,{children:"Performance overview of completed assessments"})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{subject:"Mathematics",grade:"Grade 9",average:78,highest:95,lowest:45,students:120},{subject:"Science",grade:"Grade 10",average:82,highest:98,lowest:52,students:115},{subject:"English",grade:"Grade 11",average:75,highest:92,lowest:48,students:95},{subject:"Social Studies",grade:"Grade 9",average:80,highest:94,lowest:58,students:118}].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:e.subject}),(0,a.jsx)("p",{className:"text-xs text-blue-600",children:e.grade})]}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[e.average,"% avg"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-xs text-gray-600",children:[(0,a.jsxs)("div",{children:["Highest: ",e.highest,"%"]}),(0,a.jsxs)("div",{children:["Lowest: ",e.lowest,"%"]}),(0,a.jsxs)("div",{children:["Students: ",e.students]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.average,"%")}})})]},s))})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsxs)(r.Ol,{children:[(0,a.jsx)(r.ll,{children:"Assessment Tools"}),(0,a.jsx)(r.SZ,{children:"Tools for creating and managing assessments"})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{tool:"Question Bank",description:"Access pre-made questions",icon:"❓",count:"2,456 questions"},{tool:"Auto Paper Generator",description:"Generate papers automatically",icon:"\uD83E\uDD16",count:"15 templates"},{tool:"Answer Key Creator",description:"Create answer keys",icon:"\uD83D\uDD11",count:"89 keys"},{tool:"Grade Calculator",description:"Calculate grades and statistics",icon:"\uD83E\uDDEE",count:"Active"},{tool:"Result Analyzer",description:"Analyze assessment results",icon:"\uD83D\uDCC8",count:"12 reports"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between border rounded p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.tool}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:e.description})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.count}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700 mt-1",children:"Use"})]})]},s))})})]})]})]})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return i},aY:function(){return m},SZ:function(){return o},Ol:function(){return c},ll:function(){return x}});var a=t(7437),r=t(2265),l=t(7042),d=t(4769);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,d.m6)((0,l.W)(s))}let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:n("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});i.displayName="Card";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:n("flex flex-col space-y-1.5 p-6",t),...r})});c.displayName="CardHeader";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:n("text-2xl font-semibold leading-none tracking-tight",t),...r})});x.displayName="CardTitle";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:n("text-sm text-muted-foreground",t),...r})});o.displayName="CardDescription";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:n("p-6 pt-0",t),...r})});m.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:n("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=6284)}),_N_E=e.O()}]);