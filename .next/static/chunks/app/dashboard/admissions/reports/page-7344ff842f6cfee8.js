(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[971],{8462:function(e,s,l){Promise.resolve().then(l.bind(l,8190))},8190:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return r}});var t=l(7437);l(2265);var n=l(9197);function r(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Admission Reports"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Generate comprehensive reports and analytics for admission process"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Schedule Report"}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Generate Report"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Applications"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCB"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"1,234"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"+12% from last year"})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Acceptance Rate"}),(0,t.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"85%"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"456 admitted"})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Enrollment Rate"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"87%"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"398 enrolled"})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ll,{className:"text-sm font-medium",children:"Revenue Generated"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"₹2.4M"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"From admissions"})]})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsx)(n.ll,{children:"Report Templates"}),(0,t.jsx)(n.SZ,{children:"Pre-configured reports for different stakeholders"})]}),(0,t.jsx)(n.aY,{children:(0,t.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[{title:"Application Summary Report",description:"Overview of all applications by grade and status",icon:"\uD83D\uDCCA",frequency:"Weekly",lastGenerated:"2024-02-15",recipients:"Principal, Admission Team"},{title:"Interview Performance Report",description:"Interview scores and interviewer feedback analysis",icon:"\uD83C\uDFAF",frequency:"Monthly",lastGenerated:"2024-02-01",recipients:"Academic Head, Interviewers"},{title:"Enrollment Tracking Report",description:"Student enrollment progress and completion rates",icon:"\uD83D\uDCC8",frequency:"Daily",lastGenerated:"2024-02-16",recipients:"Admission Office, Finance"},{title:"Fee Collection Report",description:"Admission fee payments and outstanding amounts",icon:"\uD83D\uDCB3",frequency:"Weekly",lastGenerated:"2024-02-14",recipients:"Finance Team, Principal"},{title:"Grade-wise Analysis",description:"Detailed breakdown by grade level and sections",icon:"\uD83C\uDFEB",frequency:"Monthly",lastGenerated:"2024-02-01",recipients:"Academic Coordinators"},{title:"Demographic Report",description:"Student demographics and diversity metrics",icon:"\uD83D\uDC65",frequency:"Quarterly",lastGenerated:"2024-01-15",recipients:"Management, Board"}].map((e,s)=>(0,t.jsxs)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,t.jsx)("div",{className:"flex items-start justify-between mb-3",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-blue-600",children:e.frequency})]})]})}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-3",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Last Generated:"})," ",e.lastGenerated]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Recipients:"})," ",e.recipients]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Generate"}),(0,t.jsx)("button",{className:"border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50",children:"Schedule"}),(0,t.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm",children:"View Sample"})]})]},s))})})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsx)(n.ll,{children:"Custom Report Builder"}),(0,t.jsx)(n.SZ,{children:"Create custom reports with specific filters and metrics"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Report Type"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Report Type"}),(0,t.jsx)("option",{value:"application",children:"Application Analysis"}),(0,t.jsx)("option",{value:"interview",children:"Interview Performance"}),(0,t.jsx)("option",{value:"enrollment",children:"Enrollment Tracking"}),(0,t.jsx)("option",{value:"financial",children:"Financial Summary"}),(0,t.jsx)("option",{value:"demographic",children:"Demographic Analysis"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Date Range"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Date Range"}),(0,t.jsx)("option",{value:"last-week",children:"Last Week"}),(0,t.jsx)("option",{value:"last-month",children:"Last Month"}),(0,t.jsx)("option",{value:"last-quarter",children:"Last Quarter"}),(0,t.jsx)("option",{value:"academic-year",children:"Academic Year 2023-24"}),(0,t.jsx)("option",{value:"custom",children:"Custom Range"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Grade Filter"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"All Grades"}),(0,t.jsx)("option",{value:"nursery",children:"Nursery"}),(0,t.jsx)("option",{value:"grade-1",children:"Grade 1"}),(0,t.jsx)("option",{value:"grade-6",children:"Grade 6"}),(0,t.jsx)("option",{value:"grade-9",children:"Grade 9"}),(0,t.jsx)("option",{value:"grade-11",children:"Grade 11"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Status Filter"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"All Status"}),(0,t.jsx)("option",{value:"submitted",children:"Submitted"}),(0,t.jsx)("option",{value:"interviewed",children:"Interviewed"}),(0,t.jsx)("option",{value:"approved",children:"Approved"}),(0,t.jsx)("option",{value:"enrolled",children:"Enrolled"})]})]})]}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Include Metrics"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2 mt-2",children:["Application Count","Interview Scores","Acceptance Rate","Enrollment Rate","Fee Collection","Geographic Distribution","Age Demographics","Previous School Analysis"].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",className:"rounded"}),(0,t.jsx)("label",{className:"text-sm",children:e})]},s))})]}),(0,t.jsxs)("div",{className:"mt-4 flex space-x-2",children:[(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Generate Custom Report"}),(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Save Template"}),(0,t.jsx)("button",{className:"text-blue-600 hover:text-blue-800",children:"Preview"})]})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsx)(n.ll,{children:"Application Trends"}),(0,t.jsx)(n.SZ,{children:"Monthly application patterns"})]}),(0,t.jsx)(n.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{month:"October 2023",applications:89,change:"+15%"},{month:"November 2023",applications:156,change:"+75%"},{month:"December 2023",applications:234,change:"+50%"},{month:"January 2024",applications:345,change:"+47%"},{month:"February 2024",applications:410,change:"+19%"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:e.month}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.applications," applications"]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)("span",{className:"text-sm font-medium ".concat(e.change.startsWith("+")?"text-green-600":"text-red-600"),children:e.change})})]},s))})})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsx)(n.ll,{children:"Performance Metrics"}),(0,t.jsx)(n.SZ,{children:"Key admission KPIs"})]}),(0,t.jsx)(n.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Application to Interview Rate"}),(0,t.jsx)("span",{className:"font-medium",children:"78%"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"78%"}})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Interview to Admission Rate"}),(0,t.jsx)("span",{className:"font-medium",children:"85%"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"85%"}})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Admission to Enrollment Rate"}),(0,t.jsx)("span",{className:"font-medium",children:"87%"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-purple-600 h-2 rounded-full",style:{width:"87%"}})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Fee Collection Rate"}),(0,t.jsx)("span",{className:"font-medium",children:"92%"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-yellow-600 h-2 rounded-full",style:{width:"92%"}})})]})]})})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsx)(n.ll,{children:"Recent Reports"}),(0,t.jsx)(n.SZ,{children:"Recently generated reports and downloads"})]}),(0,t.jsx)(n.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{name:"Weekly Application Summary",type:"PDF",size:"2.3 MB",generated:"2024-02-15 10:30 AM",downloads:12},{name:"Interview Performance Analysis",type:"Excel",size:"1.8 MB",generated:"2024-02-14 03:45 PM",downloads:8},{name:"Enrollment Progress Report",type:"PDF",size:"3.1 MB",generated:"2024-02-14 09:15 AM",downloads:15},{name:"Fee Collection Summary",type:"Excel",size:"1.2 MB",generated:"2024-02-13 02:20 PM",downloads:6},{name:"Grade 9 Admission Report",type:"PDF",size:"2.7 MB",generated:"2024-02-12 11:00 AM",downloads:9}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between border-b pb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:"text-2xl",children:"PDF"===e.type?"\uD83D\uDCC4":"\uD83D\uDCCA"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-sm",children:e.name}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.type," • ",e.size," • Generated: ",e.generated]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:[e.downloads," downloads"]}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Download"})]})]},s))})})]})]})}},9197:function(e,s,l){"use strict";l.d(s,{Zb:function(){return d},aY:function(){return m},SZ:function(){return x},Ol:function(){return c},ll:function(){return o}});var t=l(7437),n=l(2265),r=l(7042),a=l(4769);function i(){for(var e=arguments.length,s=Array(e),l=0;l<e;l++)s[l]=arguments[l];return(0,a.m6)((0,r.W)(s))}let d=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,t.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",l),...n})});d.displayName="Card";let c=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,t.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",l),...n})});c.displayName="CardHeader";let o=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,t.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",l),...n})});o.displayName="CardTitle";let x=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,t.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",l),...n})});x.displayName="CardDescription";let m=n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,t.jsx)("div",{ref:s,className:i("p-6 pt-0",l),...n})});m.displayName="CardContent",n.forwardRef((e,s)=>{let{className:l,...n}=e;return(0,t.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",l),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=8462)}),_N_E=e.O()}]);