(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2911],{2420:function(e,s,r){Promise.resolve().then(r.bind(r,4024))},4024:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return d}});var t=r(7437);r(2265);var a=r(9197);function d(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Admission Interviews"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Schedule, conduct, and manage student admission interviews"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Interview Report"}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Schedule Interview"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Scheduled Interviews"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC5"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"89"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"This week"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Completed"}),(0,t.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"234"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Pending Feedback"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"23"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Awaiting scores"})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ll,{className:"text-sm font-medium",children:"Success Rate"}),(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFAF"})]}),(0,t.jsxs)(a.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"78%"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Pass rate"})]})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Today's Interview Schedule"}),(0,t.jsxs)(a.SZ,{children:["Interviews scheduled for today - ",new Date().toLocaleDateString()]})]}),(0,t.jsx)(a.aY,{children:(0,t.jsx)("div",{className:"space-y-4",children:[{time:"09:00 AM",student:"Aarav Sharma",grade:"Grade 9",applicationId:"APP-2024-001",interviewer:"Dr. Priya Patel",room:"Conference Room A",status:"Scheduled",parentContact:"+91-9876543210",duration:"30 mins"},{time:"09:30 AM",student:"Kavya Gupta",grade:"Grade 6",applicationId:"APP-2024-015",interviewer:"Mr. Rajesh Kumar",room:"Conference Room B",status:"In Progress",parentContact:"+91-9876543211",duration:"30 mins"},{time:"10:00 AM",student:"Arjun Singh",grade:"Grade 11",applicationId:"APP-2024-032",interviewer:"Ms. Sneha Gupta",room:"Conference Room A",status:"Completed",parentContact:"+91-9876543212",duration:"45 mins"},{time:"10:45 AM",student:"Riya Patel",grade:"Grade 3",applicationId:"APP-2024-048",interviewer:"Dr. Amit Verma",room:"Conference Room C",status:"Scheduled",parentContact:"+91-9876543213",duration:"20 mins"}].map((e,s)=>(0,t.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:e.student}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e.grade}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Completed"===e.status?"bg-green-100 text-green-800":"In Progress"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:e.status})]}),(0,t.jsx)("p",{className:"text-blue-600 font-medium",children:e.applicationId}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDD52 Time:"})," ",e.time]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"⏱️ Duration:"})," ",e.duration]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83C\uDFE2 Room:"})," ",e.room]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDC68‍\uD83C\uDFEB Interviewer:"})," ",e.interviewer]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("span",{className:"font-medium",children:"\uD83D\uDCDE Parent Contact:"})," ",e.parentContact]})]})]})]}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:["Scheduled"===e.status&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm",children:"Start Interview"}),(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"Reschedule"})]}),"In Progress"===e.status&&(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"Complete Interview"}),"Completed"===e.status&&(0,t.jsx)("button",{className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 text-sm",children:"View Feedback"}),(0,t.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm",children:"View Application"})]})]})},s))})})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Interview Evaluation"}),(0,t.jsx)(a.SZ,{children:"Complete interview feedback and scoring"})]}),(0,t.jsx)(a.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Student Name"}),(0,t.jsx)("input",{type:"text",value:"Arjun Singh",disabled:!0,className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Application ID"}),(0,t.jsx)("input",{type:"text",value:"APP-2024-032",disabled:!0,className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Interviewer"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Interviewer"}),(0,t.jsx)("option",{value:"dr-priya",children:"Dr. Priya Patel"}),(0,t.jsx)("option",{value:"mr-rajesh",children:"Mr. Rajesh Kumar"}),(0,t.jsx)("option",{value:"ms-sneha",children:"Ms. Sneha Gupta"}),(0,t.jsx)("option",{value:"dr-amit",children:"Dr. Amit Verma"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Interview Date"}),(0,t.jsx)("input",{type:"date",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Evaluation Criteria"}),[{criteria:"Academic Knowledge",description:"Understanding of subject matter"},{criteria:"Communication Skills",description:"Clarity and confidence in expression"},{criteria:"Problem Solving",description:"Analytical and logical thinking"},{criteria:"Personality & Attitude",description:"Enthusiasm and positive attitude"},{criteria:"Extracurricular Interest",description:"Involvement in activities beyond academics"}].map((e,s)=>(0,t.jsx)("div",{className:"border rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:e.criteria}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]}),(0,t.jsx)("div",{className:"flex space-x-2",children:[1,2,3,4,5].map(e=>(0,t.jsx)("button",{className:"w-8 h-8 rounded-full border border-gray-300 hover:bg-blue-100 text-sm",children:e},e))})]})},s))]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Additional Comments"}),(0,t.jsx)("textarea",{rows:4,placeholder:"Provide detailed feedback about the student's performance...",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Overall Recommendation"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Recommendation"}),(0,t.jsx)("option",{value:"strongly-recommend",children:"Strongly Recommend"}),(0,t.jsx)("option",{value:"recommend",children:"Recommend"}),(0,t.jsx)("option",{value:"conditional",children:"Conditional Admission"}),(0,t.jsx)("option",{value:"not-recommend",children:"Not Recommend"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Overall Score"}),(0,t.jsx)("input",{type:"number",min:"0",max:"100",placeholder:"0-100",className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Submit Feedback"}),(0,t.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Save as Draft"}),(0,t.jsx)("button",{className:"text-red-600 hover:text-red-800",children:"Cancel"})]})]})})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Interview Performance"}),(0,t.jsx)(a.SZ,{children:"Success rates by grade level"})]}),(0,t.jsx)(a.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{grade:"Nursery",total:45,passed:42,rate:93},{grade:"Grade 1",total:67,passed:58,rate:87},{grade:"Grade 6",total:89,passed:71,rate:80},{grade:"Grade 9",total:123,passed:95,rate:77},{grade:"Grade 11",total:78,passed:56,rate:72}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:e.grade}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.passed,"/",e.total," passed"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-20 bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"".concat(e.rate,"%")}})}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[e.rate,"%"]})]})]},s))})})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"Upcoming Interviews"}),(0,t.jsx)(a.SZ,{children:"Next week's schedule"})]}),(0,t.jsx)(a.aY,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{date:"Feb 19",count:12,interviewer:"Dr. Priya Patel"},{date:"Feb 20",count:15,interviewer:"Mr. Rajesh Kumar"},{date:"Feb 21",count:8,interviewer:"Ms. Sneha Gupta"},{date:"Feb 22",count:18,interviewer:"Dr. Amit Verma"},{date:"Feb 23",count:10,interviewer:"Dr. Priya Patel"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between border-b pb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-sm",children:e.date}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:e.interviewer})]}),(0,t.jsxs)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:[e.count," interviews"]})]},s))})})]})]})]})}},9197:function(e,s,r){"use strict";r.d(s,{Zb:function(){return i},aY:function(){return x},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var t=r(7437),a=r(2265),d=r(7042),l=r(4769);function n(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,l.m6)((0,d.W)(s))}let i=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:n("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:n("flex flex-col space-y-1.5 p-6",r),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h3",{ref:s,className:n("text-2xl font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let m=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("p",{ref:s,className:n("text-sm text-muted-foreground",r),...a})});m.displayName="CardDescription";let x=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:n("p-6 pt-0",r),...a})});x.displayName="CardContent",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:n("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=2420)}),_N_E=e.O()}]);