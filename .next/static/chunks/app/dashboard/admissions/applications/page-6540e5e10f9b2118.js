(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6430],{2615:function(e,s,t){Promise.resolve().then(t.bind(t,1824))},1824:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return l}});var r=t(7437);t(2265);var a=t(9197);function l(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Student Applications"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Review and process student admission applications"})]}),(0,r.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Export Applications"})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Total Applications"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCB"})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"1,234"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"This academic year"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Under Review"}),(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC40"})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"156"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Pending review"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Approved"}),(0,r.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"789"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"64% approval rate"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Rejected"}),(0,r.jsx)("span",{className:"text-2xl",children:"❌"})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"289"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"23% rejection rate"})]})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Recent Applications"}),(0,r.jsx)(a.SZ,{children:"Latest student admission applications"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"space-y-4",children:[{id:"APP-2024-001",name:"Aarav Sharma",grade:"Grade 9",submittedDate:"2024-02-15",status:"Under Review",previousSchool:"Delhi Public School",parentName:"Rajesh Sharma",phone:"+91-**********",documents:["Birth Certificate","Transfer Certificate","Report Card"],priority:"High"},{id:"APP-2024-002",name:"Priya Patel",grade:"Grade 6",submittedDate:"2024-02-14",status:"Approved",previousSchool:"St. Mary's School",parentName:"Amit Patel",phone:"+91-**********",documents:["Birth Certificate","Transfer Certificate","Report Card","Medical Certificate"],priority:"Medium"},{id:"APP-2024-003",name:"Arjun Singh",grade:"Grade 11",submittedDate:"2024-02-13",status:"Interview Scheduled",previousSchool:"Ryan International",parentName:"Sunita Singh",phone:"+91-**********",documents:["Birth Certificate","Transfer Certificate","Report Card"],priority:"High"},{id:"APP-2024-004",name:"Kavya Gupta",grade:"Grade 3",submittedDate:"2024-02-12",status:"Documents Pending",previousSchool:"Little Angels School",parentName:"Vikram Gupta",phone:"+91-**********",documents:["Birth Certificate","Report Card"],priority:"Low"}].map((e,s)=>(0,r.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:e.name}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Approved"===e.status?"bg-green-100 text-green-800":"Under Review"===e.status?"bg-yellow-100 text-yellow-800":"Interview Scheduled"===e.status?"bg-blue-100 text-blue-800":"Documents Pending"===e.status?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"),children:e.status}),(0,r.jsxs)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("High"===e.priority?"bg-red-100 text-red-800":"Medium"===e.priority?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:[e.priority," Priority"]})]}),(0,r.jsx)("p",{className:"text-blue-600 font-medium",children:e.id}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"\uD83C\uDF93 Grade:"})," ",e.grade]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"\uD83C\uDFEB Previous School:"})," ",e.previousSchool]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"\uD83D\uDC68‍\uD83D\uDC69‍\uD83D\uDC67‍\uD83D\uDC66 Parent:"})," ",e.parentName]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"\uD83D\uDCDE Phone:"})," ",e.phone]})]})]}),(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:[(0,r.jsx)("span",{className:"font-medium",children:"\uD83D\uDCC5 Submitted:"})," ",e.submittedDate]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Documents:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:e.documents.map((e,s)=>(0,r.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded",children:e},s))})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,r.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"Review"}),(0,r.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"Schedule Interview"}),(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm",children:"View Details"})]})]})},s))})})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Application Status"}),(0,r.jsx)(a.SZ,{children:"Current application pipeline"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"space-y-3",children:[{status:"Submitted",count:156,color:"bg-blue-100 text-blue-800"},{status:"Under Review",count:89,color:"bg-yellow-100 text-yellow-800"},{status:"Interview Scheduled",count:45,color:"bg-purple-100 text-purple-800"},{status:"Documents Pending",count:23,color:"bg-orange-100 text-orange-800"},{status:"Approved",count:789,color:"bg-green-100 text-green-800"},{status:"Rejected",count:132,color:"bg-red-100 text-red-800"}].map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:e.status}),(0,r.jsxs)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(e.color),children:[e.count," applications"]})]},s))})})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Grade-wise Applications"}),(0,r.jsx)(a.SZ,{children:"Applications by grade level"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"space-y-3",children:[{grade:"Nursery",count:89},{grade:"Grade 1",count:156},{grade:"Grade 6",count:234},{grade:"Grade 9",count:345},{grade:"Grade 11",count:278},{grade:"Others",count:132}].map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:e.grade}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-20 bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.count/345*100,"%")}})}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:e.count})]})]},s))})})]})]})]})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return d},aY:function(){return u},SZ:function(){return x},Ol:function(){return c},ll:function(){return o}});var r=t(7437),a=t(2265),l=t(7042),n=t(4769);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,l.W)(s))}let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});d.displayName="Card";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",t),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let x=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",t),...a})});x.displayName="CardDescription";let u=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:i("p-6 pt-0",t),...a})});u.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=2615)}),_N_E=e.O()}]);