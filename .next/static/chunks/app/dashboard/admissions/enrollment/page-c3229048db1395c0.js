(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9791],{5711:function(e,s,t){Promise.resolve().then(t.bind(t,1225))},1225:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return a}});var l=t(7437);t(2265);var n=t(9197);function a(){return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Student Enrollment"}),(0,l.jsx)("p",{className:"text-muted-foreground",children:"Complete student enrollment process and generate student IDs"})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Bulk Enrollment"}),(0,l.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Generate IDs"})]})]}),(0,l.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,l.jsxs)(n.Zb,{children:[(0,l.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(n.ll,{className:"text-sm font-medium",children:"Ready for Enrollment"}),(0,l.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCB"})]}),(0,l.jsxs)(n.aY,{children:[(0,l.jsx)("div",{className:"text-2xl font-bold",children:"398"}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"Allocated students"})]})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(n.ll,{className:"text-sm font-medium",children:"Enrolled"}),(0,l.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,l.jsxs)(n.aY,{children:[(0,l.jsx)("div",{className:"text-2xl font-bold",children:"342"}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"86% enrollment rate"})]})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(n.ll,{className:"text-sm font-medium",children:"Pending Documents"}),(0,l.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC4"})]}),(0,l.jsxs)(n.aY,{children:[(0,l.jsx)("div",{className:"text-2xl font-bold",children:"34"}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"Missing documents"})]})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(n.ll,{className:"text-sm font-medium",children:"Fee Pending"}),(0,l.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"})]}),(0,l.jsxs)(n.aY,{children:[(0,l.jsx)("div",{className:"text-2xl font-bold",children:"22"}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"Payment pending"})]})]})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsxs)(n.Ol,{children:[(0,l.jsx)(n.ll,{children:"Enrollment Pipeline"}),(0,l.jsx)(n.SZ,{children:"Students at different stages of enrollment process"})]}),(0,l.jsx)(n.aY,{children:(0,l.jsx)("div",{className:"space-y-4",children:[{name:"Aarav Sharma",id:"APP-2024-001",grade:"Grade 9",section:"9-A",stage:"Documents Verified",documentsStatus:"Complete",feeStatus:"Paid",medicalStatus:"Pending",parentContact:"+91-**********",enrollmentDate:"2024-02-20"},{name:"Kavya Gupta",id:"APP-2024-015",grade:"Grade 6",section:"6-B",stage:"Fee Payment",documentsStatus:"Complete",feeStatus:"Pending",medicalStatus:"Complete",parentContact:"+91-**********",enrollmentDate:"2024-02-18"},{name:"Arjun Singh",id:"APP-2024-032",grade:"Grade 11",section:"11-Science A",stage:"Medical Checkup",documentsStatus:"Complete",feeStatus:"Paid",medicalStatus:"Scheduled",parentContact:"+91-**********",enrollmentDate:"2024-02-22"},{name:"Riya Patel",id:"APP-2024-048",grade:"Grade 3",section:"3-A",stage:"Enrolled",documentsStatus:"Complete",feeStatus:"Paid",medicalStatus:"Complete",parentContact:"+91-**********",enrollmentDate:"2024-02-15",studentId:"STU-2024-0342"}].map((e,s)=>(0,l.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:e.name}),(0,l.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e.grade}),(0,l.jsx)("span",{className:"px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full",children:e.section}),(0,l.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Enrolled"===e.stage?"bg-green-100 text-green-800":"Documents Verified"===e.stage?"bg-blue-100 text-blue-800":"Fee Payment"===e.stage?"bg-yellow-100 text-yellow-800":"bg-orange-100 text-orange-800"),children:e.stage})]}),(0,l.jsx)("p",{className:"text-blue-600 font-medium",children:e.id}),e.studentId&&(0,l.jsxs)("p",{className:"text-green-600 font-medium",children:["Student ID: ",e.studentId]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("p",{children:[(0,l.jsx)("span",{className:"font-medium",children:"\uD83D\uDCC4 Documents:"}),(0,l.jsx)("span",{className:"ml-1 ".concat("Complete"===e.documentsStatus?"text-green-600":"text-red-600"),children:e.documentsStatus})]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("span",{className:"font-medium",children:"\uD83D\uDCB0 Fee Payment:"}),(0,l.jsx)("span",{className:"ml-1 ".concat("Paid"===e.feeStatus?"text-green-600":"text-red-600"),children:e.feeStatus})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("p",{children:[(0,l.jsx)("span",{className:"font-medium",children:"\uD83C\uDFE5 Medical:"}),(0,l.jsx)("span",{className:"ml-1 ".concat("Complete"===e.medicalStatus?"text-green-600":"Scheduled"===e.medicalStatus?"text-yellow-600":"text-red-600"),children:e.medicalStatus})]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("span",{className:"font-medium",children:"\uD83D\uDCDE Contact:"})," ",e.parentContact]})]})]}),(0,l.jsx)("div",{className:"mt-3",children:(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,l.jsx)("span",{className:"font-medium",children:"\uD83D\uDCC5 Enrollment Date:"})," ",e.enrollmentDate]})}),(0,l.jsxs)("div",{className:"mt-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 text-xs mb-1",children:[(0,l.jsx)("span",{className:"Complete"===e.documentsStatus?"text-green-600":"text-gray-400",children:"\uD83D\uDCC4 Docs"}),(0,l.jsx)("span",{className:"Paid"===e.feeStatus?"text-green-600":"text-gray-400",children:"\uD83D\uDCB0 Fee"}),(0,l.jsx)("span",{className:"Complete"===e.medicalStatus?"text-green-600":"text-gray-400",children:"\uD83C\uDFE5 Medical"}),(0,l.jsx)("span",{className:"Enrolled"===e.stage?"text-green-600":"text-gray-400",children:"✅ Enrolled"})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,l.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat("Enrolled"===e.stage?100:"Medical Checkup"===e.stage?75:"Fee Payment"===e.stage?50:"Documents Verified"===e.stage?25:0,"%")}})})]})]}),(0,l.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:["Enrolled"===e.stage?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md text-sm",children:"View Profile"}),(0,l.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"Print ID Card"})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"Complete Enrollment"}),(0,l.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 text-sm",children:"Send Reminder"})]}),(0,l.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm",children:"View Details"})]})]})},s))})})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsxs)(n.Ol,{children:[(0,l.jsx)(n.ll,{children:"Enrollment Checklist"}),(0,l.jsx)(n.SZ,{children:"Required steps for student enrollment"})]}),(0,l.jsx)(n.aY,{children:(0,l.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[{step:"Document Verification",description:"Birth certificate, transfer certificate, etc.",icon:"\uD83D\uDCC4",completed:364,total:398,status:"In Progress"},{step:"Fee Payment",description:"Admission and first term fees",icon:"\uD83D\uDCB0",completed:342,total:398,status:"In Progress"},{step:"Medical Checkup",description:"Health screening and vaccination records",icon:"\uD83C\uDFE5",completed:298,total:398,status:"In Progress"},{step:"ID Generation",description:"Student ID and access cards",icon:"\uD83C\uDD94",completed:342,total:398,status:"Ready"}].map((e,s)=>(0,l.jsxs)("div",{className:"border rounded-lg p-4 text-center",children:[(0,l.jsx)("div",{className:"text-3xl mb-2",children:e.icon}),(0,l.jsx)("h3",{className:"font-semibold mb-1",children:e.step}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,l.jsxs)("div",{className:"mb-2",children:[(0,l.jsxs)("div",{className:"text-lg font-bold text-blue-600",children:[e.completed,"/",e.total]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-1",children:(0,l.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.completed/e.total*100,"%")}})})]}),(0,l.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Ready"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:e.status})]},s))})})]}),(0,l.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,l.jsxs)(n.Zb,{children:[(0,l.jsxs)(n.Ol,{children:[(0,l.jsx)(n.ll,{children:"Bulk Enrollment Actions"}),(0,l.jsx)(n.SZ,{children:"Process multiple students at once"})]}),(0,l.jsx)(n.aY,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,l.jsx)("h4",{className:"font-medium mb-2",children:"Generate Student IDs"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Create unique student IDs for enrolled students"}),(0,l.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"Generate IDs (56 students)"})]}),(0,l.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,l.jsx)("h4",{className:"font-medium mb-2",children:"Send Welcome Emails"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Send enrollment confirmation to parents"}),(0,l.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm",children:"Send Emails (342 families)"})]}),(0,l.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,l.jsx)("h4",{className:"font-medium mb-2",children:"Print ID Cards"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Generate physical ID cards for students"}),(0,l.jsx)("button",{className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 text-sm",children:"Print Cards (342 cards)"})]})]})})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsxs)(n.Ol,{children:[(0,l.jsx)(n.ll,{children:"Enrollment Statistics"}),(0,l.jsx)(n.SZ,{children:"Current enrollment progress"})]}),(0,l.jsx)(n.aY,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-1",children:"86%"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Enrollment Complete"}),(0,l.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mt-2",children:(0,l.jsx)("div",{className:"bg-blue-600 h-3 rounded-full",style:{width:"86%"}})})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,l.jsx)("span",{children:"Documents Complete"}),(0,l.jsx)("span",{className:"font-medium",children:"91%"})]}),(0,l.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,l.jsx)("span",{children:"Fees Collected"}),(0,l.jsx)("span",{className:"font-medium",children:"86%"})]}),(0,l.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,l.jsx)("span",{children:"Medical Clearance"}),(0,l.jsx)("span",{className:"font-medium",children:"75%"})]}),(0,l.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,l.jsx)("span",{children:"IDs Generated"}),(0,l.jsx)("span",{className:"font-medium",children:"86%"})]})]}),(0,l.jsx)("div",{className:"pt-3 border-t",children:(0,l.jsxs)("div",{className:"flex justify-between font-medium",children:[(0,l.jsx)("span",{children:"Total Enrolled"}),(0,l.jsx)("span",{className:"text-green-600",children:"342 / 398"})]})})]})})]})]})]})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return c},aY:function(){return o},SZ:function(){return x},Ol:function(){return i},ll:function(){return m}});var l=t(7437),n=t(2265),a=t(7042),d=t(4769);function r(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,d.m6)((0,a.W)(s))}let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,l.jsx)("div",{ref:s,className:r("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});c.displayName="Card";let i=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,l.jsx)("div",{ref:s,className:r("flex flex-col space-y-1.5 p-6",t),...n})});i.displayName="CardHeader";let m=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,l.jsx)("h3",{ref:s,className:r("text-2xl font-semibold leading-none tracking-tight",t),...n})});m.displayName="CardTitle";let x=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,l.jsx)("p",{ref:s,className:r("text-sm text-muted-foreground",t),...n})});x.displayName="CardDescription";let o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,l.jsx)("div",{ref:s,className:r("p-6 pt-0",t),...n})});o.displayName="CardContent",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,l.jsx)("div",{ref:s,className:r("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=5711)}),_N_E=e.O()}]);