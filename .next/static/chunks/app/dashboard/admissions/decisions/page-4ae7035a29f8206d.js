(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[855],{1925:function(e,s,t){Promise.resolve().then(t.bind(t,683))},683:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return i}});var r=t(7437);t(2265);var a=t(9197);function i(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Admission Decisions"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Make admission decisions and send notifications to applicants"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Bulk Actions"}),(0,r.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Send Notifications"})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Pending Decisions"}),(0,r.jsx)("span",{className:"text-2xl",children:"⏳"})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"45"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Awaiting decision"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Approved"}),(0,r.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"456"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"85% acceptance rate"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Rejected"}),(0,r.jsx)("span",{className:"text-2xl",children:"❌"})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"89"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"15% rejection rate"})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ll,{className:"text-sm font-medium",children:"Waitlisted"}),(0,r.jsx)("span",{className:"text-2xl",children:"⏰"})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"23"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"On waiting list"})]})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Applications Pending Decision"}),(0,r.jsx)(a.SZ,{children:"Students awaiting admission decision"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"space-y-4",children:[{id:"APP-2024-001",name:"Aarav Sharma",grade:"Grade 9",interviewScore:85,academicScore:92,overallScore:88,interviewer:"Dr. Priya Patel",applicationDate:"2024-01-15",interviewDate:"2024-02-10",recommendation:"Strongly Recommend",priority:"High"},{id:"APP-2024-015",name:"Kavya Gupta",grade:"Grade 6",interviewScore:78,academicScore:85,overallScore:82,interviewer:"Mr. Rajesh Kumar",applicationDate:"2024-01-18",interviewDate:"2024-02-12",recommendation:"Recommend",priority:"Medium"},{id:"APP-2024-032",name:"Arjun Singh",grade:"Grade 11",interviewScore:72,academicScore:88,overallScore:80,interviewer:"Ms. Sneha Gupta",applicationDate:"2024-01-20",interviewDate:"2024-02-14",recommendation:"Conditional",priority:"Medium"}].map((e,s)=>(0,r.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:e.name}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e.grade}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Strongly Recommend"===e.recommendation?"bg-green-100 text-green-800":"Recommend"===e.recommendation?"bg-blue-100 text-blue-800":"Conditional"===e.recommendation?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.recommendation}),(0,r.jsxs)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("High"===e.priority?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:[e.priority," Priority"]})]}),(0,r.jsx)("p",{className:"text-blue-600 font-medium",children:e.id}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"\uD83D\uDCCA Interview Score:"})," ",e.interviewScore,"/100"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"\uD83D\uDCDA Academic Score:"})," ",e.academicScore,"/100"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"\uD83C\uDFAF Overall Score:"})," ",e.overallScore,"/100"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"\uD83D\uDC68‍\uD83C\uDFEB Interviewer:"})," ",e.interviewer]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"\uD83D\uDCC5 Applied:"})," ",e.applicationDate]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"\uD83C\uDFAF Interviewed:"})," ",e.interviewDate]})]})]}),(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full ".concat(e.overallScore>=85?"bg-green-600":e.overallScore>=70?"bg-yellow-600":"bg-red-600"),style:{width:"".concat(e.overallScore,"%")}})}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Overall Score: ",e.overallScore,"/100"]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,r.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm",children:"Approve"}),(0,r.jsx)("button",{className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 text-sm",children:"Reject"}),(0,r.jsx)("button",{className:"bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 text-sm",children:"Waitlist"}),(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm",children:"View Details"})]})]})},s))})})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Decision Statistics"}),(0,r.jsx)(a.SZ,{children:"Admission decisions by grade"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"space-y-3",children:[{grade:"Nursery",approved:42,rejected:8,waitlisted:3,total:53},{grade:"Grade 1",approved:58,rejected:12,waitlisted:5,total:75},{grade:"Grade 6",approved:71,rejected:15,waitlisted:8,total:94},{grade:"Grade 9",approved:95,rejected:20,waitlisted:12,total:127},{grade:"Grade 11",approved:56,rejected:18,waitlisted:6,total:80}].map((e,s)=>(0,r.jsxs)("div",{className:"border rounded p-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"font-medium",children:e.grade}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.total," total"]})]}),(0,r.jsxs)("div",{className:"flex space-x-2 text-xs",children:[(0,r.jsxs)("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded",children:[e.approved," Approved"]}),(0,r.jsxs)("span",{className:"px-2 py-1 bg-red-100 text-red-800 rounded",children:[e.rejected," Rejected"]}),(0,r.jsxs)("span",{className:"px-2 py-1 bg-yellow-100 text-yellow-800 rounded",children:[e.waitlisted," Waitlisted"]})]})]},s))})})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Recent Decisions"}),(0,r.jsx)(a.SZ,{children:"Latest admission decisions made"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsx)("div",{className:"space-y-3",children:[{name:"Riya Patel",decision:"Approved",grade:"Grade 3",date:"2024-02-15",score:88},{name:"Vikram Singh",decision:"Rejected",grade:"Grade 10",date:"2024-02-15",score:65},{name:"Ananya Gupta",decision:"Approved",grade:"Grade 7",date:"2024-02-14",score:92},{name:"Rohit Kumar",decision:"Waitlisted",grade:"Grade 12",date:"2024-02-14",score:75},{name:"Priya Sharma",decision:"Approved",grade:"Grade 5",date:"2024-02-13",score:85}].map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between border-b pb-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.name}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[e.grade," • Score: ",e.score]}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:e.date})]}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Approved"===e.decision?"bg-green-100 text-green-800":"Rejected"===e.decision?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:e.decision})]},s))})})]})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Bulk Decision Actions"}),(0,r.jsx)(a.SZ,{children:"Process multiple applications at once"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,r.jsxs)("div",{className:"border rounded-lg p-4 text-center",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"Auto-Approve High Scorers"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Automatically approve applications with score ≥ 85"}),(0,r.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm",children:"Auto-Approve (12 applications)"})]}),(0,r.jsxs)("div",{className:"border rounded-lg p-4 text-center",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"Waitlist Borderline Cases"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Waitlist applications with score 70-75"}),(0,r.jsx)("button",{className:"bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 text-sm",children:"Auto-Waitlist (8 applications)"})]}),(0,r.jsxs)("div",{className:"border rounded-lg p-4 text-center",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"Send Decision Letters"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Send notifications to all decided applications"}),(0,r.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm",children:"Send Notifications (156 letters)"})]})]})})]})]})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return n},aY:function(){return m},SZ:function(){return x},Ol:function(){return c},ll:function(){return o}});var r=t(7437),a=t(2265),i=t(7042),l=t(4769);function d(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,l.m6)((0,i.W)(s))}let n=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});n.displayName="Card";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:d("flex flex-col space-y-1.5 p-6",t),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:d("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let x=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:d("text-sm text-muted-foreground",t),...a})});x.displayName="CardDescription";let m=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:d("p-6 pt-0",t),...a})});m.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:d("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=1925)}),_N_E=e.O()}]);