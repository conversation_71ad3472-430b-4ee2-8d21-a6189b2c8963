(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7544],{4912:function(e,s,l){Promise.resolve().then(l.bind(l,6285))},6285:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return c}});var a=l(7437);l(2265);var t=l(9197);function c(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Class Allocation"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Allocate admitted students to classes and sections"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50",children:"Auto-Allocate"}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Finalize Allocation"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(t.Zb,{children:[(0,a.jsxs)(t.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(t.ll,{className:"text-sm font-medium",children:"Admitted Students"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"})]}),(0,a.jsxs)(t.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"456"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Ready for allocation"})]})]}),(0,a.jsxs)(t.Zb,{children:[(0,a.jsxs)(t.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(t.ll,{className:"text-sm font-medium",children:"Allocated"}),(0,a.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,a.jsxs)(t.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"398"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"87% allocated"})]})]}),(0,a.jsxs)(t.Zb,{children:[(0,a.jsxs)(t.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(t.ll,{className:"text-sm font-medium",children:"Pending Allocation"}),(0,a.jsx)("span",{className:"text-2xl",children:"⏳"})]}),(0,a.jsxs)(t.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"58"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Awaiting placement"})]})]}),(0,a.jsxs)(t.Zb,{children:[(0,a.jsxs)(t.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(t.ll,{className:"text-sm font-medium",children:"Available Seats"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83E\uDE91"})]}),(0,a.jsxs)(t.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"142"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Across all classes"})]})]})]}),(0,a.jsxs)(t.Zb,{children:[(0,a.jsxs)(t.Ol,{children:[(0,a.jsx)(t.ll,{children:"Class Capacity Overview"}),(0,a.jsx)(t.SZ,{children:"Current occupancy and available seats by grade"})]}),(0,a.jsx)(t.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{grade:"Nursery",sections:["A","B"],capacity:60,allocated:53,available:7,pending:5},{grade:"Grade 1",sections:["A","B","C"],capacity:90,allocated:75,available:15,pending:8},{grade:"Grade 6",sections:["A","B","C"],capacity:90,allocated:82,available:8,pending:12},{grade:"Grade 9",sections:["A","B","C","D"],capacity:120,allocated:105,available:15,pending:22},{grade:"Grade 11",sections:["Science A","Science B","Commerce A"],capacity:90,allocated:83,available:7,pending:11}].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.grade}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.sections.length," sections: ",e.sections.join(", ")]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm font-medium",children:[e.allocated,"/",e.capacity," allocated"]}),(0,a.jsxs)("p",{className:"text-xs text-gray-600",children:[e.pending," pending allocation"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4 text-sm mb-3",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"font-medium text-gray-700",children:"Total Capacity"}),(0,a.jsx)("p",{className:"text-lg font-bold text-blue-600",children:e.capacity})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"font-medium text-gray-700",children:"Allocated"}),(0,a.jsx)("p",{className:"text-lg font-bold text-green-600",children:e.allocated})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"font-medium text-gray-700",children:"Available"}),(0,a.jsx)("p",{className:"text-lg font-bold text-orange-600",children:e.available})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"font-medium text-gray-700",children:"Pending"}),(0,a.jsx)("p",{className:"text-lg font-bold text-red-600",children:e.pending})]})]}),(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsxs)("div",{className:"flex h-3 rounded-full overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-green-600",style:{width:"".concat(e.allocated/e.capacity*100,"%")}}),(0,a.jsx)("div",{className:"bg-yellow-600",style:{width:"".concat(e.pending/e.capacity*100,"%")}})]})}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,a.jsxs)("span",{children:["Allocated: ",Math.round(e.allocated/e.capacity*100),"%"]}),(0,a.jsxs)("span",{children:["Available: ",e.available," seats"]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"View Sections"}),(0,a.jsx)("button",{className:"border border-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-50",children:"Allocate Students"}),e.pending>e.available&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:"Oversubscribed"})]})]},s))})})]}),(0,a.jsxs)(t.Zb,{children:[(0,a.jsxs)(t.Ol,{children:[(0,a.jsx)(t.ll,{children:"Students Pending Allocation"}),(0,a.jsx)(t.SZ,{children:"Admitted students awaiting class assignment"})]}),(0,a.jsx)(t.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{name:"Aarav Sharma",id:"APP-2024-001",grade:"Grade 9",admissionScore:88,preferences:["Science Stream"],specialNeeds:null,siblingClass:null},{name:"Kavya Gupta",id:"APP-2024-015",grade:"Grade 6",admissionScore:82,preferences:["Section A"],specialNeeds:"Learning Support",siblingClass:"Grade 4-B"},{name:"Arjun Singh",id:"APP-2024-032",grade:"Grade 11",admissionScore:80,preferences:["Science Stream","Commerce Stream"],specialNeeds:null,siblingClass:null}].map((e,s)=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.name}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e.grade}),e.specialNeeds&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full",children:"Special Needs"}),e.siblingClass&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full",children:"Sibling"})]}),(0,a.jsx)("p",{className:"text-blue-600 font-medium",children:e.id}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDCCA Admission Score:"})," ",e.admissionScore,"/100"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83C\uDFAF Preferences:"})," ",e.preferences.join(", ")]})]}),(0,a.jsxs)("div",{children:[e.specialNeeds&&(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDD27 Special Needs:"})," ",e.specialNeeds]}),e.siblingClass&&(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"\uD83D\uDC68‍\uD83D\uDC69‍\uD83D\uDC67‍\uD83D\uDC66 Sibling in:"})," ",e.siblingClass]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,a.jsxs)("select",{className:"px-3 py-1 border border-gray-300 rounded text-sm",children:[(0,a.jsx)("option",{value:"",children:"Select Section"}),(0,a.jsx)("option",{value:"9A",children:"Grade 9-A (28/30)"}),(0,a.jsx)("option",{value:"9B",children:"Grade 9-B (25/30)"}),(0,a.jsx)("option",{value:"9C",children:"Grade 9-C (27/30)"}),(0,a.jsx)("option",{value:"9D",children:"Grade 9-D (25/30)"})]}),(0,a.jsx)("button",{className:"bg-green-600 text-white px-4 py-1 rounded text-sm hover:bg-green-700",children:"Allocate"}),(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm",children:"View Profile"})]})]})},s))})})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(t.Zb,{children:[(0,a.jsxs)(t.Ol,{children:[(0,a.jsx)(t.ll,{children:"Auto-Allocation Rules"}),(0,a.jsx)(t.SZ,{children:"Configure automatic allocation criteria"})]}),(0,a.jsx)(t.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"border rounded p-3",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Score-Based Allocation"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Allocate students based on admission scores"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",className:"rounded"}),(0,a.jsx)("label",{className:"text-sm",children:"Enable score-based allocation"})]})]}),(0,a.jsxs)("div",{className:"border rounded p-3",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Sibling Preference"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Keep siblings in same section when possible"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",className:"rounded",defaultChecked:!0}),(0,a.jsx)("label",{className:"text-sm",children:"Prioritize sibling placement"})]})]}),(0,a.jsxs)("div",{className:"border rounded p-3",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Special Needs Support"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Allocate to sections with support facilities"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",className:"rounded",defaultChecked:!0}),(0,a.jsx)("label",{className:"text-sm",children:"Consider special needs"})]})]}),(0,a.jsx)("button",{className:"w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700",children:"Run Auto-Allocation"})]})})]}),(0,a.jsxs)(t.Zb,{children:[(0,a.jsxs)(t.Ol,{children:[(0,a.jsx)(t.ll,{children:"Allocation Summary"}),(0,a.jsx)(t.SZ,{children:"Current allocation status"})]}),(0,a.jsxs)(t.aY,{children:[(0,a.jsx)("div",{className:"space-y-3",children:[{status:"Successfully Allocated",count:398,color:"bg-green-100 text-green-800"},{status:"Pending Allocation",count:58,color:"bg-yellow-100 text-yellow-800"},{status:"Allocation Conflicts",count:12,color:"bg-red-100 text-red-800"},{status:"Special Considerations",count:23,color:"bg-purple-100 text-purple-800"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e.status}),(0,a.jsxs)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(e.color),children:[e.count," students"]})]},s))}),(0,a.jsxs)("div",{className:"mt-4 pt-3 border-t",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between font-medium",children:[(0,a.jsx)("span",{children:"Total Students"}),(0,a.jsx)("span",{className:"text-blue-600",children:"456"})]}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"".concat(398/456*100,"%")}})}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[Math.round(398/456*100),"% allocation complete"]})]})]})]})]})]})]})}},9197:function(e,s,l){"use strict";l.d(s,{Zb:function(){return d},aY:function(){return m},SZ:function(){return o},Ol:function(){return r},ll:function(){return x}});var a=l(7437),t=l(2265),c=l(7042),n=l(4769);function i(){for(var e=arguments.length,s=Array(e),l=0;l<e;l++)s[l]=arguments[l];return(0,n.m6)((0,c.W)(s))}let d=t.forwardRef((e,s)=>{let{className:l,...t}=e;return(0,a.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",l),...t})});d.displayName="Card";let r=t.forwardRef((e,s)=>{let{className:l,...t}=e;return(0,a.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",l),...t})});r.displayName="CardHeader";let x=t.forwardRef((e,s)=>{let{className:l,...t}=e;return(0,a.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",l),...t})});x.displayName="CardTitle";let o=t.forwardRef((e,s)=>{let{className:l,...t}=e;return(0,a.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",l),...t})});o.displayName="CardDescription";let m=t.forwardRef((e,s)=>{let{className:l,...t}=e;return(0,a.jsx)("div",{ref:s,className:i("p-6 pt-0",l),...t})});m.displayName="CardContent",t.forwardRef((e,s)=>{let{className:l,...t}=e;return(0,a.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",l),...t})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=4912)}),_N_E=e.O()}]);