(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7453],{3303:function(e,s,t){Promise.resolve().then(t.bind(t,9860))},9860:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return i}});var a=t(7437);t(2265);var r=t(1396),l=t.n(r),n=t(9197);function i(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Student Admissions"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage student applications, interviews, and enrollment process"})]})}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Applications"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCB"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"1,234"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+12% from last year"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Pending Interviews"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFAF"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"89"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"This week"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Admitted Students"}),(0,a.jsx)("span",{className:"text-2xl",children:"✅"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"456"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"85% acceptance rate"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Enrolled Students"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"398"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"87% enrollment rate"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsx)(l(),{href:"/dashboard/admissions/applications",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCB"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Applications"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Review and process student applications"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Applications →"})]})]})}),(0,a.jsx)(l(),{href:"/dashboard/admissions/interviews",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFAF"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Interviews"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Schedule and conduct admission interviews"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Interviews →"})]})]})}),(0,a.jsx)(l(),{href:"/dashboard/admissions/decisions",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"✅"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Decisions"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Make admission decisions and notifications"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Decisions →"})]})]})}),(0,a.jsx)(l(),{href:"/dashboard/admissions/classes",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFEB"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Class Allocation"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Allocate students to classes and sections"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Classes →"})]})]})}),(0,a.jsx)(l(),{href:"/dashboard/admissions/enrollment",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Enrollment"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Complete student enrollment process"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Enrollment →"})]})]})}),(0,a.jsx)(l(),{href:"/dashboard/admissions/reports",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Reports"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Generate admission reports and analytics"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Reports →"})]})]})})]})]})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return d},aY:function(){return o},SZ:function(){return m},Ol:function(){return c},ll:function(){return x}});var a=t(7437),r=t(2265),l=t(7042),n=t(4769);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,l.W)(s))}let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});d.displayName="Card";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",t),...r})});c.displayName="CardHeader";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",t),...r})});x.displayName="CardTitle";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",t),...r})});m.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:i("p-6 pt-0",t),...r})});o.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},1396:function(e,s,t){e.exports=t(5250)}},function(e){e.O(0,[7895,5250,2971,4938,1744],function(){return e(e.s=3303)}),_N_E=e.O()}]);