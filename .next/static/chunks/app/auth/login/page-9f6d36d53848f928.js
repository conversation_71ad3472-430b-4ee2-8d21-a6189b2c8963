(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6716],{9579:function(e,r,t){Promise.resolve().then(t.bind(t,133))},133:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return o}});var s=t(7437),a=t(2265),l=t(4033),n=t(3082),i=t(9197);function o(){let[e,r]=(0,a.useState)(""),[t,o]=(0,a.useState)(""),[c,d]=(0,a.useState)(!1),[m,u]=(0,a.useState)(""),x=(0,l.useRouter)(),p=(0,l.useSearchParams)().get("redirectTo")||"/dashboard",f=(0,n.createClientComponentClient)(),h=async r=>{r.preventDefault(),d(!0),u("");try{let{data:r,error:s}=await f.auth.signInWithPassword({email:e,password:t});if(s){u(s.message);return}r.user&&(x.push(p),x.refresh())}catch(e){u("An unexpected error occurred")}finally{d(!1)}},y=async()=>{d(!0),u("");try{let{error:e}=await f.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/auth/callback?redirectTo=").concat(encodeURIComponent(p))}});e&&u(e.message)}catch(e){u("An unexpected error occurred")}finally{d(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Access your EMS dashboard"})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[(0,s.jsx)(i.ll,{children:"Welcome back"}),(0,s.jsx)(i.SZ,{children:"Enter your credentials to access your account"})]}),(0,s.jsxs)(i.aY,{children:[(0,s.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[m&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:m}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>r(e.target.value),className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Enter your email"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:t,onChange:e=>o(e.target.value),className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Enter your password"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)("a",{href:"/auth/forgot-password",className:"font-medium text-primary hover:text-primary/80",children:"Forgot your password?"})})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:c,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:c?"Signing in...":"Sign in"})}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("button",{type:"button",onClick:y,disabled:c,className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),(0,s.jsx)("span",{className:"ml-2",children:"Sign in with Google"})]})})]})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,s.jsx)("a",{href:"/auth/signup",className:"font-medium text-primary hover:text-primary/80",children:"Sign up"})]})})]})]})]})})}},9197:function(e,r,t){"use strict";t.d(r,{Zb:function(){return o},aY:function(){return u},SZ:function(){return m},Ol:function(){return c},ll:function(){return d}});var s=t(7437),a=t(2265),l=t(7042),n=t(4769);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.m6)((0,l.W)(r))}let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});o.displayName="Card";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:i("flex flex-col space-y-1.5 p-6",t),...a})});c.displayName="CardHeader";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:i("text-2xl font-semibold leading-none tracking-tight",t),...a})});d.displayName="CardTitle";let m=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:i("text-sm text-muted-foreground",t),...a})});m.displayName="CardDescription";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:i("p-6 pt-0",t),...a})});u.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:i("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,8757,2971,4938,1744],function(){return e(e.s=9579)}),_N_E=e.O()}]);