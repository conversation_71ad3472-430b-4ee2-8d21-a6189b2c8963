(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7308],{2463:function(e,s,t){Promise.resolve().then(t.bind(t,1163))},1163:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return r}});var a=t(7437),n=t(2265),l=t(9197);function i(e){let{tenantId:s,onPostJob:t,onApplyToJob:i,onViewJob:r,onEditJob:c}=e,[o,d]=(0,n.useState)(""),[m,u]=(0,n.useState)("all"),[p,x]=(0,n.useState)("all"),[h,j]=(0,n.useState)("all"),[g,f]=(0,n.useState)("all"),[y,b]=(0,n.useState)(!1),[v,N]=(0,n.useState)(null),w=[{id:"1",jobId:"JOB-2024-001",title:"Senior Software Engineer",company:"TechCorp Inc.",location:{city:"San Francisco",state:"CA",country:"USA",remote:!0,hybrid:!0},type:"full_time",level:"senior",department:"Engineering",industry:"Technology",description:"We are looking for a Senior Software Engineer to join our growing team. You will be responsible for designing and implementing scalable software solutions.",requirements:["5+ years of software development experience","Strong proficiency in JavaScript, React, and Node.js","Experience with cloud platforms (AWS, Azure, or GCP)","Bachelor's degree in Computer Science or related field"],responsibilities:["Design and develop high-quality software solutions","Collaborate with cross-functional teams","Mentor junior developers","Participate in code reviews and technical discussions"],skills:["JavaScript","React","Node.js","AWS","Python","Docker"],benefits:["Competitive salary and equity","Health, dental, and vision insurance","Flexible work arrangements","401(k) with company matching","Professional development budget"],salary:{min:12e4,max:18e4,currency:"USD",negotiable:!0,equity:!0},postedBy:{alumniId:"ALU-2020-001",name:"John Smith",title:"Engineering Manager",company:"TechCorp Inc.",verified:!0},applicationDeadline:"2024-03-15",applicationMethod:{type:"email",contact:"<EMAIL>",instructions:"Please include your resume and a cover letter explaining your interest in the role."},tags:["Remote","Full-time","Senior Level","JavaScript","React"],applicationsCount:45,viewsCount:234,status:"active",featured:!0,postedDate:"2024-01-20",lastUpdated:"2024-01-25"},{id:"2",jobId:"JOB-2024-002",title:"Product Manager",company:"InnovateTech Startup",location:{city:"Austin",state:"TX",country:"USA",remote:!1,hybrid:!0},type:"full_time",level:"mid",department:"Product",industry:"Technology",description:"Join our dynamic startup as a Product Manager and help shape the future of our innovative platform.",requirements:["3+ years of product management experience","Experience with agile development methodologies","Strong analytical and problem-solving skills","MBA or equivalent experience preferred"],responsibilities:["Define product roadmap and strategy","Work closely with engineering and design teams","Conduct market research and user interviews","Analyze product metrics and user feedback"],skills:["Product Management","Agile","Analytics","User Research","Roadmapping"],benefits:["Competitive salary and significant equity","Health and wellness benefits","Flexible PTO policy","Learning and development opportunities"],salary:{min:9e4,max:13e4,currency:"USD",negotiable:!0,equity:!0},postedBy:{alumniId:"ALU-2021-003",name:"Michael Brown",title:"Founder & CEO",company:"InnovateTech Startup",verified:!0},applicationDeadline:"2024-02-28",applicationMethod:{type:"website",contact:"https://innovatetech.com/careers/product-manager",instructions:"Apply through our careers page with your resume and portfolio."},tags:["Hybrid","Full-time","Mid Level","Product","Startup"],applicationsCount:28,viewsCount:156,status:"active",featured:!1,postedDate:"2024-01-15",lastUpdated:"2024-01-22"},{id:"3",jobId:"JOB-2024-003",title:"Data Scientist Intern",company:"MedTech Solutions",location:{city:"Boston",state:"MA",country:"USA",remote:!0,hybrid:!1},type:"internship",level:"entry",department:"Research & Development",industry:"Healthcare",description:"Summer internship opportunity for students interested in applying data science to healthcare challenges.",requirements:["Currently pursuing degree in Data Science, Statistics, or related field","Proficiency in Python and R","Knowledge of machine learning algorithms","Strong communication skills"],responsibilities:["Analyze healthcare datasets","Develop predictive models","Create data visualizations","Present findings to research team"],skills:["Python","R","Machine Learning","Statistics","Data Visualization"],benefits:["Competitive internship stipend","Mentorship from senior data scientists","Opportunity for full-time offer","Remote work flexibility"],salary:{min:25,max:30,currency:"USD",negotiable:!1,equity:!1},postedBy:{alumniId:"ALU-2019-002",name:"Dr. Sarah Johnson",title:"Research Director",company:"MedTech Solutions",verified:!0},applicationDeadline:"2024-03-01",applicationMethod:{type:"email",contact:"<EMAIL>",instructions:"Send resume, transcript, and a brief cover letter."},tags:["Remote","Internship","Entry Level","Data Science","Healthcare"],applicationsCount:67,viewsCount:289,status:"active",featured:!1,postedDate:"2024-01-10",lastUpdated:"2024-01-18"}],C=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"closed":default:return"bg-gray-100 text-gray-800";case"filled":return"bg-blue-100 text-blue-800";case"expired":return"bg-red-100 text-red-800"}},S=e=>{switch(e){case"full_time":return"bg-blue-100 text-blue-800";case"part_time":return"bg-green-100 text-green-800";case"contract":return"bg-purple-100 text-purple-800";case"internship":return"bg-orange-100 text-orange-800";case"freelance":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},k=e=>{switch(e){case"entry":return"bg-green-100 text-green-800";case"mid":return"bg-blue-100 text-blue-800";case"senior":return"bg-purple-100 text-purple-800";case"executive":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},A=w.filter(e=>{let s=e.title.toLowerCase().includes(o.toLowerCase())||e.company.toLowerCase().includes(o.toLowerCase())||e.skills.some(e=>e.toLowerCase().includes(o.toLowerCase())),t="all"===m||e.location.country===m,a="all"===p||e.type===p,n="all"===h||e.level===h,l="all"===g||e.industry===g,i=!y||e.location.remote;return s&&t&&a&&n&&l&&i});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Job Board"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Discover career opportunities from the alumni network"})]}),(0,a.jsx)("button",{onClick:t,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Post a Job"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Active Jobs"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:w.filter(e=>"active"===e.status).length})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Remote Jobs"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:w.filter(e=>e.location.remote).length})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"This Week"})}),(0,a.jsxs)(l.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"5"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"New postings"})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Total Applications"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:w.reduce((e,s)=>e+s.applicationsCount,0)})})]})]}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search jobs, companies, skills",value:o,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Location"}),(0,a.jsxs)("select",{value:m,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Locations"}),(0,a.jsx)("option",{value:"USA",children:"USA"}),(0,a.jsx)("option",{value:"Canada",children:"Canada"}),(0,a.jsx)("option",{value:"UK",children:"UK"}),(0,a.jsx)("option",{value:"Remote",children:"Remote"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Type"}),(0,a.jsxs)("select",{value:p,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Types"}),(0,a.jsx)("option",{value:"full_time",children:"Full Time"}),(0,a.jsx)("option",{value:"part_time",children:"Part Time"}),(0,a.jsx)("option",{value:"contract",children:"Contract"}),(0,a.jsx)("option",{value:"internship",children:"Internship"}),(0,a.jsx)("option",{value:"freelance",children:"Freelance"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Level"}),(0,a.jsxs)("select",{value:h,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Levels"}),(0,a.jsx)("option",{value:"entry",children:"Entry Level"}),(0,a.jsx)("option",{value:"mid",children:"Mid Level"}),(0,a.jsx)("option",{value:"senior",children:"Senior Level"}),(0,a.jsx)("option",{value:"executive",children:"Executive"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Industry"}),(0,a.jsxs)("select",{value:g,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Industries"}),(0,a.jsx)("option",{value:"Technology",children:"Technology"}),(0,a.jsx)("option",{value:"Healthcare",children:"Healthcare"}),(0,a.jsx)("option",{value:"Finance",children:"Finance"}),(0,a.jsx)("option",{value:"Education",children:"Education"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:y,onChange:e=>b(e.target.checked),className:"mr-2"}),(0,a.jsx)("span",{className:"text-sm",children:"Remote Only"})]})})]})})}),(0,a.jsx)("div",{className:"space-y-4",children:A.map(e=>{var s;return(0,a.jsx)(l.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:(0,a.jsx)(l.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:e.title}),e.featured&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full",children:"FEATURED"}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(C(e.status)),children:e.status.toUpperCase()})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-gray-600 mb-3",children:[(0,a.jsx)("span",{className:"font-medium",children:e.company}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[e.location.city,", ",e.location.state]}),e.location.remote&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:"•"}),(0,a.jsx)("span",{className:"text-green-600",children:"Remote"})]}),e.location.hybrid&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:"•"}),(0,a.jsx)("span",{className:"text-blue-600",children:"Hybrid"})]})]}),(0,a.jsxs)("p",{className:"text-gray-600 mb-4",children:[e.description.substring(0,200),"..."]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(S(e.type)),children:e.type.replace("_"," ").toUpperCase()}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(k(e.level)),children:e.level.toUpperCase()}),e.skills.slice(0,3).map((e,s)=>(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full",children:e},s)),e.skills.length>3&&(0,a.jsxs)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full",children:["+",e.skills.length-3," more"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Posted by ",e.postedBy.name," • ",e.postedDate," • ",e.applicationsCount," applications"]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>N(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"}),(0,a.jsx)("button",{onClick:()=>null==i?void 0:i(e.id),className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Apply"})]})]})]}),(0,a.jsxs)("div",{className:"text-right ml-6",children:[e.salary.min&&(0,a.jsxs)("div",{className:"text-lg font-bold text-green-600",children:["$",e.salary.min.toLocaleString()," - $",null===(s=e.salary.max)||void 0===s?void 0:s.toLocaleString()]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Deadline: ",e.applicationDeadline]})]})]})})},e.id)})}),v&&(0,a.jsx)(()=>{var e;return v?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:v.title}),(0,a.jsx)("button",{onClick:()=>N(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Job Details"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Company:"})," ",v.company]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Location:"})," ",v.location.city,", ",v.location.state,", ",v.location.country]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Type:"}),(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(S(v.type)),children:v.type.replace("_"," ").toUpperCase()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Level:"}),(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(k(v.level)),children:v.level.toUpperCase()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Department:"})," ",v.department]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Industry:"})," ",v.industry]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Remote:"})," ",v.location.remote?"Yes":"No"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Hybrid:"})," ",v.location.hybrid?"Yes":"No"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Application Info"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Deadline:"})," ",v.applicationDeadline]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Applications:"})," ",v.applicationsCount]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Views:"})," ",v.viewsCount]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Posted by:"})," ",v.postedBy.name]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Title:"})," ",v.postedBy.title]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Verified:"})," ",v.postedBy.verified?"✅":"❌"]}),v.salary.min&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Salary:"})," $",v.salary.min.toLocaleString()," - $",null===(e=v.salary.max)||void 0===e?void 0:e.toLocaleString()," ",v.salary.currency]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Status:"}),(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(C(v.status)),children:v.status.toUpperCase()})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Description"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded",children:v.description})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Requirements"}),(0,a.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:v.requirements.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-blue-500 mr-2",children:"•"}),(0,a.jsx)("span",{children:e})]},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Responsibilities"}),(0,a.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:v.responsibilities.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-green-500 mr-2",children:"•"}),(0,a.jsx)("span",{children:e})]},s))})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Required Skills"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:v.skills.map((e,s)=>(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Benefits"}),(0,a.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:v.benefits.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-purple-500 mr-2",children:"•"}),(0,a.jsx)("span",{children:e})]},s))})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"How to Apply"}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded",children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Method:"})," ",v.applicationMethod.type.toUpperCase()]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Contact:"})," ",v.applicationMethod.contact]}),v.applicationMethod.instructions&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Instructions:"})," ",v.applicationMethod.instructions]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>null==i?void 0:i(v.id),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Apply Now"}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Save Job"}),(0,a.jsx)("button",{className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700",children:"Share Job"})]})]})}):null},{})]})}function r(){return(0,a.jsx)(i,{tenantId:"demo-tenant-uuid-1234567890",onPostJob:()=>{console.log("Post new job opportunity")},onApplyToJob:e=>{console.log("Apply to job:",e),alert("Application submitted for job ".concat(e))},onViewJob:e=>{console.log("View job details:",e)},onEditJob:e=>{console.log("Edit job posting:",e)}})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return c},aY:function(){return u},SZ:function(){return m},Ol:function(){return o},ll:function(){return d}});var a=t(7437),n=t(2265),l=t(7042),i=t(4769);function r(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,i.m6)((0,l.W)(s))}let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:r("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});c.displayName="Card";let o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:r("flex flex-col space-y-1.5 p-6",t),...n})});o.displayName="CardHeader";let d=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("h3",{ref:s,className:r("text-2xl font-semibold leading-none tracking-tight",t),...n})});d.displayName="CardTitle";let m=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("p",{ref:s,className:r("text-sm text-muted-foreground",t),...n})});m.displayName="CardDescription";let u=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:r("p-6 pt-0",t),...n})});u.displayName="CardContent",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:r("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=2463)}),_N_E=e.O()}]);