(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8285],{2443:function(e,s,n){Promise.resolve().then(n.bind(n,2960))},2960:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return r}});var a=n(7437),t=n(2265),i=n(9197);function l(e){let{tenantId:s,onCreateCampaign:n,onProcessDonation:l,onSendReceipt:r,onSendAcknowledgment:d}=e,[c,o]=(0,t.useState)("donations"),[m,x]=(0,t.useState)(""),[u,h]=(0,t.useState)("all"),[p,g]=(0,t.useState)("all"),[j,f]=(0,t.useState)(null),b=[{id:"1",donationId:"DON-2024-001",donorId:"ALU-2020-001",donorName:"<PERSON>",donorEmail:"<EMAIL>",amount:5e3,currency:"USD",donationType:"one_time",campaign:{id:"CAM-001",name:"Scholarship Fund 2024",category:"scholarship"},purpose:"Supporting underprivileged students",paymentMethod:"credit_card",transactionId:"TXN-*********",donationDate:"2024-01-15",status:"completed",isAnonymous:!1,taxDeductible:!0,receiptSent:!0,acknowledgmentSent:!0,notes:"Annual donation from loyal alumnus",matchingGift:{company:"TechCorp Inc.",amount:5e3,status:"approved"}},{id:"2",donationId:"DON-2024-002",donorId:"ALU-2019-002",donorName:"Sarah Johnson",donorEmail:"<EMAIL>",amount:1e3,currency:"USD",donationType:"recurring",frequency:"monthly",campaign:{id:"CAM-002",name:"Library Renovation Project",category:"infrastructure"},purpose:"Library modernization and expansion",paymentMethod:"bank_transfer",donationDate:"2024-01-20",status:"completed",isAnonymous:!1,taxDeductible:!0,receiptSent:!0,acknowledgmentSent:!1,notes:"Monthly recurring donation"},{id:"3",donationId:"DON-2024-003",donorId:"ALU-2021-003",donorName:"Anonymous Donor",donorEmail:"<EMAIL>",amount:25e3,currency:"USD",donationType:"pledge",campaign:{id:"CAM-003",name:"Research Excellence Initiative",category:"research"},purpose:"Advanced research facilities and equipment",paymentMethod:"check",donationDate:"2024-01-10",status:"pending",isAnonymous:!0,taxDeductible:!0,receiptSent:!1,acknowledgmentSent:!1,notes:"Large pledge commitment over 5 years"}],N=[{id:"CAM-001",name:"Scholarship Fund 2024",description:"Supporting deserving students with financial assistance",category:"scholarship",goal:1e5,raised:65e3,startDate:"2024-01-01",endDate:"2024-12-31",status:"active",donorCount:45},{id:"CAM-002",name:"Library Renovation Project",description:"Modernizing library facilities and resources",category:"infrastructure",goal:25e4,raised:18e4,startDate:"2023-09-01",endDate:"2024-06-30",status:"active",donorCount:78},{id:"CAM-003",name:"Research Excellence Initiative",description:"Advancing research capabilities and innovation",category:"research",goal:5e5,raised:32e4,startDate:"2024-01-01",endDate:"2025-12-31",status:"active",donorCount:32}],y=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"failed":return"bg-red-100 text-red-800";case"refunded":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},v=e=>{switch(e){case"scholarship":return"bg-blue-100 text-blue-800";case"infrastructure":return"bg-green-100 text-green-800";case"research":return"bg-purple-100 text-purple-800";case"athletics":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},C=b.filter(e=>{let s=e.donorName.toLowerCase().includes(m.toLowerCase())||e.donationId.toLowerCase().includes(m.toLowerCase())||e.campaign.name.toLowerCase().includes(m.toLowerCase()),n="all"===u||e.campaign.id===u,a="all"===p||e.status===p;return s&&n&&a});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Donation Tracking"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage alumni contributions and fundraising campaigns"})]}),(0,a.jsx)("button",{onClick:l,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Process Donation"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{className:"pb-3",children:(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Total Raised"})}),(0,a.jsx)(i.aY,{children:(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:["$",b.filter(e=>"completed"===e.status).reduce((e,s)=>e+s.amount,0).toLocaleString()]})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{className:"pb-3",children:(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Active Donors"})}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:new Set(b.map(e=>e.donorId)).size})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{className:"pb-3",children:(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"This Month"})}),(0,a.jsx)(i.aY,{children:(0,a.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:["$",b.filter(e=>e.donationDate.startsWith("2024-01")).reduce((e,s)=>e+s.amount,0).toLocaleString()]})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{className:"pb-3",children:(0,a.jsx)(i.ll,{className:"text-sm font-medium",children:"Avg Donation"})}),(0,a.jsx)(i.aY,{children:(0,a.jsxs)("div",{className:"text-2xl font-bold text-orange-600",children:["$",Math.round(b.reduce((e,s)=>e+s.amount,0)/b.length).toLocaleString()]})})]})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,a.jsxs)("button",{onClick:()=>o("donations"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("donations"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83D\uDCB0 Donations (",b.length,")"]}),(0,a.jsxs)("button",{onClick:()=>o("campaigns"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("campaigns"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83C\uDFAF Campaigns (",N.length,")"]})]})}),(0,a.jsxs)("div",{children:["donations"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by donor name, ID, or campaign",value:m,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Campaign"}),(0,a.jsxs)("select",{value:u,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Campaigns"}),N.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsxs)("select",{value:p,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"failed",children:"Failed"}),(0,a.jsx)("option",{value:"refunded",children:"Refunded"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Donations"})})]})})}),(0,a.jsx)("div",{className:"space-y-4",children:C.map(e=>(0,a.jsx)(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-green-600 font-medium",children:"$"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-lg",children:e.donorName}),(0,a.jsxs)("p",{className:"text-gray-600",children:[e.donationId," • ",e.campaign.name]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(v(e.campaign.category)),children:e.campaign.category.toUpperCase()}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(y(e.status)),children:e.status.toUpperCase()}),"recurring"===e.donationType&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:"RECURRING"})]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:["$",e.amount.toLocaleString()]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.donationDate}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.paymentMethod.replace("_"," ").toUpperCase()}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[e.receiptSent&&(0,a.jsx)("span",{className:"text-green-600 text-xs",children:"✅ Receipt"}),e.acknowledgmentSent&&(0,a.jsx)("span",{className:"text-blue-600 text-xs",children:"✅ Thank You"})]})]})]}),(0,a.jsxs)("div",{className:"mt-4 flex justify-end space-x-2",children:[(0,a.jsx)("button",{onClick:()=>f(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"}),!e.receiptSent&&(0,a.jsx)("button",{onClick:()=>null==r?void 0:r(e.id),className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"Send Receipt"}),!e.acknowledgmentSent&&(0,a.jsx)("button",{onClick:()=>null==d?void 0:d(e.id),className:"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700",children:"Send Thank You"})]})]})},e.id))})]}),"campaigns"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Fundraising Campaigns"}),(0,a.jsx)("button",{onClick:n,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Create Campaign"})]}),(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:N.map(e=>(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(i.ll,{className:"text-lg",children:e.name}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(v(e.category)),children:e.category.toUpperCase()})]}),(0,a.jsx)(i.SZ,{children:e.description})]}),(0,a.jsx)(i.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[(0,a.jsx)("span",{children:"Progress"}),(0,a.jsxs)("span",{children:[(e.raised/e.goal*100).toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"".concat(Math.min(e.raised/e.goal*100,100),"%")}})}),(0,a.jsxs)("div",{className:"flex justify-between text-sm mt-2",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:["Raised: $",e.raised.toLocaleString()]}),(0,a.jsxs)("span",{className:"text-gray-600",children:["Goal: $",e.goal.toLocaleString()]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Donors:"}),(0,a.jsx)("div",{className:"font-medium",children:e.donorCount})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"End Date:"}),(0,a.jsx)("div",{className:"font-medium",children:e.endDate})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Campaign"})})]})})]},e.id))})]})]}),j&&(0,a.jsx)(()=>j?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:"Donation Details"}),(0,a.jsx)("button",{onClick:()=>f(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Donation Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Donation ID:"})," ",j.donationId]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Amount:"})," $",j.amount.toLocaleString()," ",j.currency]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Type:"})," ",j.donationType.replace("_"," ").toUpperCase()]}),j.frequency&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Frequency:"})," ",j.frequency.toUpperCase()]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Campaign:"})," ",j.campaign.name]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Purpose:"})," ",j.purpose]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Payment Method:"})," ",j.paymentMethod.replace("_"," ").toUpperCase()]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Date:"})," ",j.donationDate]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Status:"}),(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(y(j.status)),children:j.status.toUpperCase()})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Donor Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Donor:"})," ",j.donorName]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Email:"})," ",j.donorEmail]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Anonymous:"})," ",j.isAnonymous?"Yes":"No"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Tax Deductible:"})," ",j.taxDeductible?"Yes":"No"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Receipt Sent:"})," ",j.receiptSent?"✅":"❌"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Acknowledgment Sent:"})," ",j.acknowledgmentSent?"✅":"❌"]}),j.transactionId&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Transaction ID:"})," ",j.transactionId]})]})]})]}),j.matchingGift&&(0,a.jsxs)(i.Zb,{className:"mb-6",children:[(0,a.jsx)(i.Ol,{children:(0,a.jsx)(i.ll,{className:"text-lg",children:"Matching Gift"})}),(0,a.jsx)(i.aY,{children:(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Company:"})," ",j.matchingGift.company]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Amount:"})," $",j.matchingGift.amount.toLocaleString()]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Status:"}),(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(y(j.matchingGift.status)),children:j.matchingGift.status.toUpperCase()})]})]})})]}),j.notes&&(0,a.jsxs)(i.Zb,{className:"mb-6",children:[(0,a.jsx)(i.Ol,{children:(0,a.jsx)(i.ll,{className:"text-lg",children:"Notes"})}),(0,a.jsx)(i.aY,{children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:j.notes})})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[!j.receiptSent&&(0,a.jsx)("button",{onClick:()=>null==r?void 0:r(j.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Send Receipt"}),!j.acknowledgmentSent&&(0,a.jsx)("button",{onClick:()=>null==d?void 0:d(j.id),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Send Thank You"}),(0,a.jsx)("button",{className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700",children:"Generate Report"})]})]})}):null,{})]})}function r(){return(0,a.jsx)(l,{tenantId:"demo-tenant-uuid-*********0",onCreateCampaign:()=>{console.log("Create new fundraising campaign")},onProcessDonation:()=>{console.log("Process new donation")},onSendReceipt:e=>{console.log("Send receipt for donation:",e),alert("Receipt sent for donation ".concat(e))},onSendAcknowledgment:e=>{console.log("Send acknowledgment for donation:",e),alert("Thank you note sent for donation ".concat(e))}})}},9197:function(e,s,n){"use strict";n.d(s,{Zb:function(){return d},aY:function(){return x},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var a=n(7437),t=n(2265),i=n(7042),l=n(4769);function r(){for(var e=arguments.length,s=Array(e),n=0;n<e;n++)s[n]=arguments[n];return(0,l.m6)((0,i.W)(s))}let d=t.forwardRef((e,s)=>{let{className:n,...t}=e;return(0,a.jsx)("div",{ref:s,className:r("rounded-lg border bg-card text-card-foreground shadow-sm",n),...t})});d.displayName="Card";let c=t.forwardRef((e,s)=>{let{className:n,...t}=e;return(0,a.jsx)("div",{ref:s,className:r("flex flex-col space-y-1.5 p-6",n),...t})});c.displayName="CardHeader";let o=t.forwardRef((e,s)=>{let{className:n,...t}=e;return(0,a.jsx)("h3",{ref:s,className:r("text-2xl font-semibold leading-none tracking-tight",n),...t})});o.displayName="CardTitle";let m=t.forwardRef((e,s)=>{let{className:n,...t}=e;return(0,a.jsx)("p",{ref:s,className:r("text-sm text-muted-foreground",n),...t})});m.displayName="CardDescription";let x=t.forwardRef((e,s)=>{let{className:n,...t}=e;return(0,a.jsx)("div",{ref:s,className:r("p-6 pt-0",n),...t})});x.displayName="CardContent",t.forwardRef((e,s)=>{let{className:n,...t}=e;return(0,a.jsx)("div",{ref:s,className:r("flex items-center p-6 pt-0",n),...t})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=2443)}),_N_E=e.O()}]);