(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2959],{4200:function(e,s,n){Promise.resolve().then(n.bind(n,4615))},4615:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return r}});var a=n(7437),l=n(2265),t=n(9197);function i(e){let{tenantId:s,onViewProfile:n,onEditProfile:i,onConnectWithAlumni:r}=e,[o,c]=(0,l.useState)(""),[d,m]=(0,l.useState)("all"),[h,x]=(0,l.useState)("all"),[u,p]=(0,l.useState)("all"),[j,f]=(0,l.useState)("all"),[g,b]=(0,l.useState)(null),[v,N]=(0,l.useState)(!1),y=[{id:"1",alumniId:"ALU-2020-001",firstName:"<PERSON>",lastName:"<PERSON>",fullName:"<PERSON>",email:"<EMAIL>",phone:"******-567-8901",graduationYear:2020,degree:"Bachelor of Technology",major:"Computer Science",currentPosition:"Senior Software Engineer",currentCompany:"TechCorp Inc.",industry:"Technology",location:{city:"San Francisco",state:"California",country:"USA"},linkedinUrl:"https://linkedin.com/in/johnsmith",bio:"Passionate software engineer with expertise in full-stack development and cloud technologies.",achievements:["Led development of award-winning mobile app","Speaker at Tech Conference 2023","Published research paper on AI applications"],skills:["JavaScript","React","Node.js","AWS","Python"],interests:["Technology","Mentorship","Startups"],mentorshipAvailable:!0,jobReferralsAvailable:!0,speakingAvailable:!0,donationHistory:{totalAmount:5e3,lastDonation:"2024-01-15",donationCount:3},networkingPreferences:{openToNetworking:!0,preferredContactMethod:"linkedin",availableForMentorship:!0},privacySettings:{showEmail:!0,showPhone:!1,showLocation:!0,showCompany:!0},lastUpdated:"2024-01-20",joinedDate:"2020-06-15",status:"active"},{id:"2",alumniId:"ALU-2019-002",firstName:"Sarah",lastName:"Johnson",fullName:"Sarah Johnson",email:"<EMAIL>",phone:"+1-************",graduationYear:2019,degree:"Master of Science",major:"Biotechnology",currentPosition:"Research Director",currentCompany:"MedTech Solutions",industry:"Healthcare",location:{city:"Boston",state:"Massachusetts",country:"USA"},linkedinUrl:"https://linkedin.com/in/sarahjohnson",personalWebsite:"https://sarahjohnson.com",bio:"Biotech researcher focused on developing innovative medical solutions and leading cross-functional teams.",achievements:["Patent holder for medical device innovation","Featured in Forbes 30 Under 30","Led $10M research project"],skills:["Biotechnology","Research","Team Leadership","Project Management"],interests:["Medical Research","Innovation","Public Speaking"],mentorshipAvailable:!0,jobReferralsAvailable:!1,speakingAvailable:!0,donationHistory:{totalAmount:12e3,lastDonation:"2024-01-10",donationCount:5},networkingPreferences:{openToNetworking:!0,preferredContactMethod:"email",availableForMentorship:!0},privacySettings:{showEmail:!0,showPhone:!0,showLocation:!0,showCompany:!0},lastUpdated:"2024-01-18",joinedDate:"2019-05-20",status:"active"},{id:"3",alumniId:"ALU-2021-003",firstName:"Michael",lastName:"Brown",fullName:"Michael Brown",email:"<EMAIL>",phone:"******-567-8903",graduationYear:2021,degree:"Bachelor of Business Administration",major:"Entrepreneurship",currentPosition:"Founder & CEO",currentCompany:"InnovateTech Startup",industry:"Technology",location:{city:"Austin",state:"Texas",country:"USA"},linkedinUrl:"https://linkedin.com/in/michaelbrown",personalWebsite:"https://innovatetech.com",bio:"Serial entrepreneur passionate about building innovative solutions and mentoring the next generation of founders.",achievements:["Raised $5M in Series A funding","Named Entrepreneur of the Year 2023","Mentor at TechStars accelerator"],skills:["Entrepreneurship","Business Development","Fundraising","Leadership"],interests:["Startups","Mentorship","Angel Investing"],mentorshipAvailable:!0,jobReferralsAvailable:!0,speakingAvailable:!0,donationHistory:{totalAmount:25e3,lastDonation:"2024-01-05",donationCount:2},networkingPreferences:{openToNetworking:!0,preferredContactMethod:"linkedin",availableForMentorship:!0},privacySettings:{showEmail:!1,showPhone:!1,showLocation:!0,showCompany:!0},lastUpdated:"2024-01-22",joinedDate:"2021-07-10",status:"active"}],w=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"inactive":default:return"bg-gray-100 text-gray-800";case"pending_verification":return"bg-yellow-100 text-yellow-800"}},k=y.filter(e=>{let s=e.fullName.toLowerCase().includes(o.toLowerCase())||e.currentCompany.toLowerCase().includes(o.toLowerCase())||e.major.toLowerCase().includes(o.toLowerCase()),n="all"===d||e.graduationYear.toString()===d,a="all"===h||e.degree===h,l="all"===u||e.industry===u,t="all"===j||e.location.country===j;return s&&n&&a&&l&&t});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Alumni Directory"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Connect with fellow alumni and expand your network"})]}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Update My Profile"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)(t.Zb,{children:[(0,a.jsx)(t.Ol,{className:"pb-3",children:(0,a.jsx)(t.ll,{className:"text-sm font-medium",children:"Total Alumni"})}),(0,a.jsx)(t.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:y.length})})]}),(0,a.jsxs)(t.Zb,{children:[(0,a.jsx)(t.Ol,{className:"pb-3",children:(0,a.jsx)(t.ll,{className:"text-sm font-medium",children:"Active Members"})}),(0,a.jsx)(t.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:y.filter(e=>"active"===e.status).length})})]}),(0,a.jsxs)(t.Zb,{children:[(0,a.jsx)(t.Ol,{className:"pb-3",children:(0,a.jsx)(t.ll,{className:"text-sm font-medium",children:"Mentors Available"})}),(0,a.jsx)(t.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:y.filter(e=>e.mentorshipAvailable).length})})]}),(0,a.jsxs)(t.Zb,{children:[(0,a.jsx)(t.Ol,{className:"pb-3",children:(0,a.jsx)(t.ll,{className:"text-sm font-medium",children:"Recent Updates"})}),(0,a.jsxs)(t.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:"12"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"This month"})]})]})]}),(0,a.jsx)(t.Zb,{children:(0,a.jsx)(t.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by name, company, or major",value:o,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Graduation Year"}),(0,a.jsxs)("select",{value:d,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Years"}),(0,a.jsx)("option",{value:"2024",children:"2024"}),(0,a.jsx)("option",{value:"2023",children:"2023"}),(0,a.jsx)("option",{value:"2022",children:"2022"}),(0,a.jsx)("option",{value:"2021",children:"2021"}),(0,a.jsx)("option",{value:"2020",children:"2020"}),(0,a.jsx)("option",{value:"2019",children:"2019"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Degree"}),(0,a.jsxs)("select",{value:h,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Degrees"}),(0,a.jsx)("option",{value:"Bachelor of Technology",children:"Bachelor of Technology"}),(0,a.jsx)("option",{value:"Master of Science",children:"Master of Science"}),(0,a.jsx)("option",{value:"Bachelor of Business Administration",children:"Bachelor of Business Administration"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Industry"}),(0,a.jsxs)("select",{value:u,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Industries"}),(0,a.jsx)("option",{value:"Technology",children:"Technology"}),(0,a.jsx)("option",{value:"Healthcare",children:"Healthcare"}),(0,a.jsx)("option",{value:"Finance",children:"Finance"}),(0,a.jsx)("option",{value:"Education",children:"Education"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Location"}),(0,a.jsxs)("select",{value:j,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Locations"}),(0,a.jsx)("option",{value:"USA",children:"USA"}),(0,a.jsx)("option",{value:"Canada",children:"Canada"}),(0,a.jsx)("option",{value:"UK",children:"UK"}),(0,a.jsx)("option",{value:"India",children:"India"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Directory"})})]})})}),(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:k.map(e=>(0,a.jsxs)(t.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsxs)(t.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(t.ll,{className:"text-lg",children:e.fullName}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(w(e.status)),children:e.status.replace("_"," ").toUpperCase()})]}),(0,a.jsxs)(t.SZ,{children:[e.currentPosition," at ",e.currentCompany]})]}),(0,a.jsxs)(t.aY,{children:[(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Graduation:"}),(0,a.jsx)("span",{className:"font-medium",children:e.graduationYear})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Degree:"}),(0,a.jsx)("span",{className:"font-medium",children:e.degree})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Major:"}),(0,a.jsx)("span",{className:"font-medium",children:e.major})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Industry:"}),(0,a.jsx)("span",{className:"font-medium",children:e.industry})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Location:"}),(0,a.jsxs)("span",{className:"font-medium",children:[e.location.city,", ",e.location.country]})]})]}),(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap gap-2",children:[e.mentorshipAvailable&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full",children:"Mentor Available"}),e.jobReferralsAvailable&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:"Job Referrals"}),e.speakingAvailable&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full",children:"Speaker"})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["Updated: ",e.lastUpdated]}),(0,a.jsx)("button",{onClick:()=>b(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Profile"})]})]})]},e.id))}),g&&(0,a.jsx)(()=>g?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:g.fullName}),(0,a.jsx)("button",{onClick:()=>b(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Personal Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Alumni ID:"})," ",g.alumniId]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Graduation Year:"})," ",g.graduationYear]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Degree:"})," ",g.degree]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Major:"})," ",g.major]}),g.privacySettings.showEmail&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Email:"})," ",g.email]}),g.privacySettings.showPhone&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Phone:"})," ",g.phone]}),g.privacySettings.showLocation&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Location:"})," ",g.location.city,", ",g.location.state,", ",g.location.country]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Professional Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Position:"})," ",g.currentPosition]}),g.privacySettings.showCompany&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Company:"})," ",g.currentCompany]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Industry:"})," ",g.industry]}),g.linkedinUrl&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"LinkedIn:"}),(0,a.jsx)("a",{href:g.linkedinUrl,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 ml-1",children:"View Profile"})]}),g.personalWebsite&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Website:"}),(0,a.jsx)("a",{href:g.personalWebsite,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 ml-1",children:"Visit Website"})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Bio"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded",children:g.bio})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Skills"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:g.skills.map((e,s)=>(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Interests"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:g.interests.map((e,s)=>(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full",children:e},s))})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Achievements"}),(0,a.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:g.achievements.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-blue-500 mr-2",children:"•"}),(0,a.jsx)("span",{children:e})]},s))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Networking Preferences"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:["Open to Networking: ",(0,a.jsx)("span",{className:g.networkingPreferences.openToNetworking?"text-green-600":"text-red-600",children:g.networkingPreferences.openToNetworking?"Yes":"No"})]}),(0,a.jsxs)("div",{children:["Mentorship Available: ",(0,a.jsx)("span",{className:g.mentorshipAvailable?"text-green-600":"text-red-600",children:g.mentorshipAvailable?"Yes":"No"})]}),(0,a.jsxs)("div",{children:["Job Referrals: ",(0,a.jsx)("span",{className:g.jobReferralsAvailable?"text-green-600":"text-red-600",children:g.jobReferralsAvailable?"Available":"Not Available"})]}),(0,a.jsxs)("div",{children:["Speaking Opportunities: ",(0,a.jsx)("span",{className:g.speakingAvailable?"text-green-600":"text-red-600",children:g.speakingAvailable?"Available":"Not Available"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Contribution History"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:["Total Donations: ",(0,a.jsxs)("span",{className:"font-medium",children:["$",g.donationHistory.totalAmount.toLocaleString()]})]}),(0,a.jsxs)("div",{children:["Number of Donations: ",(0,a.jsx)("span",{className:"font-medium",children:g.donationHistory.donationCount})]}),(0,a.jsxs)("div",{children:["Last Donation: ",(0,a.jsx)("span",{className:"font-medium",children:g.donationHistory.lastDonation})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>null==r?void 0:r(g.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Connect"}),(0,a.jsx)("button",{onClick:()=>null==i?void 0:i(g.id),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Edit Profile"}),(0,a.jsx)("button",{className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700",children:"Send Message"})]})]})}):null,{})]})}function r(){return(0,a.jsx)(i,{tenantId:"demo-tenant-uuid-1234567890",onViewProfile:e=>{console.log("View alumni profile:",e)},onEditProfile:e=>{console.log("Edit alumni profile:",e)},onConnectWithAlumni:e=>{console.log("Connect with alumni:",e),alert("Connection request sent to alumni ".concat(e))}})}},9197:function(e,s,n){"use strict";n.d(s,{Zb:function(){return o},aY:function(){return h},SZ:function(){return m},Ol:function(){return c},ll:function(){return d}});var a=n(7437),l=n(2265),t=n(7042),i=n(4769);function r(){for(var e=arguments.length,s=Array(e),n=0;n<e;n++)s[n]=arguments[n];return(0,i.m6)((0,t.W)(s))}let o=l.forwardRef((e,s)=>{let{className:n,...l}=e;return(0,a.jsx)("div",{ref:s,className:r("rounded-lg border bg-card text-card-foreground shadow-sm",n),...l})});o.displayName="Card";let c=l.forwardRef((e,s)=>{let{className:n,...l}=e;return(0,a.jsx)("div",{ref:s,className:r("flex flex-col space-y-1.5 p-6",n),...l})});c.displayName="CardHeader";let d=l.forwardRef((e,s)=>{let{className:n,...l}=e;return(0,a.jsx)("h3",{ref:s,className:r("text-2xl font-semibold leading-none tracking-tight",n),...l})});d.displayName="CardTitle";let m=l.forwardRef((e,s)=>{let{className:n,...l}=e;return(0,a.jsx)("p",{ref:s,className:r("text-sm text-muted-foreground",n),...l})});m.displayName="CardDescription";let h=l.forwardRef((e,s)=>{let{className:n,...l}=e;return(0,a.jsx)("div",{ref:s,className:r("p-6 pt-0",n),...l})});h.displayName="CardContent",l.forwardRef((e,s)=>{let{className:n,...l}=e;return(0,a.jsx)("div",{ref:s,className:r("flex items-center p-6 pt-0",n),...l})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=4200)}),_N_E=e.O()}]);