(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[87],{251:function(e,s,a){Promise.resolve().then(a.bind(a,4090))},4090:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return l}});var t=a(7437),n=a(2265),i=a(9197);function r(e){let{tenantId:s,onSubmitAchievement:a,onFeatureAchievement:r,onApproveAchievement:l,onCreateAward:c}=e,[d,o]=(0,n.useState)("achievements"),[m,x]=(0,n.useState)(""),[u,h]=(0,n.useState)("all"),[p,g]=(0,n.useState)("all"),[j,v]=(0,n.useState)(null),f=[{id:"1",achievementId:"ACH-2024-001",alumniId:"ALU-2020-001",alumniName:"<PERSON>",graduationYear:2020,title:"Forbes 30 Under 30 in Technology",description:"Recognized for innovative contributions to artificial intelligence and machine learning applications in healthcare.",category:"professional",achievementDate:"2024-01-15",organization:"Forbes Media",recognitionLevel:"national",media:{images:["forbes-award.jpg"],articles:[{title:"Young Innovator Transforms Healthcare with AI",url:"https://forbes.com/john-smith-ai-healthcare",publication:"Forbes"}]},tags:["AI","Healthcare","Innovation","Technology"],verified:!0,featured:!0,likes:245,comments:32,shares:18,submittedDate:"2024-01-16",approvedDate:"2024-01-17",status:"featured"},{id:"2",achievementId:"ACH-2024-002",alumniId:"ALU-2019-002",alumniName:"Sarah Johnson",graduationYear:2019,title:"Breakthrough Medical Device Patent",description:"Developed and patented a revolutionary medical device that improves patient outcomes in cardiac surgery.",category:"innovation",achievementDate:"2024-01-10",organization:"US Patent Office",recognitionLevel:"national",media:{images:["patent-certificate.jpg"],articles:[{title:"Alumni Innovates Life-Saving Medical Device",url:"https://medtech-news.com/sarah-johnson-patent",publication:"MedTech News"}]},tags:["Medical Device","Patent","Innovation","Healthcare"],verified:!0,featured:!1,likes:189,comments:24,shares:12,submittedDate:"2024-01-11",approvedDate:"2024-01-12",status:"approved"},{id:"3",achievementId:"ACH-2024-003",alumniId:"ALU-2021-003",alumniName:"Michael Brown",graduationYear:2021,title:"Successful Series A Funding Round",description:"Led company to raise $10M in Series A funding, scaling the team from 5 to 50 employees.",category:"entrepreneurship",achievementDate:"2024-01-05",organization:"InnovateTech Startup",recognitionLevel:"local",media:{images:["funding-announcement.jpg"],articles:[{title:"Local Startup Raises $10M in Series A",url:"https://techcrunch.com/innovatetech-series-a",publication:"TechCrunch"}]},tags:["Startup","Funding","Entrepreneurship","Leadership"],verified:!0,featured:!1,likes:156,comments:18,shares:9,submittedDate:"2024-01-06",approvedDate:"2024-01-07",status:"approved"}],b=[{id:"1",awardId:"AWD-2024-001",name:"Distinguished Alumni Award",description:"Recognizing exceptional professional achievements and contributions to society",category:"Professional Excellence",criteria:["Outstanding professional achievements","Positive impact on society","Exemplary representation of school values"],recipientId:"ALU-2020-001",recipientName:"John Smith",awardDate:"2024-02-15",presenter:"Alumni Association",significance:"Highest honor bestowed by the Alumni Association",type:"recognition",status:"active"},{id:"2",awardId:"AWD-2024-002",name:"Innovation Excellence Scholarship",description:"Supporting current students pursuing innovative research projects",category:"Innovation Support",criteria:["Demonstrated innovation in research","Potential for significant impact","Financial need consideration"],recipientId:"STU-2024-001",recipientName:"Emily Davis",awardDate:"2024-01-30",presenter:"Research Foundation",significance:"Annual scholarship for promising researchers",amount:25e3,currency:"USD",type:"scholarship",status:"active"}],N=e=>{switch(e){case"professional":return"bg-blue-100 text-blue-800";case"academic":return"bg-green-100 text-green-800";case"entrepreneurship":return"bg-purple-100 text-purple-800";case"social_impact":return"bg-orange-100 text-orange-800";case"innovation":return"bg-red-100 text-red-800";case"leadership":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},y=e=>{switch(e){case"local":return"bg-green-100 text-green-800";case"national":return"bg-blue-100 text-blue-800";case"international":return"bg-purple-100 text-purple-800";case"global":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"featured":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},A=f.filter(e=>{let s=e.title.toLowerCase().includes(m.toLowerCase())||e.alumniName.toLowerCase().includes(m.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(m.toLowerCase())),a="all"===u||e.category===u,t="all"===p||e.recognitionLevel===p;return s&&a&&t});return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Achievement Showcase"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Celebrate and recognize alumni success stories"})]}),(0,t.jsx)("button",{onClick:a,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Submit Achievement"})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,t.jsxs)(i.Zb,{children:[(0,t.jsx)(i.Ol,{className:"pb-3",children:(0,t.jsx)(i.ll,{className:"text-sm font-medium",children:"Total Achievements"})}),(0,t.jsx)(i.aY,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:f.length})})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsx)(i.Ol,{className:"pb-3",children:(0,t.jsx)(i.ll,{className:"text-sm font-medium",children:"Featured"})}),(0,t.jsx)(i.aY,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:f.filter(e=>e.featured).length})})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsx)(i.Ol,{className:"pb-3",children:(0,t.jsx)(i.ll,{className:"text-sm font-medium",children:"This Month"})}),(0,t.jsxs)(i.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"5"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"New submissions"})]})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsx)(i.Ol,{className:"pb-3",children:(0,t.jsx)(i.ll,{className:"text-sm font-medium",children:"Total Engagement"})}),(0,t.jsx)(i.aY,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:f.reduce((e,s)=>e+s.likes+s.comments+s.shares,0)})})]})]}),(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,t.jsxs)("button",{onClick:()=>o("achievements"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("achievements"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83C\uDFC6 Achievements (",f.length,")"]}),(0,t.jsxs)("button",{onClick:()=>o("awards"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("awards"===d?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83E\uDD47 Awards (",b.length,")"]})]})}),(0,t.jsxs)("div",{children:["achievements"===d&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(i.Zb,{children:(0,t.jsx)(i.aY,{className:"pt-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,t.jsx)("input",{type:"text",placeholder:"Search achievements, alumni, or tags",value:m,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),(0,t.jsxs)("select",{value:u,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Categories"}),(0,t.jsx)("option",{value:"professional",children:"Professional"}),(0,t.jsx)("option",{value:"academic",children:"Academic"}),(0,t.jsx)("option",{value:"entrepreneurship",children:"Entrepreneurship"}),(0,t.jsx)("option",{value:"social_impact",children:"Social Impact"}),(0,t.jsx)("option",{value:"innovation",children:"Innovation"}),(0,t.jsx)("option",{value:"leadership",children:"Leadership"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Recognition Level"}),(0,t.jsxs)("select",{value:p,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Levels"}),(0,t.jsx)("option",{value:"local",children:"Local"}),(0,t.jsx)("option",{value:"national",children:"National"}),(0,t.jsx)("option",{value:"international",children:"International"}),(0,t.jsx)("option",{value:"global",children:"Global"})]})]}),(0,t.jsx)("div",{className:"flex items-end",children:(0,t.jsx)("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Achievements"})})]})})}),f.some(e=>e.featured)&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Featured Achievements"}),(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2",children:f.filter(e=>e.featured).map(e=>(0,t.jsxs)(i.Zb,{className:"border-2 border-purple-200 hover:shadow-md transition-shadow",children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-purple-600",children:"⭐"}),(0,t.jsx)(i.ll,{className:"text-lg",children:e.title})]}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(w(e.status)),children:"FEATURED"})]}),(0,t.jsxs)(i.SZ,{children:[e.alumniName," • Class of ",e.graduationYear]})]}),(0,t.jsxs)(i.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex space-x-4 text-sm text-gray-500",children:[(0,t.jsxs)("span",{children:["\uD83D\uDC4D ",e.likes]}),(0,t.jsxs)("span",{children:["\uD83D\uDCAC ",e.comments]}),(0,t.jsxs)("span",{children:["\uD83D\uDCE4 ",e.shares]})]}),(0,t.jsx)("button",{onClick:()=>v(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"})]})]})]},e.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"All Achievements"}),(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:A.map(e=>(0,t.jsxs)(i.Zb,{className:"hover:shadow-md transition-shadow",children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(i.ll,{className:"text-lg",children:e.title}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(w(e.status)),children:e.status.toUpperCase()})]}),(0,t.jsxs)(i.SZ,{children:[e.alumniName," • Class of ",e.graduationYear]})]}),(0,t.jsx)(i.aY,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(N(e.category)),children:e.category.replace("_"," ").toUpperCase()}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(y(e.recognitionLevel)),children:e.recognitionLevel.toUpperCase()})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.description.substring(0,100),"..."]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map((e,s)=>(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full",children:e},s)),e.tags.length>3&&(0,t.jsxs)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full",children:["+",e.tags.length-3]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex space-x-3 text-sm text-gray-500",children:[(0,t.jsxs)("span",{children:["\uD83D\uDC4D ",e.likes]}),(0,t.jsxs)("span",{children:["\uD83D\uDCAC ",e.comments]})]}),(0,t.jsx)("button",{onClick:()=>v(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"})]})]})})]},e.id))})]})]}),"awards"===d&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"Alumni Awards"}),(0,t.jsx)("button",{onClick:c,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Create Award"})]}),(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2",children:b.map(e=>(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{children:[(0,t.jsx)(i.ll,{className:"text-lg",children:e.name}),(0,t.jsx)(i.SZ,{children:e.category})]}),(0,t.jsx)(i.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Recipient:"}),(0,t.jsx)("div",{className:"font-medium",children:e.recipientName})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Award Date:"}),(0,t.jsx)("div",{className:"font-medium",children:e.awardDate})]})]}),e.amount&&(0,t.jsxs)("div",{className:"text-lg font-bold text-green-600",children:["$",e.amount.toLocaleString()," ",e.currency]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"Presenter:"})," ",e.presenter]}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:e.significance})]})})]},e.id))})]})]}),j&&(0,t.jsx)(()=>j?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold",children:j.title}),(0,t.jsx)("button",{onClick:()=>v(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Achievement Details"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Alumni:"})," ",j.alumniName]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Graduation Year:"})," ",j.graduationYear]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Category:"}),(0,t.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(N(j.category)),children:j.category.replace("_"," ").toUpperCase()})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Recognition Level:"}),(0,t.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(y(j.recognitionLevel)),children:j.recognitionLevel.toUpperCase()})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Achievement Date:"})," ",j.achievementDate]}),j.organization&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Organization:"})," ",j.organization]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Verified:"})," ",j.verified?"✅":"❌"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Status:"}),(0,t.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(w(j.status)),children:j.status.toUpperCase()})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Engagement"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Likes:"})," ",j.likes]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Comments:"})," ",j.comments]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Shares:"})," ",j.shares]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Submitted:"})," ",j.submittedDate]}),j.approvedDate&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Approved:"})," ",j.approvedDate]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Featured:"})," ",j.featured?"⭐":"❌"]})]})]})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Description"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded",children:j.description})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Tags"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:j.tags.map((e,s)=>(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e},s))})]}),j.media.articles&&j.media.articles.length>0&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Media Coverage"}),(0,t.jsx)("div",{className:"space-y-2",children:j.media.articles.map((e,s)=>(0,t.jsxs)("div",{className:"border rounded-lg p-3",children:[(0,t.jsx)("h5",{className:"font-medium",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.publication}),(0,t.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 text-sm",children:"Read Article →"})]},s))})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[!j.featured&&"approved"===j.status&&(0,t.jsx)("button",{onClick:()=>null==r?void 0:r(j.id),className:"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700",children:"Feature Achievement"}),"pending"===j.status&&(0,t.jsx)("button",{onClick:()=>null==l?void 0:l(j.id),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Approve Achievement"}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Share Achievement"})]})]})}):null,{})]})}function l(){return(0,t.jsx)(r,{tenantId:"demo-tenant-uuid-1234567890",onSubmitAchievement:()=>{console.log("Submit new achievement")},onFeatureAchievement:e=>{console.log("Feature achievement:",e),alert("Achievement ".concat(e," featured successfully"))},onApproveAchievement:e=>{console.log("Approve achievement:",e),alert("Achievement ".concat(e," approved successfully"))},onCreateAward:()=>{console.log("Create new award")}})}},9197:function(e,s,a){"use strict";a.d(s,{Zb:function(){return c},aY:function(){return x},SZ:function(){return m},Ol:function(){return d},ll:function(){return o}});var t=a(7437),n=a(2265),i=a(7042),r=a(4769);function l(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.m6)((0,i.W)(s))}let c=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:s,className:l("rounded-lg border bg-card text-card-foreground shadow-sm",a),...n})});c.displayName="Card";let d=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:s,className:l("flex flex-col space-y-1.5 p-6",a),...n})});d.displayName="CardHeader";let o=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("h3",{ref:s,className:l("text-2xl font-semibold leading-none tracking-tight",a),...n})});o.displayName="CardTitle";let m=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("p",{ref:s,className:l("text-sm text-muted-foreground",a),...n})});m.displayName="CardDescription";let x=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:s,className:l("p-6 pt-0",a),...n})});x.displayName="CardContent",n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:s,className:l("flex items-center p-6 pt-0",a),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=251)}),_N_E=e.O()}]);