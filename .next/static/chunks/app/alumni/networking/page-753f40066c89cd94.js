(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7803],{8973:function(e,t,r){Promise.resolve().then(r.bind(r,9355))},9355:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return l}});var s=r(7437),n=r(2265),a=r(9197);function i(e){let{tenantId:t,onSendConnectionRequest:r,onRespondToRequest:i,onJoinMentorship:l,onBecomeMentor:o}=e,[c,d]=(0,n.useState)("connections"),[m,u]=(0,n.useState)(""),[x,h]=(0,n.useState)("all"),[p,g]=(0,n.useState)("all"),b=[{id:"1",connectionId:"CON-2024-001",requesterName:"<PERSON>",requesterTitle:"Software Engineer",requesterCompany:"StartupTech",requesterGradYear:2022,targetName:"<PERSON>",targetTitle:"Senior Software Engineer",targetCompany:"TechCorp Inc.",targetGradYear:2020,connectionType:"mentorship",message:"Hi John! I'm a recent graduate working in tech and would love to learn from your experience. Would you be open to a mentorship relationship?",status:"pending",requestDate:"2024-01-25",notes:"Interested in career growth in software engineering"},{id:"2",connectionId:"CON-2024-002",requesterName:"Michael Brown",requesterTitle:"Founder & CEO",requesterCompany:"InnovateTech Startup",requesterGradYear:2021,targetName:"Sarah Johnson",targetTitle:"Research Director",targetCompany:"MedTech Solutions",targetGradYear:2019,connectionType:"business_collaboration",message:"Hello Sarah! I'm working on a healthcare tech startup and would love to explore potential collaboration opportunities.",status:"accepted",requestDate:"2024-01-20",responseDate:"2024-01-22",notes:"Potential partnership in healthcare technology"},{id:"3",connectionId:"CON-2024-003",requesterName:"Alex Wilson",requesterTitle:"Marketing Manager",requesterCompany:"Digital Agency",requesterGradYear:2023,targetName:"Michael Brown",targetTitle:"Founder & CEO",targetCompany:"InnovateTech Startup",targetGradYear:2021,connectionType:"job_referral",message:"Hi Michael! I saw your company is growing rapidly. I'm interested in marketing opportunities and would appreciate any referrals.",status:"pending",requestDate:"2024-01-28",notes:"Looking for marketing role in tech startup"}],j=[{id:"1",mentorId:"ALU-2020-001",mentorName:"John Smith",mentorTitle:"Senior Software Engineer",mentorCompany:"TechCorp Inc.",mentorGradYear:2020,expertise:["Software Development","Career Growth","Technical Leadership"],industries:["Technology","Software"],availability:"high",maxMentees:3,currentMentees:1,bio:"Experienced software engineer passionate about helping new graduates navigate their tech careers.",mentorshipType:"career",preferredCommunication:"video",timeCommitment:"2 hours per month",joinedDate:"2024-01-01",rating:4.8,reviewCount:12},{id:"2",mentorId:"ALU-2019-002",mentorName:"Sarah Johnson",mentorTitle:"Research Director",mentorCompany:"MedTech Solutions",mentorGradYear:2019,expertise:["Research & Development","Healthcare Innovation","Team Leadership"],industries:["Healthcare","Biotechnology"],availability:"medium",maxMentees:2,currentMentees:2,bio:"Healthcare research leader with expertise in medical device innovation and team management.",mentorshipType:"industry_specific",preferredCommunication:"phone",timeCommitment:"1.5 hours per month",joinedDate:"2023-12-15",rating:4.9,reviewCount:8},{id:"3",mentorId:"ALU-2021-003",mentorName:"Michael Brown",mentorTitle:"Founder & CEO",mentorCompany:"InnovateTech Startup",mentorGradYear:2021,expertise:["Entrepreneurship","Fundraising","Business Development"],industries:["Technology","Startups"],availability:"low",maxMentees:1,currentMentees:1,bio:"Serial entrepreneur with experience in building and scaling tech startups.",mentorshipType:"entrepreneurship",preferredCommunication:"video",timeCommitment:"1 hour per month",joinedDate:"2024-01-10",rating:5,reviewCount:3}],f=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"accepted":return"bg-green-100 text-green-800";case"declined":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},y=e=>{switch(e){case"mentorship":return"bg-blue-100 text-blue-800";case"job_referral":return"bg-green-100 text-green-800";case"business_collaboration":return"bg-purple-100 text-purple-800";case"social":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},N=e=>{switch(e){case"high":return"bg-green-100 text-green-800";case"medium":return"bg-yellow-100 text-yellow-800";case"low":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},v=b.filter(e=>{let t=e.requesterName.toLowerCase().includes(m.toLowerCase())||e.targetName.toLowerCase().includes(m.toLowerCase())||e.requesterCompany.toLowerCase().includes(m.toLowerCase()),r="all"===x||e.connectionType===x;return t&&r}),C=j.filter(e=>{let t=e.mentorName.toLowerCase().includes(m.toLowerCase())||e.mentorCompany.toLowerCase().includes(m.toLowerCase())||e.expertise.some(e=>e.toLowerCase().includes(m.toLowerCase())),r="all"===p||e.industries.includes(p);return t&&r});return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Networking Hub"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Connect with alumni for mentorship, collaboration, and career growth"})]}),(0,s.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Send Connection Request"})]}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,s.jsxs)(a.Zb,{children:[(0,s.jsx)(a.Ol,{className:"pb-3",children:(0,s.jsx)(a.ll,{className:"text-sm font-medium",children:"Active Connections"})}),(0,s.jsx)(a.aY,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:b.filter(e=>"accepted"===e.status).length})})]}),(0,s.jsxs)(a.Zb,{children:[(0,s.jsx)(a.Ol,{className:"pb-3",children:(0,s.jsx)(a.ll,{className:"text-sm font-medium",children:"Pending Requests"})}),(0,s.jsx)(a.aY,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:b.filter(e=>"pending"===e.status).length})})]}),(0,s.jsxs)(a.Zb,{children:[(0,s.jsx)(a.Ol,{className:"pb-3",children:(0,s.jsx)(a.ll,{className:"text-sm font-medium",children:"Available Mentors"})}),(0,s.jsx)(a.aY,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:j.filter(e=>e.currentMentees<e.maxMentees).length})})]}),(0,s.jsxs)(a.Zb,{children:[(0,s.jsx)(a.Ol,{className:"pb-3",children:(0,s.jsx)(a.ll,{className:"text-sm font-medium",children:"This Month"})}),(0,s.jsxs)(a.aY,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"8"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"New connections"})]})]})]}),(0,s.jsx)("div",{className:"border-b border-gray-200",children:(0,s.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,s.jsxs)("button",{onClick:()=>d("connections"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("connections"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83E\uDD1D Connections (",b.length,")"]}),(0,s.jsxs)("button",{onClick:()=>d("mentorship"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("mentorship"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83D\uDC68‍\uD83C\uDFEB Mentorship (",j.length,")"]})]})}),(0,s.jsxs)("div",{children:["connections"===c&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(a.Zb,{children:(0,s.jsx)(a.aY,{className:"pt-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,s.jsx)("input",{type:"text",placeholder:"Search by name or company",value:m,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Connection Type"}),(0,s.jsxs)("select",{value:x,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Types"}),(0,s.jsx)("option",{value:"mentorship",children:"Mentorship"}),(0,s.jsx)("option",{value:"job_referral",children:"Job Referral"}),(0,s.jsx)("option",{value:"business_collaboration",children:"Business Collaboration"}),(0,s.jsx)("option",{value:"social",children:"Social"}),(0,s.jsx)("option",{value:"advice",children:"Advice"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Connections"})})]})})}),(0,s.jsx)("div",{className:"space-y-4",children:v.map(e=>(0,s.jsx)(a.Zb,{children:(0,s.jsx)(a.aY,{className:"pt-6",children:(0,s.jsx)("div",{className:"flex items-start justify-between",children:(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold",children:[e.requesterName," → ",e.targetName]}),(0,s.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(y(e.connectionType)),children:e.connectionType.replace("_"," ").toUpperCase()}),(0,s.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(f(e.status)),children:e.status.toUpperCase()})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900",children:"Requester"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.requesterTitle}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.requesterCompany}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Class of ",e.requesterGradYear]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900",children:"Target"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.targetTitle}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.targetCompany}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Class of ",e.targetGradYear]})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Message"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded",children:e.message})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Requested: ",e.requestDate,e.responseDate&&" • Responded: ".concat(e.responseDate)]}),"pending"===e.status&&(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>null==i?void 0:i(e.id,"accept"),className:"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700",children:"Accept"}),(0,s.jsx)("button",{onClick:()=>null==i?void 0:i(e.id,"decline"),className:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700",children:"Decline"})]})]})]})})})},e.id))})]}),"mentorship"===c&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold",children:"Mentorship Programs"}),(0,s.jsx)("button",{onClick:o,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Become a Mentor"})]}),(0,s.jsx)(a.Zb,{children:(0,s.jsx)(a.aY,{className:"pt-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,s.jsx)("input",{type:"text",placeholder:"Search mentors by name, company, or expertise",value:m,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Industry"}),(0,s.jsxs)("select",{value:p,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Industries"}),(0,s.jsx)("option",{value:"Technology",children:"Technology"}),(0,s.jsx)("option",{value:"Healthcare",children:"Healthcare"}),(0,s.jsx)("option",{value:"Finance",children:"Finance"}),(0,s.jsx)("option",{value:"Startups",children:"Startups"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"View My Mentorships"})})]})})}),(0,s.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:C.map(e=>(0,s.jsxs)(a.Zb,{className:"hover:shadow-md transition-shadow",children:[(0,s.jsxs)(a.Ol,{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(a.ll,{className:"text-lg",children:e.mentorName}),(0,s.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(N(e.availability)),children:e.availability.toUpperCase()})]}),(0,s.jsxs)(a.SZ,{children:[e.mentorTitle," at ",e.mentorCompany]})]}),(0,s.jsx)(a.aY,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Expertise"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:e.expertise.map((e,t)=>(0,s.jsx)("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:e},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Industries"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:e.industries.map((e,t)=>(0,s.jsx)("span",{className:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full",children:e},t))})]}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.bio}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Mentees:"}),(0,s.jsxs)("div",{className:"font-medium",children:[e.currentMentees,"/",e.maxMentees]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Rating:"}),(0,s.jsxs)("div",{className:"font-medium",children:["⭐ ",e.rating," (",e.reviewCount,")"]})]})]}),(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Time Commitment:"})," ",e.timeCommitment]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Communication:"})," ",e.preferredCommunication]})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)("button",{onClick:()=>null==l?void 0:l(e.id),disabled:e.currentMentees>=e.maxMentees,className:"px-4 py-2 rounded text-sm ".concat(e.currentMentees>=e.maxMentees?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:e.currentMentees>=e.maxMentees?"Full":"Request Mentorship"})})]})})]},e.id))})]})]})]})}function l(){return(0,s.jsx)(i,{tenantId:"demo-tenant-uuid-1234567890",onSendConnectionRequest:(e,t,r)=>{console.log("Send connection request:",{targetId:e,message:t,type:r}),alert("Connection request sent to ".concat(e))},onRespondToRequest:(e,t)=>{console.log("Respond to connection request:",{requestId:e,response:t}),alert("Connection request ".concat(t,"ed"))},onJoinMentorship:e=>{console.log("Join mentorship program:",e),alert("Mentorship request sent for program ".concat(e))},onBecomeMentor:()=>{console.log("Become a mentor"),alert("Mentor application submitted for review")}})}},9197:function(e,t,r){"use strict";r.d(t,{Zb:function(){return o},aY:function(){return u},SZ:function(){return m},Ol:function(){return c},ll:function(){return d}});var s=r(7437),n=r(2265),a=r(7042),i=r(4769);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,a.W)(t))}let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:l("rounded-lg border bg-card text-card-foreground shadow-sm",r),...n})});o.displayName="Card";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:l("flex flex-col space-y-1.5 p-6",r),...n})});c.displayName="CardHeader";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("h3",{ref:t,className:l("text-2xl font-semibold leading-none tracking-tight",r),...n})});d.displayName="CardTitle";let m=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("p",{ref:t,className:l("text-sm text-muted-foreground",r),...n})});m.displayName="CardDescription";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:l("p-6 pt-0",r),...n})});u.displayName="CardContent",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:l("flex items-center p-6 pt-0",r),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=8973)}),_N_E=e.O()}]);