(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1299],{5234:function(e,s,t){Promise.resolve().then(t.bind(t,7567))},7567:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return i}});var l=t(7437),a=t(2265),r=t(9197);function n(e){let{tenantId:s,onGenerateBills:t,onSendBill:n,onViewBill:i}=e,[d,c]=(0,a.useState)("Grade 11 - A"),[o,m]=(0,a.useState)("Semester 1"),[x,u]=(0,a.useState)("semester"),[h,p]=(0,a.useState)([]),[j,b]=(0,a.useState)(!1),[g,f]=(0,a.useState)(null),N=[{id:"1",name:"<PERSON>",rollNumber:"11A001",grade:"Grade 11",division:"A",stream:"Science",email:"<EMAIL>",parentEmail:"<EMAIL>"},{id:"2",name:"<PERSON>",rollNumber:"11A002",grade:"Grade 11",division:"A",stream:"Science",email:"<EMAIL>",parentEmail:"<EMAIL>"},{id:"3",name:"Michael Brown",rollNumber:"11A003",grade:"Grade 11",division:"A",stream:"Science",email:"<EMAIL>",parentEmail:"<EMAIL>"}],y=e=>{switch(e){case"draft":default:return"bg-gray-100 text-gray-800";case"generated":return"bg-blue-100 text-blue-800";case"sent":return"bg-green-100 text-green-800";case"paid":return"bg-purple-100 text-purple-800";case"overdue":return"bg-red-100 text-red-800";case"cancelled":return"bg-orange-100 text-orange-800"}},v=(e,s)=>{s?p([...h,e]):p(h.filter(s=>s!==e))},S=e=>{e?p(N.map(e=>e.id)):p([])};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Billing Generation"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Generate and manage student bills"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("button",{className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"View All Bills"}),(0,l.jsxs)("button",{onClick:()=>{h.length>0&&(null==t||t(h,x))},disabled:0===h.length,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:["Generate Bills (",h.length,")"]})]})]}),(0,l.jsxs)(r.Zb,{children:[(0,l.jsxs)(r.Ol,{children:[(0,l.jsx)(r.ll,{children:"Bill Generation Settings"}),(0,l.jsx)(r.SZ,{children:"Configure billing parameters and select students"})]}),(0,l.jsx)(r.aY,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Class"}),(0,l.jsxs)("select",{value:d,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,l.jsx)("option",{value:"Grade 11 - A",children:"Grade 11 - Division A"}),(0,l.jsx)("option",{value:"Grade 11 - B",children:"Grade 11 - Division B"}),(0,l.jsx)("option",{value:"Grade 10 - A",children:"Grade 10 - Division A"}),(0,l.jsx)("option",{value:"Grade 10 - B",children:"Grade 10 - Division B"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Semester"}),(0,l.jsxs)("select",{value:o,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,l.jsx)("option",{value:"Semester 1",children:"Semester 1"}),(0,l.jsx)("option",{value:"Semester 2",children:"Semester 2"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Bill Type"}),(0,l.jsxs)("select",{value:x,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,l.jsx)("option",{value:"semester",children:"Semester Bill"}),(0,l.jsx)("option",{value:"monthly",children:"Monthly Bill"}),(0,l.jsx)("option",{value:"annual",children:"Annual Bill"}),(0,l.jsx)("option",{value:"custom",children:"Custom Bill"})]})]}),(0,l.jsx)("div",{className:"flex items-end",children:(0,l.jsx)("button",{className:"w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Preview Bill"})})]})})]}),(0,l.jsxs)(r.Zb,{children:[(0,l.jsxs)(r.Ol,{children:[(0,l.jsx)(r.ll,{children:"Select Students"}),(0,l.jsx)(r.SZ,{children:"Choose students for bill generation"})]}),(0,l.jsxs)(r.aY,{children:[(0,l.jsx)("div",{className:"mb-4",children:(0,l.jsxs)("label",{className:"flex items-center",children:[(0,l.jsx)("input",{type:"checkbox",checked:h.length===N.length,onChange:e=>S(e.target.checked),className:"mr-2"}),(0,l.jsxs)("span",{className:"font-medium",children:["Select All Students (",N.length,")"]})]})}),(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)("table",{className:"w-full table-auto",children:[(0,l.jsx)("thead",{children:(0,l.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,l.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Select"}),(0,l.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Roll No."}),(0,l.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Student Name"}),(0,l.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Grade"}),(0,l.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Stream"}),(0,l.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Parent Email"}),(0,l.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Last Bill"})]})}),(0,l.jsx)("tbody",{children:N.map(e=>(0,l.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,l.jsx)("td",{className:"py-3 px-4",children:(0,l.jsx)("input",{type:"checkbox",checked:h.includes(e.id),onChange:s=>v(e.id,s.target.checked)})}),(0,l.jsx)("td",{className:"py-3 px-4 font-medium",children:e.rollNumber}),(0,l.jsx)("td",{className:"py-3 px-4",children:e.name}),(0,l.jsx)("td",{className:"py-3 px-4",children:e.grade}),(0,l.jsx)("td",{className:"py-3 px-4",children:e.stream}),(0,l.jsx)("td",{className:"py-3 px-4",children:e.parentEmail}),(0,l.jsx)("td",{className:"py-3 px-4",children:(0,l.jsx)("span",{className:"text-sm text-gray-500",children:"Jan 2024"})})]},e.id))})]})})]})]}),(0,l.jsxs)(r.Zb,{children:[(0,l.jsxs)(r.Ol,{children:[(0,l.jsx)(r.ll,{children:"Recent Bills"}),(0,l.jsx)(r.SZ,{children:"Recently generated bills"})]}),(0,l.jsx)(r.aY,{children:(0,l.jsx)("div",{className:"space-y-3",children:[{id:"1",billNumber:"BILL-2024-001",studentId:"1",studentName:"John Smith",rollNumber:"11A001",academicYear:"2024-25",semester:"Semester 1",items:[{id:"1",categoryId:"tuition",categoryName:"Tuition Fee",description:"Semester 1 tuition fee",amount:15e3,dueDate:"2024-02-15",type:"mandatory"},{id:"2",categoryId:"lab",categoryName:"Laboratory Fee",description:"Science lab usage fee",amount:3e3,dueDate:"2024-02-15",type:"mandatory"},{id:"3",categoryId:"transport",categoryName:"Transport Fee",description:"Monthly transport fee",amount:1500,dueDate:"2024-02-05",type:"optional"}],subtotal:19500,discounts:[{type:"Merit Scholarship",amount:4875,percentage:25}],totalDiscount:4875,totalAmount:14625,dueDate:"2024-02-15",status:"sent",generatedDate:"2024-01-20",sentDate:"2024-01-21",notes:"Merit scholarship applied based on previous year performance"}].map(e=>(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"font-medium",children:e.billNumber}),(0,l.jsxs)("div",{className:"text-sm text-gray-600",children:[e.studentName," (",e.rollNumber,")"]})]}),(0,l.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(y(e.status)),children:e.status.toUpperCase()})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsxs)("div",{className:"font-medium",children:["$",e.totalAmount.toLocaleString()]}),(0,l.jsxs)("div",{className:"text-sm text-gray-500",children:["Due: ",e.dueDate]})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)("button",{onClick:()=>f(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View"}),(0,l.jsx)("button",{onClick:()=>null==n?void 0:n(e.id),className:"text-green-600 hover:text-green-800 text-sm font-medium",children:"Send"})]})]},e.id))})})]}),g&&(0,l.jsx)(()=>g?(0,l.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsxs)("h3",{className:"text-xl font-semibold",children:["Bill Details - ",g.billNumber]}),(0,l.jsx)("button",{onClick:()=>f(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium mb-3",children:"Student Information"}),(0,l.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium",children:"Name:"})," ",g.studentName]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium",children:"Roll Number:"})," ",g.rollNumber]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium",children:"Academic Year:"})," ",g.academicYear]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium",children:"Semester:"})," ",g.semester]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium mb-3",children:"Bill Information"}),(0,l.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium",children:"Bill Number:"})," ",g.billNumber]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium",children:"Generated Date:"})," ",g.generatedDate]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium",children:"Due Date:"})," ",g.dueDate]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium",children:"Status:"}),(0,l.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(y(g.status)),children:g.status.toUpperCase()})]})]})]})]}),(0,l.jsxs)(r.Zb,{className:"mb-6",children:[(0,l.jsx)(r.Ol,{children:(0,l.jsx)(r.ll,{children:"Bill Items"})}),(0,l.jsx)(r.aY,{children:(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)("table",{className:"w-full table-auto",children:[(0,l.jsx)("thead",{children:(0,l.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,l.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Description"}),(0,l.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Type"}),(0,l.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Due Date"}),(0,l.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900",children:"Amount"})]})}),(0,l.jsx)("tbody",{children:g.items.map(e=>(0,l.jsxs)("tr",{className:"border-b border-gray-100",children:[(0,l.jsx)("td",{className:"py-3 px-4",children:(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"font-medium",children:e.categoryName}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})}),(0,l.jsx)("td",{className:"py-3 px-4",children:(0,l.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("mandatory"===e.type?"bg-red-100 text-red-800":"optional"===e.type?"bg-blue-100 text-blue-800":"bg-yellow-100 text-yellow-800"),children:e.type.toUpperCase()})}),(0,l.jsx)("td",{className:"py-3 px-4",children:e.dueDate}),(0,l.jsxs)("td",{className:"py-3 px-4 text-right font-medium",children:["$",e.amount.toLocaleString()]})]},e.id))})]})})})]}),(0,l.jsxs)(r.Zb,{className:"mb-6",children:[(0,l.jsx)(r.Ol,{children:(0,l.jsx)(r.ll,{children:"Bill Summary"})}),(0,l.jsx)(r.aY,{children:(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{children:"Subtotal:"}),(0,l.jsxs)("span",{className:"font-medium",children:["$",g.subtotal.toLocaleString()]})]}),g.discounts.map((e,s)=>(0,l.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,l.jsxs)("span",{children:[e.type," (",e.percentage,"%):"]}),(0,l.jsxs)("span",{children:["-$",e.amount.toLocaleString()]})]},s)),(0,l.jsxs)("div",{className:"border-t pt-3 flex justify-between text-lg font-bold",children:[(0,l.jsx)("span",{children:"Total Amount:"}),(0,l.jsxs)("span",{className:"text-blue-600",children:["$",g.totalAmount.toLocaleString()]})]})]})})]}),g.notes&&(0,l.jsxs)(r.Zb,{className:"mb-6",children:[(0,l.jsx)(r.Ol,{children:(0,l.jsx)(r.ll,{children:"Notes"})}),(0,l.jsx)(r.aY,{children:(0,l.jsx)("p",{className:"text-sm text-gray-600",children:g.notes})})]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,l.jsx)("button",{className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Download PDF"}),(0,l.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Send to Parent"}),(0,l.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Mark as Paid"})]})]})}):null,{})]})}function i(){return(0,l.jsx)(n,{tenantId:"demo-tenant-uuid-1234567890",onGenerateBills:(e,s)=>{console.log("Generate bills for students:",{studentIds:e,billType:s}),alert("Generating ".concat(s," bills for ").concat(e.length," students"))},onSendBill:e=>{console.log("Send bill:",e),alert("Sending bill ".concat(e," to parent"))},onViewBill:e=>{console.log("View bill:",e)}})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return d},aY:function(){return x},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var l=t(7437),a=t(2265),r=t(7042),n=t(4769);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,r.W)(s))}let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,l.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});d.displayName="Card";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,l.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",t),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,l.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let m=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,l.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",t),...a})});m.displayName="CardDescription";let x=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,l.jsx)("div",{ref:s,className:i("p-6 pt-0",t),...a})});x.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,l.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=5234)}),_N_E=e.O()}]);