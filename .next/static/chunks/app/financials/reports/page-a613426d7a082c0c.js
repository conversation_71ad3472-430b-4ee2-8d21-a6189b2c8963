(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5193],{8678:function(e,t,l){Promise.resolve().then(l.bind(l,9826))},9826:function(e,t,l){"use strict";l.r(t),l.d(t,{default:function(){return d}});var s=l(7437),n=l(2265),r=l(9197);function a(e){let{tenantId:t,onExportReport:l,onScheduleReport:a}=e,[d,i]=(0,n.useState)("current_month"),[c,o]=(0,n.useState)("overview"),[x,m]=(0,n.useState)({start:"2024-01-01",end:"2024-01-31"}),h={totalRevenue:245e4,totalCollected:21e5,totalPending:28e4,totalOverdue:7e4,collectionRate:85.7,monthlyTrend:[{month:"Jan",collected:45e4,pending:5e4},{month:"Feb",collected:42e4,pending:45e3},{month:"Mar",collected:48e4,pending:55e3},{month:"Apr",collected:39e4,pending:6e4},{month:"May",collected:36e4,pending:7e4}],paymentMethodBreakdown:[{method:"Online Payment",amount:12e5,percentage:57.1},{method:"Bank Transfer",amount:6e5,percentage:28.6},{method:"Cash",amount:2e5,percentage:9.5},{method:"Cheque",amount:1e5,percentage:4.8}],gradeWiseCollection:[{grade:"Grade 9",totalBilled:48e4,collected:42e4,pending:6e4,collectionRate:87.5},{grade:"Grade 10",totalBilled:52e4,collected:45e4,pending:7e4,collectionRate:86.5},{grade:"Grade 11",totalBilled:68e4,collected:58e4,pending:1e5,collectionRate:85.3},{grade:"Grade 12",totalBilled:77e4,collected:65e4,pending:12e4,collectionRate:84.4}],scholarshipDisbursement:[{program:"Merit Excellence Scholarship",recipients:12,totalAmount:18e4},{program:"Financial Need Assistance",recipients:35,totalAmount:525e3},{program:"Sports Excellence Award",recipients:8,totalAmount:8e4}]},u=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),p=e=>e>=90?"text-green-600":e>=80?"text-yellow-600":"text-red-600";return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Financial Reports"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Comprehensive financial analytics and reporting"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>null==a?void 0:a({type:c,period:d}),className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Schedule Report"}),(0,s.jsx)("button",{onClick:()=>null==l?void 0:l(c,{period:d,dateRange:x}),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Export Report"})]})]}),(0,s.jsx)(r.Zb,{children:(0,s.jsx)(r.aY,{className:"pt-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Report Type"}),(0,s.jsxs)("select",{value:c,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"overview",children:"Financial Overview"}),(0,s.jsx)("option",{value:"collection",children:"Collection Report"}),(0,s.jsx)("option",{value:"grade_wise",children:"Grade-wise Analysis"}),(0,s.jsx)("option",{value:"scholarship",children:"Scholarship Report"}),(0,s.jsx)("option",{value:"payment_method",children:"Payment Method Analysis"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Time Period"}),(0,s.jsxs)("select",{value:d,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"current_month",children:"Current Month"}),(0,s.jsx)("option",{value:"last_month",children:"Last Month"}),(0,s.jsx)("option",{value:"current_quarter",children:"Current Quarter"}),(0,s.jsx)("option",{value:"current_year",children:"Current Academic Year"}),(0,s.jsx)("option",{value:"custom",children:"Custom Range"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Start Date"}),(0,s.jsx)("input",{type:"date",value:x.start,onChange:e=>m({...x,start:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"End Date"}),(0,s.jsx)("input",{type:"date",value:x.end,onChange:e=>m({...x,end:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})})}),(0,s.jsxs)("div",{children:["overview"===c&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,s.jsxs)(r.Zb,{children:[(0,s.jsx)(r.Ol,{className:"pb-3",children:(0,s.jsx)(r.ll,{className:"text-sm font-medium",children:"Total Revenue"})}),(0,s.jsxs)(r.aY,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u(h.totalRevenue)}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Billed amount"})]})]}),(0,s.jsxs)(r.Zb,{children:[(0,s.jsx)(r.Ol,{className:"pb-3",children:(0,s.jsx)(r.ll,{className:"text-sm font-medium",children:"Collected"})}),(0,s.jsxs)(r.aY,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:u(h.totalCollected)}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:[(h.totalCollected/h.totalRevenue*100).toFixed(1),"% of total"]})]})]}),(0,s.jsxs)(r.Zb,{children:[(0,s.jsx)(r.Ol,{className:"pb-3",children:(0,s.jsx)(r.ll,{className:"text-sm font-medium",children:"Pending"})}),(0,s.jsxs)(r.aY,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:u(h.totalPending)}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Outstanding amount"})]})]}),(0,s.jsxs)(r.Zb,{children:[(0,s.jsx)(r.Ol,{className:"pb-3",children:(0,s.jsx)(r.ll,{className:"text-sm font-medium",children:"Overdue"})}),(0,s.jsxs)(r.aY,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:u(h.totalOverdue)}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Past due date"})]})]})]}),(0,s.jsxs)(r.Zb,{children:[(0,s.jsxs)(r.Ol,{children:[(0,s.jsx)(r.ll,{children:"Collection Rate"}),(0,s.jsx)(r.SZ,{children:"Overall fee collection performance"})]}),(0,s.jsx)(r.aY,{children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"text-4xl font-bold text-green-600",children:[h.collectionRate,"%"]}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-4",children:(0,s.jsx)("div",{className:"bg-green-600 h-4 rounded-full",style:{width:"".concat(h.collectionRate,"%")}})}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-2",children:[u(h.totalCollected)," collected out of ",u(h.totalRevenue)," billed"]})]})]})})]}),(0,s.jsxs)(r.Zb,{children:[(0,s.jsxs)(r.Ol,{children:[(0,s.jsx)(r.ll,{children:"Monthly Collection Trend"}),(0,s.jsx)(r.SZ,{children:"Collection vs pending amounts over time"})]}),(0,s.jsx)(r.aY,{children:(0,s.jsx)("div",{className:"space-y-4",children:h.monthlyTrend.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-12 text-sm font-medium",children:e.month}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Collected:"}),(0,s.jsx)("span",{className:"font-medium",children:u(e.collected)})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"".concat(e.collected/(e.collected+e.pending)*100,"%")}})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Pending:"}),(0,s.jsx)("span",{className:"font-medium text-yellow-600",children:u(e.pending)})]})]})]},t))})})]}),(0,s.jsxs)(r.Zb,{children:[(0,s.jsxs)(r.Ol,{children:[(0,s.jsx)(r.ll,{children:"Payment Method Breakdown"}),(0,s.jsx)(r.SZ,{children:"Distribution of payment methods used"})]}),(0,s.jsx)(r.aY,{children:(0,s.jsx)("div",{className:"space-y-3",children:h.paymentMethodBreakdown.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-blue-600 rounded",style:{backgroundColor:"hsl(".concat(60*t,", 70%, 50%)")}}),(0,s.jsx)("span",{className:"font-medium",children:e.method})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("div",{className:"font-medium",children:u(e.amount)}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[e.percentage,"%"]})]})]},t))})})]})]}),"grade_wise"===c&&(0,s.jsxs)(r.Zb,{children:[(0,s.jsxs)(r.Ol,{children:[(0,s.jsx)(r.ll,{children:"Grade-wise Collection Report"}),(0,s.jsx)(r.SZ,{children:"Fee collection performance by grade"})]}),(0,s.jsx)(r.aY,{children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full table-auto",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Grade"}),(0,s.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900",children:"Total Billed"}),(0,s.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900",children:"Collected"}),(0,s.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900",children:"Pending"}),(0,s.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900",children:"Collection Rate"})]})}),(0,s.jsx)("tbody",{children:h.gradeWiseCollection.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b border-gray-100",children:[(0,s.jsx)("td",{className:"py-3 px-4 font-medium",children:e.grade}),(0,s.jsx)("td",{className:"py-3 px-4 text-right",children:u(e.totalBilled)}),(0,s.jsx)("td",{className:"py-3 px-4 text-right text-green-600 font-medium",children:u(e.collected)}),(0,s.jsx)("td",{className:"py-3 px-4 text-right text-yellow-600 font-medium",children:u(e.pending)}),(0,s.jsxs)("td",{className:"py-3 px-4 text-right font-bold ".concat(p(e.collectionRate)),children:[e.collectionRate,"%"]})]},t))})]})})})]}),"scholarship"===c&&(0,s.jsxs)(r.Zb,{children:[(0,s.jsxs)(r.Ol,{children:[(0,s.jsx)(r.ll,{children:"Scholarship Disbursement Report"}),(0,s.jsx)(r.SZ,{children:"Financial aid programs and disbursements"})]}),(0,s.jsx)(r.aY,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[h.scholarshipDisbursement.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium",children:e.program}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[e.recipients," recipients"]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("div",{className:"text-xl font-bold text-purple-600",children:u(e.totalAmount)}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[u(e.totalAmount/e.recipients)," avg per student"]})]})]},t)),(0,s.jsxs)("div",{className:"border-t pt-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"font-medium",children:"Total Disbursed:"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-purple-600",children:u(h.scholarshipDisbursement.reduce((e,t)=>e+t.totalAmount,0))})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center mt-2",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Total Recipients:"}),(0,s.jsxs)("span",{className:"font-medium",children:[h.scholarshipDisbursement.reduce((e,t)=>e+t.recipients,0)," students"]})]})]})]})})]}),("collection"===c||"payment_method"===c)&&(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:["collection"===c?"Collection Report":"Payment Method Analysis"," - Detailed analysis will be displayed here"]})]}),(0,s.jsxs)(r.Zb,{children:[(0,s.jsx)(r.Ol,{children:(0,s.jsx)(r.ll,{children:"Quick Actions"})}),(0,s.jsx)(r.aY,{children:(0,s.jsxs)("div",{className:"grid gap-3 md:grid-cols-2 lg:grid-cols-4",children:[(0,s.jsxs)("button",{className:"p-3 text-left border rounded-lg hover:bg-gray-50",children:[(0,s.jsx)("div",{className:"font-medium",children:"Monthly Summary"}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Generate monthly financial summary"})]}),(0,s.jsxs)("button",{className:"p-3 text-left border rounded-lg hover:bg-gray-50",children:[(0,s.jsx)("div",{className:"font-medium",children:"Overdue Report"}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"List of overdue payments"})]}),(0,s.jsxs)("button",{className:"p-3 text-left border rounded-lg hover:bg-gray-50",children:[(0,s.jsx)("div",{className:"font-medium",children:"Tax Report"}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Generate tax compliance report"})]}),(0,s.jsxs)("button",{className:"p-3 text-left border rounded-lg hover:bg-gray-50",children:[(0,s.jsx)("div",{className:"font-medium",children:"Audit Trail"}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Financial transaction audit"})]})]})})]})]})}function d(){return(0,s.jsx)(a,{tenantId:"demo-tenant-uuid-1234567890",onExportReport:(e,t)=>{console.log("Export report:",{reportType:e,filters:t}),alert("Exporting ".concat(e," report with filters"))},onScheduleReport:e=>{console.log("Schedule report:",e),alert("Scheduling ".concat(e.type," report for ").concat(e.period))}})}},9197:function(e,t,l){"use strict";l.d(t,{Zb:function(){return i},aY:function(){return m},SZ:function(){return x},Ol:function(){return c},ll:function(){return o}});var s=l(7437),n=l(2265),r=l(7042),a=l(4769);function d(){for(var e=arguments.length,t=Array(e),l=0;l<e;l++)t[l]=arguments[l];return(0,a.m6)((0,r.W)(t))}let i=n.forwardRef((e,t)=>{let{className:l,...n}=e;return(0,s.jsx)("div",{ref:t,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",l),...n})});i.displayName="Card";let c=n.forwardRef((e,t)=>{let{className:l,...n}=e;return(0,s.jsx)("div",{ref:t,className:d("flex flex-col space-y-1.5 p-6",l),...n})});c.displayName="CardHeader";let o=n.forwardRef((e,t)=>{let{className:l,...n}=e;return(0,s.jsx)("h3",{ref:t,className:d("text-2xl font-semibold leading-none tracking-tight",l),...n})});o.displayName="CardTitle";let x=n.forwardRef((e,t)=>{let{className:l,...n}=e;return(0,s.jsx)("p",{ref:t,className:d("text-sm text-muted-foreground",l),...n})});x.displayName="CardDescription";let m=n.forwardRef((e,t)=>{let{className:l,...n}=e;return(0,s.jsx)("div",{ref:t,className:d("p-6 pt-0",l),...n})});m.displayName="CardContent",n.forwardRef((e,t)=>{let{className:l,...n}=e;return(0,s.jsx)("div",{ref:t,className:d("flex items-center p-6 pt-0",l),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=8678)}),_N_E=e.O()}]);