(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2478],{9653:function(e,s,t){Promise.resolve().then(t.bind(t,8446))},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return c},aY:function(){return o},SZ:function(){return m},Ol:function(){return i},ll:function(){return x}});var a=t(7437),r=t(2265),l=t(7042),n=t(4769);function d(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,l.W)(s))}let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});c.displayName="Card";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:d("flex flex-col space-y-1.5 p-6",t),...r})});i.displayName="CardHeader";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:d("text-2xl font-semibold leading-none tracking-tight",t),...r})});x.displayName="CardTitle";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:d("text-sm text-muted-foreground",t),...r})});m.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:d("p-6 pt-0",t),...r})});o.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:d("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},8446:function(e,s,t){"use strict";t.r(s),t.d(s,{FinancialsDashboard:function(){return d}});var a=t(7437);t(2265);var r=t(1396),l=t.n(r),n=t(9197);function d(e){let{tenantId:s}=e,t={totalRevenue:125e4,pendingPayments:85e3,overduePayments:25e3,totalStudents:1250,collectionRate:92.5,averageFeePerStudent:1e3},r=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),d=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"failed":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Financial Dashboard"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Student fee management and financial overview"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90",children:"Generate Report"}),(0,a.jsx)("button",{className:"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90",children:"Record Payment"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-6",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Revenue"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:r(t.totalRevenue)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+12% from last month"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Pending Payments"}),(0,a.jsx)("span",{className:"text-2xl",children:"⏳"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:r(t.pendingPayments)}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["From ",t.totalStudents," students"]})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Overdue"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDEA8"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:r(t.overduePayments)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Requires attention"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Students"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC65"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.totalStudents}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active enrollment"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Collection Rate"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.collectionRate,"%"]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"This academic year"})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ll,{className:"text-sm font-medium",children:"Avg Fee/Student"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB3"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:r(t.averageFeePerStudent)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Per month"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsxs)(n.Zb,{className:"lg:col-span-2",children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Recent Transactions"}),(0,a.jsx)(n.SZ,{children:"Latest payment transactions"})]}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{id:"1",student:"John Smith",amount:1500,method:"Online",date:"2024-01-15",status:"completed"},{id:"2",student:"Sarah Johnson",amount:2e3,method:"Bank Transfer",date:"2024-01-15",status:"completed"},{id:"3",student:"Michael Brown",amount:1200,method:"Cash",date:"2024-01-14",status:"pending"},{id:"4",student:"Emily Davis",amount:1800,method:"Credit Card",date:"2024-01-14",status:"completed"}].map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-sm font-medium text-primary",children:e.student.split(" ").map(e=>e[0]).join("")})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.student}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.method})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-medium",children:r(e.amount)}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(d(e.status)),children:e.status}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.date})]})]})]},e.id))})})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Fee Distribution"}),(0,a.jsx)(n.SZ,{children:"Revenue by fee type"})]}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{type:"Tuition",amount:85e4,percentage:68},{type:"Transport",amount:18e4,percentage:14.4},{type:"Laboratory",amount:95e3,percentage:7.6},{type:"Library",amount:65e3,percentage:5.2},{type:"Sports",amount:6e4,percentage:4.8}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-primary",style:{opacity:1-.15*s}}),(0,a.jsx)("span",{className:"text-sm",children:e.type})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:r(e.amount)}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.percentage,"%"]})]})]},s))})})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"Overdue Payments"}),(0,a.jsx)(n.SZ,{children:"Students with overdue fee payments"})]}),(0,a.jsx)(n.aY,{children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full table-auto",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Student"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Class"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Amount"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Days Overdue"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:[{student:"Alex Wilson",class:"Grade 9A",amount:2500,daysOverdue:15},{student:"Lisa Garcia",class:"Grade 10B",amount:1800,daysOverdue:8},{student:"David Miller",class:"Grade 8A",amount:3200,daysOverdue:22},{student:"Maria Rodriguez",class:"Grade 11A",amount:1500,daysOverdue:5}].map((e,s)=>(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"font-medium text-gray-900",children:e.student})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"text-gray-900",children:e.class})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"font-medium text-red-600",children:r(e.amount)})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(e.daysOverdue>15?"bg-red-100 text-red-800":e.daysOverdue>7?"bg-orange-100 text-orange-800":"bg-yellow-100 text-yellow-800"),children:[e.daysOverdue," days"]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"text-primary hover:text-primary/80 text-sm font-medium",children:"Send Reminder"}),(0,a.jsx)("button",{className:"text-gray-600 hover:text-gray-800 text-sm font-medium",children:"View Details"})]})})]},s))})]})})})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB3"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Fee Management"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage fee structures and billing"}),(0,a.jsx)("button",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Fees →"})]})]}),(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Payments"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Process and track payments"}),(0,a.jsx)("button",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Payments →"})]})]}),(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Financial Aid"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage scholarships and aid"}),(0,a.jsx)("button",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Aid →"})]})]}),(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Reports"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Financial reports and analytics"}),(0,a.jsx)("button",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Reports →"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6",children:[(0,a.jsx)(l(),{href:"/financials/fee-structure",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB0"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Fee Structure"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Setup fee categories"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Fees →"})]})]})}),(0,a.jsx)(l(),{href:"/financials/billing",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC4"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Billing"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Generate student bills"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Create Bills →"})]})]})}),(0,a.jsx)(l(),{href:"/financials/payments",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB3"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Payments"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Process payments"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Process Payments →"})]})]})}),(0,a.jsx)(l(),{href:"/financials/financial-aid",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDF93"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Financial Aid"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Scholarships & aid"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Aid →"})]})]})}),(0,a.jsx)(l(),{href:"/financials/reports",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Reports"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Financial analytics"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Reports →"})]})]})}),(0,a.jsx)(l(),{href:"/financials/history",children:(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCB"}),(0,a.jsx)(n.ll,{className:"text-lg",children:"Payment History"})]})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Transaction records"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View History →"})]})]})})]})]})}},1396:function(e,s,t){e.exports=t(5250)}},function(e){e.O(0,[7895,5250,2971,4938,1744],function(){return e(e.s=9653)}),_N_E=e.O()}]);