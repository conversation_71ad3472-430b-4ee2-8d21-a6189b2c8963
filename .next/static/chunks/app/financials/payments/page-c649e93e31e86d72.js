(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7537],{815:function(e,t,s){Promise.resolve().then(s.bind(s,3106))},3106:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return d}});var n=s(7437),a=s(2265),l=s(9197);function r(e){let{tenantId:t,onProcessPayment:s,onGenerateReceipt:r,onRefundPayment:d}=e,[i,c]=(0,a.useState)("pending"),[o,m]=(0,a.useState)(null),[x,u]=(0,a.useState)(!1),[h,p]=(0,a.useState)(null),b=[{id:"1",billId:"BILL-2024-001",billNumber:"BILL-2024-001",studentId:"1",studentName:"<PERSON>",rollNumber:"11A001",totalAmount:14625,paidAmount:0,pendingAmount:14625,dueDate:"2024-02-15",overdueDays:0,lateFee:0},{id:"2",billId:"BILL-2024-002",billNumber:"BILL-2024-002",studentId:"2",studentName:"Sarah Johnson",rollNumber:"11A002",totalAmount:18500,paidAmount:1e4,pendingAmount:8500,dueDate:"2024-02-10",overdueDays:5,lateFee:250}],f=[{id:"1",transactionId:"TXN-2024-001",billId:"BILL-2024-003",billNumber:"BILL-2024-003",studentId:"3",studentName:"Michael Brown",rollNumber:"11A003",amount:15e3,paymentMethod:"online",paymentDate:"2024-01-25",receivedBy:"System",status:"completed",reference:"RAZORPAY_12345",receiptNumber:"RCP-2024-001",notes:"Online payment via Razorpay"},{id:"2",transactionId:"TXN-2024-002",billId:"BILL-2024-004",billNumber:"BILL-2024-004",studentId:"4",studentName:"Emily Davis",rollNumber:"11A004",amount:12e3,paymentMethod:"bank_transfer",paymentDate:"2024-01-22",receivedBy:"Finance Officer",status:"completed",reference:"NEFT123456789",receiptNumber:"RCP-2024-002",notes:"Bank transfer payment"}],g=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"completed":return"bg-green-100 text-green-800";case"failed":return"bg-red-100 text-red-800";case"refunded":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},N=e=>{switch(e){case"cash":return"\uD83D\uDCB5";case"bank_transfer":return"\uD83C\uDFE6";case"online":case"card":return"\uD83D\uDCB3";case"cheque":return"\uD83D\uDCDD";default:return"\uD83D\uDCB0"}},j=e=>{p(e),u(!0)};return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Payment Processing"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Process payments and manage payment records"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("button",{className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Payments"}),(0,n.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Payment Report"})]})]}),(0,n.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,n.jsxs)(l.Zb,{children:[(0,n.jsx)(l.Ol,{className:"pb-3",children:(0,n.jsx)(l.ll,{className:"text-sm font-medium",children:"Pending Payments"})}),(0,n.jsxs)(l.aY,{children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-red-600",children:b.length}),(0,n.jsxs)("p",{className:"text-xs text-gray-500",children:["$",b.reduce((e,t)=>e+t.pendingAmount,0).toLocaleString()," pending"]})]})]}),(0,n.jsxs)(l.Zb,{children:[(0,n.jsx)(l.Ol,{className:"pb-3",children:(0,n.jsx)(l.ll,{className:"text-sm font-medium",children:"Completed Today"})}),(0,n.jsxs)(l.aY,{children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"8"}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"$45,000 collected"})]})]}),(0,n.jsxs)(l.Zb,{children:[(0,n.jsx)(l.Ol,{className:"pb-3",children:(0,n.jsx)(l.ll,{className:"text-sm font-medium",children:"Overdue Payments"})}),(0,n.jsxs)(l.aY,{children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:b.filter(e=>e.overdueDays>0).length}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"Require immediate attention"})]})]}),(0,n.jsxs)(l.Zb,{children:[(0,n.jsx)(l.Ol,{className:"pb-3",children:(0,n.jsx)(l.ll,{className:"text-sm font-medium",children:"This Month"})}),(0,n.jsxs)(l.aY,{children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"$125,000"}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"Total collected"})]})]})]}),(0,n.jsx)("div",{className:"border-b border-gray-200",children:(0,n.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,n.jsxs)("button",{onClick:()=>c("pending"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("pending"===i?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["⏳ Pending Payments (",b.length,")"]}),(0,n.jsxs)("button",{onClick:()=>c("completed"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("completed"===i?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["✅ Completed Payments (",f.length,")"]}),(0,n.jsx)("button",{onClick:()=>c("failed"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("failed"===i?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:"❌ Failed Payments (0)"})]})}),(0,n.jsxs)("div",{children:["pending"===i&&(0,n.jsx)("div",{className:"space-y-4",children:b.map(e=>(0,n.jsx)(l.Zb,{children:(0,n.jsxs)(l.aY,{className:"pt-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-yellow-600 font-medium",children:e.studentName.split(" ").map(e=>e[0]).join("")})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold text-lg",children:e.studentName}),(0,n.jsxs)("p",{className:"text-gray-600",children:[e.rollNumber," • ",e.billNumber]}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["Due: ",e.dueDate]}),e.overdueDays>0&&(0,n.jsxs)("p",{className:"text-sm text-red-600",children:["Overdue by ",e.overdueDays," days"]})]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-red-600",children:["$",e.pendingAmount.toLocaleString()]}),(0,n.jsxs)("div",{className:"text-sm text-gray-500",children:["Paid: $",e.paidAmount.toLocaleString()," / $",e.totalAmount.toLocaleString()]}),e.lateFee>0&&(0,n.jsxs)("div",{className:"text-sm text-red-600",children:["Late Fee: $",e.lateFee]})]})]}),(0,n.jsxs)("div",{className:"mt-4 flex justify-end space-x-2",children:[(0,n.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Bill"}),(0,n.jsx)("button",{onClick:()=>j(e),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Process Payment"})]})]})},e.id))}),"completed"===i&&(0,n.jsx)("div",{className:"space-y-4",children:f.map(e=>(0,n.jsx)(l.Zb,{children:(0,n.jsxs)(l.aY,{className:"pt-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("div",{className:"text-2xl",children:N(e.paymentMethod)}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold text-lg",children:e.studentName}),(0,n.jsxs)("p",{className:"text-gray-600",children:[e.rollNumber," • ",e.billNumber]}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:[e.paymentDate," • ",e.transactionId]}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["Method: ",e.paymentMethod.replace("_"," ").toUpperCase()]})]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:["$",e.amount.toLocaleString()]}),(0,n.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(g(e.status)),children:e.status.toUpperCase()}),(0,n.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:["Receipt: ",e.receiptNumber]})]})]}),(0,n.jsxs)("div",{className:"mt-4 flex justify-end space-x-2",children:[(0,n.jsx)("button",{onClick:()=>null==r?void 0:r(e.id),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"Download Receipt"}),(0,n.jsx)("button",{className:"text-gray-600 hover:text-gray-800 text-sm font-medium",children:"View Details"}),"completed"===e.status&&(0,n.jsx)("button",{onClick:()=>null==d?void 0:d(e.id),className:"text-red-600 hover:text-red-800 text-sm font-medium",children:"Refund"})]})]})},e.id))}),"failed"===i&&(0,n.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No failed payments"})]}),x&&(0,n.jsx)(()=>{if(!h)return null;let[e,t]=(0,a.useState)(h.pendingAmount),[l,r]=(0,a.useState)("cash"),[d,i]=(0,a.useState)(""),[c,o]=(0,a.useState)("");return(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold",children:"Process Payment"}),(0,n.jsx)("button",{onClick:()=>u(!1),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,n.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Payment Details"}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Student:"})," ",h.studentName]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Roll Number:"})," ",h.rollNumber]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Bill Number:"})," ",h.billNumber]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Total Amount:"})," $",h.totalAmount.toLocaleString()]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Paid Amount:"})," $",h.paidAmount.toLocaleString()]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Pending Amount:"})," $",h.pendingAmount.toLocaleString()]})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Payment Amount"}),(0,n.jsx)("input",{type:"number",value:e,onChange:e=>t(Number(e.target.value)),max:h.pendingAmount,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Payment Method"}),(0,n.jsxs)("select",{value:l,onChange:e=>r(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,n.jsx)("option",{value:"cash",children:"Cash"}),(0,n.jsx)("option",{value:"bank_transfer",children:"Bank Transfer"}),(0,n.jsx)("option",{value:"online",children:"Online Payment"}),(0,n.jsx)("option",{value:"cheque",children:"Cheque"}),(0,n.jsx)("option",{value:"card",children:"Card Payment"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Reference Number"}),(0,n.jsx)("input",{type:"text",value:d,onChange:e=>i(e.target.value),placeholder:"Transaction/Reference number",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Notes"}),(0,n.jsx)("textarea",{value:c,onChange:e=>o(e.target.value),rows:3,placeholder:"Additional notes (optional)",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,n.jsx)("button",{onClick:()=>u(!1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,n.jsx)("button",{onClick:()=>{let t={billId:h.billId,studentId:h.studentId,amount:e,paymentMethod:l,reference:d,notes:c};null==s||s(t),u(!1),p(null)},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Process Payment"})]})]})})},{})]})}function d(){return(0,n.jsx)(r,{tenantId:"demo-tenant-uuid-1234567890",onProcessPayment:e=>{console.log("Process payment:",e),alert("Processing payment of $".concat(e.amount," via ").concat(e.paymentMethod))},onGenerateReceipt:e=>{console.log("Generate receipt for payment:",e),alert("Generating receipt for payment ".concat(e))},onRefundPayment:e=>{console.log("Refund payment:",e),alert("Processing refund for payment ".concat(e))}})}},9197:function(e,t,s){"use strict";s.d(t,{Zb:function(){return i},aY:function(){return x},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var n=s(7437),a=s(2265),l=s(7042),r=s(4769);function d(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.m6)((0,l.W)(t))}let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,n.jsx)("div",{ref:t,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});i.displayName="Card";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,n.jsx)("div",{ref:t,className:d("flex flex-col space-y-1.5 p-6",s),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,n.jsx)("h3",{ref:t,className:d("text-2xl font-semibold leading-none tracking-tight",s),...a})});o.displayName="CardTitle";let m=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,n.jsx)("p",{ref:t,className:d("text-sm text-muted-foreground",s),...a})});m.displayName="CardDescription";let x=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,n.jsx)("div",{ref:t,className:d("p-6 pt-0",s),...a})});x.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,n.jsx)("div",{ref:t,className:d("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=815)}),_N_E=e.O()}]);