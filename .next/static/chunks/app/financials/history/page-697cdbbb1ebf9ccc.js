(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3692],{5980:function(e,t,s){Promise.resolve().then(s.bind(s,1628))},1628:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return i}});var a=s(7437),n=s(2265),l=s(9197);function r(e){let{tenantId:t,onViewReceipt:s,onRefundPayment:r,onExportHistory:i}=e,[d,c]=(0,n.useState)(""),[o,m]=(0,n.useState)("all"),[u,x]=(0,n.useState)("all"),[h,p]=(0,n.useState)({start:"",end:""}),[f,j]=(0,n.useState)(null),N=[{id:"1",transactionId:"TXN-2024-001",receiptNumber:"RCP-2024-001",studentId:"1",studentName:"<PERSON>",rollNumber:"11A001",billNumber:"BILL-2024-001",amount:14625,paymentMethod:"online",paymentDate:"2024-01-25",receivedBy:"System",status:"completed",reference:"RAZORPAY_12345",feeCategories:[{name:"Tuition Fee",amount:11250},{name:"Laboratory Fee",amount:2250},{name:"Development Fee",amount:1125}],notes:"Online payment via Razorpay - Merit scholarship applied"},{id:"2",transactionId:"TXN-2024-002",receiptNumber:"RCP-2024-002",studentId:"2",studentName:"Sarah Johnson",rollNumber:"11A002",billNumber:"BILL-2024-002",amount:15e3,paymentMethod:"bank_transfer",paymentDate:"2024-01-22",receivedBy:"Finance Officer",status:"completed",reference:"NEFT123456789",feeCategories:[{name:"Tuition Fee",amount:12e3},{name:"Laboratory Fee",amount:3e3}],notes:"Bank transfer payment - Financial aid applied"},{id:"3",transactionId:"TXN-2024-003",receiptNumber:"RCP-2024-003",studentId:"3",studentName:"Michael Brown",rollNumber:"11A003",billNumber:"BILL-2024-003",amount:8e3,paymentMethod:"cash",paymentDate:"2024-01-20",receivedBy:"Cashier",status:"completed",reference:"CASH-001",feeCategories:[{name:"Transport Fee",amount:4500},{name:"Activity Fee",amount:3500}]},{id:"4",transactionId:"TXN-2024-004",receiptNumber:"RCP-2024-004",studentId:"4",studentName:"Emily Davis",rollNumber:"11A004",billNumber:"BILL-2024-004",amount:5e3,paymentMethod:"cheque",paymentDate:"2024-01-18",receivedBy:"Finance Officer",status:"failed",reference:"CHQ-789456",feeCategories:[{name:"Examination Fee",amount:5e3}],notes:"Cheque bounced - insufficient funds"}],b=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"failed":return"bg-red-100 text-red-800";case"refunded":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},y=e=>{switch(e){case"cash":return"\uD83D\uDCB5";case"bank_transfer":return"\uD83C\uDFE6";case"online":case"card":return"\uD83D\uDCB3";case"cheque":return"\uD83D\uDCDD";default:return"\uD83D\uDCB0"}},g=N.filter(e=>{let t=e.studentName.toLowerCase().includes(d.toLowerCase())||e.rollNumber.toLowerCase().includes(d.toLowerCase())||e.transactionId.toLowerCase().includes(d.toLowerCase())||e.receiptNumber.toLowerCase().includes(d.toLowerCase()),s="all"===o||e.status===o,a="all"===u||e.paymentMethod===u,n=!0;if(h.start&&h.end){let t=new Date(e.paymentDate),s=new Date(h.start),a=new Date(h.end);n=t>=s&&t<=a}return t&&s&&a&&n});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Payment History"}),(0,a.jsx)("p",{className:"text-gray-600",children:"View and manage all payment transactions"})]}),(0,a.jsx)("button",{onClick:()=>null==i?void 0:i({status:o,method:u,dateRange:h,search:d}),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Export History"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Total Transactions"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:N.length})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Successful Payments"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:N.filter(e=>"completed"===e.status).length})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Failed Payments"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:N.filter(e=>"failed"===e.status).length})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Total Amount"})}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:["$",N.filter(e=>"completed"===e.status).reduce((e,t)=>e+t.amount,0).toLocaleString()]})})]})]}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by name, roll number, or transaction ID",value:d,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsxs)("select",{value:o,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"failed",children:"Failed"}),(0,a.jsx)("option",{value:"refunded",children:"Refunded"}),(0,a.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Payment Method"}),(0,a.jsxs)("select",{value:u,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Methods"}),(0,a.jsx)("option",{value:"online",children:"Online Payment"}),(0,a.jsx)("option",{value:"bank_transfer",children:"Bank Transfer"}),(0,a.jsx)("option",{value:"cash",children:"Cash"}),(0,a.jsx)("option",{value:"cheque",children:"Cheque"}),(0,a.jsx)("option",{value:"card",children:"Card"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Start Date"}),(0,a.jsx)("input",{type:"date",value:h.start,onChange:e=>p({...h,start:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"End Date"}),(0,a.jsx)("input",{type:"date",value:h.end,onChange:e=>p({...h,end:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})})}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[(0,a.jsx)(l.ll,{children:"Payment Transactions"}),(0,a.jsxs)(l.SZ,{children:["Showing ",g.length," of ",N.length," transactions"]})]}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full table-auto",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Transaction"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Student"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Amount"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Method"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Date"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:g.map(e=>(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.transactionId}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.receiptNumber})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.studentName}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.rollNumber})]})}),(0,a.jsxs)("td",{className:"py-3 px-4 font-medium",children:["$",e.amount.toLocaleString()]}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:y(e.paymentMethod)}),(0,a.jsx)("span",{className:"capitalize",children:e.paymentMethod.replace("_"," ")})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:e.paymentDate}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(b(e.status)),children:e.status.toUpperCase()})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>j(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View"}),(0,a.jsx)("button",{onClick:()=>null==s?void 0:s(e.id),className:"text-green-600 hover:text-green-800 text-sm font-medium",children:"Receipt"})]})})]},e.id))})]})})})]}),f&&(0,a.jsx)(()=>f?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:"Payment Details"}),(0,a.jsx)("button",{onClick:()=>j(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Transaction Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Transaction ID:"})," ",f.transactionId]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Receipt Number:"})," ",f.receiptNumber]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Bill Number:"})," ",f.billNumber]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Amount:"})," $",f.amount.toLocaleString()]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Payment Date:"})," ",f.paymentDate]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Payment Method:"})," ",f.paymentMethod.replace("_"," ").toUpperCase()]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Reference:"})," ",f.reference]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Status:"}),(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(b(f.status)),children:f.status.toUpperCase()})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Student Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Name:"})," ",f.studentName]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Roll Number:"})," ",f.rollNumber]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Received By:"})," ",f.receivedBy]})]})]})]}),(0,a.jsxs)(l.Zb,{className:"mb-6",children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Fee Breakdown"})}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[f.feeCategories.map((e,t)=>(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsxs)("span",{className:"font-medium",children:["$",e.amount.toLocaleString()]})]},t)),(0,a.jsxs)("div",{className:"border-t pt-2 flex justify-between font-bold",children:[(0,a.jsx)("span",{children:"Total Amount"}),(0,a.jsxs)("span",{children:["$",f.amount.toLocaleString()]})]})]})})]}),f.notes&&(0,a.jsxs)(l.Zb,{className:"mb-6",children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Notes"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:f.notes})})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>null==s?void 0:s(f.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Download Receipt"}),"completed"===f.status&&(0,a.jsx)("button",{onClick:()=>null==r?void 0:r(f.id),className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700",children:"Process Refund"})]})]})}):null,{})]})}function i(){return(0,a.jsx)(r,{tenantId:"demo-tenant-uuid-1234567890",onViewReceipt:e=>{console.log("View receipt for payment:",e),alert("Downloading receipt for payment ".concat(e))},onRefundPayment:e=>{console.log("Refund payment:",e),alert("Processing refund for payment ".concat(e))},onExportHistory:e=>{console.log("Export payment history:",e),alert("Exporting payment history with applied filters")}})}},9197:function(e,t,s){"use strict";s.d(t,{Zb:function(){return d},aY:function(){return u},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var a=s(7437),n=s(2265),l=s(7042),r=s(4769);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.m6)((0,l.W)(t))}let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",s),...n})});d.displayName="Card";let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:i("flex flex-col space-y-1.5 p-6",s),...n})});c.displayName="CardHeader";let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("h3",{ref:t,className:i("text-2xl font-semibold leading-none tracking-tight",s),...n})});o.displayName="CardTitle";let m=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("p",{ref:t,className:i("text-sm text-muted-foreground",s),...n})});m.displayName="CardDescription";let u=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:i("p-6 pt-0",s),...n})});u.displayName="CardContent",n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:i("flex items-center p-6 pt-0",s),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=5980)}),_N_E=e.O()}]);