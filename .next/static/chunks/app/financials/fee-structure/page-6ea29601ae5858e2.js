(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[457],{8277:function(e,t,a){Promise.resolve().then(a.bind(a,2346))},2346:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return c}});var s=a(7437),r=a(2265),l=a(9197);function n(e){let{tenantId:t,onCreateFeeStructure:a,onEditFeeStructure:n}=e,[c,i]=(0,r.useState)("all"),[d,o]=(0,r.useState)("all"),[m,u]=(0,r.useState)("2024-25"),[x,h]=(0,r.useState)(null),[p,f]=(0,r.useState)(!1),j=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"inactive":return"bg-red-100 text-red-800";case"draft":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},g=e=>{switch(e){case"mandatory":return"bg-red-100 text-red-800";case"optional":return"bg-blue-100 text-blue-800";case"conditional":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},y=[{id:"1",name:"Grade 11 Science Stream Fee Structure",academicYear:"2024-25",grade:"Grade 11",stream:"Science",categories:[{id:"1",name:"Tuition Fee",description:"Regular academic instruction fee",type:"mandatory",frequency:"semester",amount:15e3,applicableGrades:["Grade 11"],applicableStreams:["Science"],dueDate:"15th of each semester",lateFeeAmount:500,lateFeeGracePeriod:7,status:"active"},{id:"2",name:"Laboratory Fee",description:"Science laboratory usage and materials",type:"mandatory",frequency:"semester",amount:3e3,applicableGrades:["Grade 11"],applicableStreams:["Science"],dueDate:"15th of each semester",lateFeeAmount:200,lateFeeGracePeriod:7,status:"active"},{id:"3",name:"Development Fee",description:"Infrastructure and facility development",type:"mandatory",frequency:"annual",amount:2e3,applicableGrades:["Grade 11"],applicableStreams:["Science"],dueDate:"Beginning of academic year",lateFeeAmount:100,lateFeeGracePeriod:15,status:"active"},{id:"4",name:"Transport Fee",description:"School bus transportation service",type:"optional",frequency:"monthly",amount:1500,applicableGrades:["Grade 11"],applicableStreams:["Science"],dueDate:"5th of each month",lateFeeAmount:50,lateFeeGracePeriod:3,status:"active"}],totalAmount:21e3,discounts:[{type:"Sibling Discount",percentage:10,amount:2100,conditions:"Second child onwards"},{type:"Merit Scholarship",percentage:25,amount:5250,conditions:"Academic performance above 90%"}],status:"active",createdAt:"2024-01-15",updatedAt:"2024-01-20"},{id:"2",name:"Grade 10 Arts Stream Fee Structure",academicYear:"2024-25",grade:"Grade 10",stream:"Arts",categories:[{id:"5",name:"Tuition Fee",description:"Regular academic instruction fee",type:"mandatory",frequency:"semester",amount:12e3,applicableGrades:["Grade 10"],applicableStreams:["Arts"],dueDate:"15th of each semester",lateFeeAmount:400,lateFeeGracePeriod:7,status:"active"},{id:"6",name:"Activity Fee",description:"Arts and cultural activities",type:"mandatory",frequency:"semester",amount:1500,applicableGrades:["Grade 10"],applicableStreams:["Arts"],dueDate:"15th of each semester",lateFeeAmount:100,lateFeeGracePeriod:7,status:"active"}],totalAmount:13500,discounts:[{type:"Early Payment Discount",percentage:5,amount:675,conditions:"Payment before due date"}],status:"active",createdAt:"2024-01-10",updatedAt:"2024-01-18"}].filter(e=>{let t="all"===c||e.grade===c,a="all"===d||e.stream===d,s=e.academicYear===m;return t&&a&&s});return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Fee Structure Management"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Setup and manage fee structures for different grades and streams"})]}),(0,s.jsx)("button",{onClick:a,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Create Fee Structure"})]}),(0,s.jsx)(l.Zb,{children:(0,s.jsx)(l.aY,{className:"pt-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Academic Year"}),(0,s.jsxs)("select",{value:m,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"2024-25",children:"2024-25"}),(0,s.jsx)("option",{value:"2023-24",children:"2023-24"}),(0,s.jsx)("option",{value:"2025-26",children:"2025-26"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Grade"}),(0,s.jsxs)("select",{value:c,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Grades"}),(0,s.jsx)("option",{value:"Grade 9",children:"Grade 9"}),(0,s.jsx)("option",{value:"Grade 10",children:"Grade 10"}),(0,s.jsx)("option",{value:"Grade 11",children:"Grade 11"}),(0,s.jsx)("option",{value:"Grade 12",children:"Grade 12"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Stream"}),(0,s.jsxs)("select",{value:d,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Streams"}),(0,s.jsx)("option",{value:"Science",children:"Science"}),(0,s.jsx)("option",{value:"Arts",children:"Arts"}),(0,s.jsx)("option",{value:"Commerce",children:"Commerce"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Structures"})})]})})}),(0,s.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:y.map(e=>(0,s.jsxs)(l.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,s.jsxs)(l.Ol,{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(l.ll,{className:"text-lg",children:e.name}),(0,s.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(j(e.status)),children:e.status.toUpperCase()})]}),(0,s.jsxs)(l.SZ,{children:[e.grade," - ",e.stream," Stream"]})]}),(0,s.jsxs)(l.aY,{children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Total Amount:"}),(0,s.jsxs)("span",{className:"text-xl font-bold text-green-600",children:["$",e.totalAmount.toLocaleString()]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Categories:"}),(0,s.jsx)("span",{className:"font-medium",children:e.categories.length})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Discounts:"}),(0,s.jsx)("span",{className:"font-medium",children:e.discounts.length})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Academic Year:"}),(0,s.jsx)("span",{className:"font-medium",children:e.academicYear})]})]}),(0,s.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:["Updated: ",e.updatedAt]}),(0,s.jsx)("button",{onClick:()=>h(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"})]})]})]},e.id))}),x&&(0,s.jsx)(()=>x?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold",children:x.name}),(0,s.jsx)("button",{onClick:()=>h(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[(0,s.jsxs)(l.Zb,{children:[(0,s.jsx)(l.Ol,{className:"pb-3",children:(0,s.jsx)(l.ll,{className:"text-lg",children:"Academic Year"})}),(0,s.jsx)(l.aY,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:x.academicYear})})]}),(0,s.jsxs)(l.Zb,{children:[(0,s.jsx)(l.Ol,{className:"pb-3",children:(0,s.jsx)(l.ll,{className:"text-lg",children:"Grade & Stream"})}),(0,s.jsxs)(l.aY,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:x.grade}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[x.stream," Stream"]})]})]}),(0,s.jsxs)(l.Zb,{children:[(0,s.jsx)(l.Ol,{className:"pb-3",children:(0,s.jsx)(l.ll,{className:"text-lg",children:"Total Amount"})}),(0,s.jsxs)(l.aY,{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:["$",x.totalAmount.toLocaleString()]}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Per academic year"})]})]})]}),(0,s.jsxs)(l.Zb,{className:"mb-6",children:[(0,s.jsx)(l.Ol,{children:(0,s.jsx)(l.ll,{children:"Fee Categories"})}),(0,s.jsx)(l.aY,{children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full table-auto",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Category"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Type"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Amount"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Frequency"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Due Date"}),(0,s.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Late Fee"})]})}),(0,s.jsx)("tbody",{children:x.categories.map(e=>(0,s.jsxs)("tr",{className:"border-b border-gray-100",children:[(0,s.jsx)("td",{className:"py-3 px-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.name}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})}),(0,s.jsx)("td",{className:"py-3 px-4",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(g(e.type)),children:e.type.toUpperCase()})}),(0,s.jsxs)("td",{className:"py-3 px-4 font-medium",children:["$",e.amount.toLocaleString()]}),(0,s.jsx)("td",{className:"py-3 px-4 capitalize",children:e.frequency.replace("_"," ")}),(0,s.jsx)("td",{className:"py-3 px-4",children:e.dueDate}),(0,s.jsx)("td",{className:"py-3 px-4",children:(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsxs)("div",{children:["$",e.lateFeeAmount]}),(0,s.jsxs)("div",{className:"text-gray-500",children:[e.lateFeeGracePeriod," days grace"]})]})})]},e.id))})]})})})]}),(0,s.jsxs)(l.Zb,{className:"mb-6",children:[(0,s.jsx)(l.Ol,{children:(0,s.jsx)(l.ll,{children:"Available Discounts"})}),(0,s.jsx)(l.aY,{children:(0,s.jsx)("div",{className:"grid gap-4 md:grid-cols-2",children:x.discounts.map((e,t)=>(0,s.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"font-medium",children:e.type}),(0,s.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[e.percentage,"%"]}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["Save $",e.amount.toLocaleString()]}),(0,s.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:e.conditions})]},t))})})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsx)("button",{onClick:()=>null==n?void 0:n(x.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Edit Structure"}),(0,s.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Duplicate Structure"})]})]})}):null,{})]})}function c(){return(0,s.jsx)(n,{tenantId:"demo-tenant-uuid-1234567890",onCreateFeeStructure:()=>{console.log("Create new fee structure")},onEditFeeStructure:e=>{console.log("Edit fee structure:",e)}})}},9197:function(e,t,a){"use strict";a.d(t,{Zb:function(){return i},aY:function(){return u},SZ:function(){return m},Ol:function(){return d},ll:function(){return o}});var s=a(7437),r=a(2265),l=a(7042),n=a(4769);function c(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.m6)((0,l.W)(t))}let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:c("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});i.displayName="Card";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:c("flex flex-col space-y-1.5 p-6",a),...r})});d.displayName="CardHeader";let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("h3",{ref:t,className:c("text-2xl font-semibold leading-none tracking-tight",a),...r})});o.displayName="CardTitle";let m=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("p",{ref:t,className:c("text-sm text-muted-foreground",a),...r})});m.displayName="CardDescription";let u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:c("p-6 pt-0",a),...r})});u.displayName="CardContent",r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:c("flex items-center p-6 pt-0",a),...r})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=8277)}),_N_E=e.O()}]);