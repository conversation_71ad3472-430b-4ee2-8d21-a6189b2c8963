(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5801],{8508:function(e,s,t){Promise.resolve().then(t.bind(t,1051))},1051:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return r}});var a=t(7437),i=t(2265),l=t(9197);function n(e){let{tenantId:s,onCreateScholarship:t,onReviewApplication:n,onApproveApplication:r}=e,[c,d]=(0,i.useState)("programs"),[o,m]=(0,i.useState)(null),[x,p]=(0,i.useState)(!1),u=[{id:"1",name:"Merit Excellence Scholarship",description:"For students with outstanding academic performance",type:"merit",amount:0,percentage:50,maxAmount:25e3,eligibilityCriteria:["Minimum 90% in previous academic year","No disciplinary issues","Regular attendance (>95%)"],applicationDeadline:"2024-03-15",academicYear:"2024-25",totalSlots:20,filledSlots:12,status:"active"},{id:"2",name:"Financial Need Assistance",description:"Support for students from economically disadvantaged backgrounds",type:"need_based",amount:15e3,eligibilityCriteria:["Family income below $30,000 annually","Submission of income certificate","Minimum 75% academic performance"],applicationDeadline:"2024-04-01",academicYear:"2024-25",totalSlots:50,filledSlots:35,status:"active"},{id:"3",name:"Sports Excellence Award",description:"For students excelling in sports and athletics",type:"sports",amount:1e4,eligibilityCriteria:["State/National level sports participation","Minimum 70% academic performance","Active participation in school sports"],applicationDeadline:"2024-02-28",academicYear:"2024-25",totalSlots:10,filledSlots:8,status:"active"}],h=[{id:"1",applicationNumber:"FA-2024-001",studentId:"1",studentName:"John Smith",rollNumber:"11A001",grade:"Grade 11",scholarshipId:"1",scholarshipName:"Merit Excellence Scholarship",appliedAmount:25e3,familyIncome:45e3,academicPerformance:92,applicationDate:"2024-01-15",status:"under_review",documents:[{name:"Academic Transcripts",status:"verified"},{name:"Income Certificate",status:"verified"},{name:"Application Form",status:"verified"}]},{id:"2",applicationNumber:"FA-2024-002",studentId:"2",studentName:"Sarah Johnson",rollNumber:"11A002",grade:"Grade 11",scholarshipId:"2",scholarshipName:"Financial Need Assistance",appliedAmount:15e3,familyIncome:25e3,academicPerformance:78,applicationDate:"2024-01-20",status:"approved",approvedAmount:15e3,reviewNotes:"Meets all criteria for need-based assistance",documents:[{name:"Academic Transcripts",status:"verified"},{name:"Income Certificate",status:"verified"},{name:"Bank Statements",status:"verified"}]}],j=e=>{switch(e){case"active":case"approved":return"bg-green-100 text-green-800";case"inactive":default:return"bg-gray-100 text-gray-800";case"closed":case"rejected":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";case"under_review":return"bg-blue-100 text-blue-800";case"waitlisted":return"bg-orange-100 text-orange-800"}},f=e=>{switch(e){case"merit":return"bg-blue-100 text-blue-800";case"need_based":return"bg-green-100 text-green-800";case"sports":return"bg-orange-100 text-orange-800";case"arts":return"bg-purple-100 text-purple-800";case"minority":return"bg-pink-100 text-pink-800";default:return"bg-gray-100 text-gray-800"}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Financial Aid Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage scholarships and financial assistance programs"})]}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Financial Aid Report"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Active Programs"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u.filter(e=>"active"===e.status).length})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Pending Applications"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:h.filter(e=>"pending"===e.status||"under_review"===e.status).length})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Approved This Year"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:h.filter(e=>"approved"===e.status).length})})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{className:"pb-3",children:(0,a.jsx)(l.ll,{className:"text-sm font-medium",children:"Total Aid Disbursed"})}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:["$",h.filter(e=>"approved"===e.status).reduce((e,s)=>e+(s.approvedAmount||0),0).toLocaleString()]})})]})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,a.jsxs)("button",{onClick:()=>d("programs"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("programs"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83C\uDF93 Scholarship Programs (",u.length,")"]}),(0,a.jsxs)("button",{onClick:()=>d("applications"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("applications"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:["\uD83D\uDCCB Applications (",h.length,")"]})]})}),(0,a.jsxs)("div",{children:["programs"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Scholarship Programs"}),(0,a.jsx)("button",{onClick:t,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Create Program"})]}),(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:u.map(e=>(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(l.ll,{className:"text-lg",children:e.name}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(j(e.status)),children:e.status.toUpperCase()})]}),(0,a.jsx)(l.SZ,{children:e.description})]}),(0,a.jsxs)(l.aY,{children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Type:"}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(f(e.type)),children:e.type.replace("_"," ").toUpperCase()})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Amount:"}),(0,a.jsxs)("span",{className:"font-medium",children:[e.percentage?"".concat(e.percentage,"%"):"$".concat(e.amount.toLocaleString()),e.maxAmount&&" (max $".concat(e.maxAmount.toLocaleString(),")")]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Slots:"}),(0,a.jsxs)("span",{className:"font-medium",children:[e.filledSlots,"/",e.totalSlots]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Deadline:"}),(0,a.jsx)("span",{className:"font-medium",children:e.applicationDeadline})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm mb-2",children:"Eligibility Criteria:"}),(0,a.jsx)("ul",{className:"text-xs text-gray-600 space-y-1",children:e.eligibilityCriteria.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-blue-500 mr-1",children:"•"}),(0,a.jsx)("span",{children:e})]},s))})]}),(0,a.jsx)("div",{className:"mt-4 flex justify-end",children:(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"Edit Program"})})]})]},e.id))})]}),"applications"===c&&(0,a.jsx)("div",{className:"space-y-4",children:h.map(e=>(0,a.jsx)(l.Zb,{children:(0,a.jsxs)(l.aY,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:e.studentName.split(" ").map(e=>e[0]).join("")})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-lg",children:e.studentName}),(0,a.jsxs)("p",{className:"text-gray-600",children:[e.rollNumber," • ",e.applicationNumber]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.scholarshipName}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Applied: ",e.applicationDate]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-xl font-bold text-green-600",children:["$",e.appliedAmount.toLocaleString()]}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(j(e.status)),children:e.status.replace("_"," ").toUpperCase()}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:["Academic: ",e.academicPerformance,"%"]})]})]}),(0,a.jsxs)("div",{className:"mt-4 flex justify-end space-x-2",children:[(0,a.jsx)("button",{onClick:()=>m(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"Review Application"}),"approved"===e.status&&(0,a.jsx)("button",{className:"text-green-600 hover:text-green-800 text-sm font-medium",children:"Generate Award Letter"})]})]})},e.id))})]}),o&&(0,a.jsx)(()=>o?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold",children:["Application Review - ",o.applicationNumber]}),(0,a.jsx)("button",{onClick:()=>m(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Student Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Name:"})," ",o.studentName]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Roll Number:"})," ",o.rollNumber]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Grade:"})," ",o.grade]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Academic Performance:"})," ",o.academicPerformance,"%"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Family Income:"})," $",o.familyIncome.toLocaleString()]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Application Details"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Scholarship:"})," ",o.scholarshipName]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Applied Amount:"})," $",o.appliedAmount.toLocaleString()]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Application Date:"})," ",o.applicationDate]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Status:"}),(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(j(o.status)),children:o.status.replace("_"," ").toUpperCase()})]})]})]})]}),(0,a.jsxs)(l.Zb,{className:"mb-6",children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Document Status"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("div",{className:"space-y-2",children:o.documents.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 border rounded",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(j(e.status)),children:e.status.toUpperCase()})]},s))})})]}),o.reviewNotes&&(0,a.jsxs)(l.Zb,{className:"mb-6",children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Review Notes"})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:o.reviewNotes})})]}),(0,a.jsx)("div",{className:"flex justify-end space-x-3",children:"under_review"===o.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700",children:"Reject"}),(0,a.jsx)("button",{className:"bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700",children:"Waitlist"}),(0,a.jsx)("button",{onClick:()=>null==r?void 0:r(o.id,o.appliedAmount),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Approve"})]})})]})}):null,{})]})}function r(){return(0,a.jsx)(n,{tenantId:"demo-tenant-uuid-1234567890",onCreateScholarship:()=>{console.log("Create new scholarship program")},onReviewApplication:e=>{console.log("Review application:",e)},onApproveApplication:(e,s)=>{console.log("Approve application:",{applicationId:e,amount:s}),alert("Approving application ".concat(e," for $").concat(s))}})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return c},aY:function(){return x},SZ:function(){return m},Ol:function(){return d},ll:function(){return o}});var a=t(7437),i=t(2265),l=t(7042),n=t(4769);function r(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,l.W)(s))}let c=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("div",{ref:s,className:r("rounded-lg border bg-card text-card-foreground shadow-sm",t),...i})});c.displayName="Card";let d=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("div",{ref:s,className:r("flex flex-col space-y-1.5 p-6",t),...i})});d.displayName="CardHeader";let o=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("h3",{ref:s,className:r("text-2xl font-semibold leading-none tracking-tight",t),...i})});o.displayName="CardTitle";let m=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("p",{ref:s,className:r("text-sm text-muted-foreground",t),...i})});m.displayName="CardDescription";let x=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("div",{ref:s,className:r("p-6 pt-0",t),...i})});x.displayName="CardContent",i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("div",{ref:s,className:r("flex items-center p-6 pt-0",t),...i})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=8508)}),_N_E=e.O()}]);