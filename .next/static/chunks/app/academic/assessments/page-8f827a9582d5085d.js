(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7592],{8885:function(e,s,t){Promise.resolve().then(t.bind(t,25))},25:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return r}});var a=t(7437),l=t(2265),n=t(9197);function i(e){let{tenantId:s,onCreateAssessment:t,onEditAssessment:i}=e,[r,d]=(0,l.useState)("all"),[c,o]=(0,l.useState)("all"),[u,m]=(0,l.useState)("all"),[x,h]=(0,l.useState)(null),[p,f]=(0,l.useState)(!1),j=e=>{switch(e){case"draft":default:return"bg-gray-100 text-gray-800";case"published":return"bg-blue-100 text-blue-800";case"completed":return"bg-green-100 text-green-800";case"graded":return"bg-purple-100 text-purple-800"}},y=e=>{switch(e){case"exam":return"bg-red-100 text-red-800";case"quiz":return"bg-yellow-100 text-yellow-800";case"assignment":return"bg-green-100 text-green-800";case"project":return"bg-blue-100 text-blue-800";case"presentation":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},g=e=>{switch(e){case"easy":return"text-green-600";case"medium":return"text-yellow-600";case"hard":return"text-red-600";default:return"text-gray-600"}},b=[{id:"1",title:"Mid-term Mathematics Exam",subject:"Mathematics",grade:"Grade 11",type:"exam",totalMarks:100,duration:180,scheduledDate:"2024-02-15",instructions:"Answer all questions. Show your work clearly. Use of calculator is allowed.",questions:[{id:"1",type:"multiple_choice",question:"What is the derivative of x\xb2?",options:["2x","x","2","x\xb2"],correctAnswer:"2x",marks:5,difficulty:"easy"},{id:"2",type:"essay",question:"Explain the concept of limits in calculus with examples.",marks:15,difficulty:"medium"}],status:"published",createdBy:"Dr. John Smith",createdAt:"2024-01-20"},{id:"2",title:"Physics Lab Report",subject:"Physics",grade:"Grade 11",type:"assignment",totalMarks:50,duration:0,scheduledDate:"2024-02-20",instructions:"Submit a detailed lab report on the pendulum experiment conducted in class.",questions:[{id:"1",type:"essay",question:"Describe the experimental setup and procedure.",marks:15,difficulty:"medium"},{id:"2",type:"essay",question:"Analyze the results and discuss sources of error.",marks:20,difficulty:"hard"}],status:"draft",createdBy:"Dr. Sarah Wilson",createdAt:"2024-01-22"},{id:"3",title:"English Literature Quiz",subject:"English",grade:"Grade 10",type:"quiz",totalMarks:25,duration:30,scheduledDate:"2024-02-10",instructions:"Quick quiz on Shakespeare's Romeo and Juliet.",questions:[{id:"1",type:"multiple_choice",question:"Who wrote Romeo and Juliet?",options:["Charles Dickens","William Shakespeare","Jane Austen","Mark Twain"],correctAnswer:"William Shakespeare",marks:5,difficulty:"easy"},{id:"2",type:"short_answer",question:"Name the two feuding families in the play.",marks:5,difficulty:"easy"}],status:"completed",createdBy:"Prof. Emily Davis",createdAt:"2024-01-18"}].filter(e=>{let s="all"===r||e.type===r,t="all"===c||e.subject===c,a="all"===u||e.status===u;return s&&t&&a});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Assessment Creation"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Create and manage tests, quizzes, and assignments"})]}),(0,a.jsx)("button",{onClick:t,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Create Assessment"})]}),(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Type"}),(0,a.jsxs)("select",{value:r,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Types"}),(0,a.jsx)("option",{value:"exam",children:"Exam"}),(0,a.jsx)("option",{value:"quiz",children:"Quiz"}),(0,a.jsx)("option",{value:"assignment",children:"Assignment"}),(0,a.jsx)("option",{value:"project",children:"Project"}),(0,a.jsx)("option",{value:"presentation",children:"Presentation"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject"}),(0,a.jsxs)("select",{value:c,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Subjects"}),(0,a.jsx)("option",{value:"Mathematics",children:"Mathematics"}),(0,a.jsx)("option",{value:"Physics",children:"Physics"}),(0,a.jsx)("option",{value:"Chemistry",children:"Chemistry"}),(0,a.jsx)("option",{value:"English",children:"English"}),(0,a.jsx)("option",{value:"History",children:"History"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsxs)("select",{value:u,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"draft",children:"Draft"}),(0,a.jsx)("option",{value:"published",children:"Published"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"graded",children:"Graded"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Assessments"})})]})})}),(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:b.map(e=>(0,a.jsxs)(n.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(n.ll,{className:"text-lg",children:e.title}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(j(e.status)),children:e.status.toUpperCase()})]}),(0,a.jsxs)(n.SZ,{children:[e.subject," • ",e.grade]})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Type:"}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(y(e.type)),children:e.type.toUpperCase()})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Marks:"}),(0,a.jsx)("span",{className:"font-medium",children:e.totalMarks})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Duration:"}),(0,a.jsxs)("span",{className:"font-medium",children:[e.duration," min"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Scheduled:"}),(0,a.jsx)("span",{className:"font-medium",children:e.scheduledDate})]})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[e.questions.length," questions • By ",e.createdBy]}),(0,a.jsx)("button",{onClick:()=>h(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"})]})]})]},e.id))}),x&&(0,a.jsx)(()=>x?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:x.title}),(0,a.jsx)("button",{onClick:()=>h(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Assessment Details"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Subject:"})," ",x.subject]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Grade:"})," ",x.grade]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Type:"}),(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(y(x.type)),children:x.type.toUpperCase()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Total Marks:"})," ",x.totalMarks]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Duration:"})," ",x.duration," minutes"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Scheduled Date:"})," ",x.scheduledDate]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Status:"}),(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(j(x.status)),children:x.status.toUpperCase()})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Question Statistics"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Total Questions:"})," ",x.questions.length]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Easy:"})," ",x.questions.filter(e=>"easy"===e.difficulty).length]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Medium:"})," ",x.questions.filter(e=>"medium"===e.difficulty).length]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Hard:"})," ",x.questions.filter(e=>"hard"===e.difficulty).length]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Created by:"})," ",x.createdBy]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Created on:"})," ",x.createdAt]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Instructions"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded",children:x.instructions})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Questions"}),(0,a.jsx)("div",{className:"space-y-4",children:x.questions.map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("h5",{className:"font-medium",children:["Question ",s+1]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium ".concat(g(e.difficulty)),children:e.difficulty.toUpperCase()}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.marks," marks"]})]})]}),(0,a.jsx)("p",{className:"text-sm mb-3",children:e.question}),e.options&&(0,a.jsx)("div",{className:"space-y-1",children:e.options.map((s,t)=>(0,a.jsxs)("div",{className:"text-sm p-2 rounded ".concat(s===e.correctAnswer?"bg-green-100 text-green-800":"bg-gray-50"),children:[String.fromCharCode(65+t),". ",s,s===e.correctAnswer&&" ✓"]},t))})]},e.id))})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>null==i?void 0:i(x.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Edit Assessment"}),"draft"===x.status&&(0,a.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Publish Assessment"})]})]})}):null,{})]})}function r(){return(0,a.jsx)(i,{tenantId:"demo-tenant-uuid-1234567890",onCreateAssessment:()=>{console.log("Create new assessment")},onEditAssessment:e=>{console.log("Edit assessment:",e)}})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return d},aY:function(){return m},SZ:function(){return u},Ol:function(){return c},ll:function(){return o}});var a=t(7437),l=t(2265),n=t(7042),i=t(4769);function r(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,i.m6)((0,n.W)(s))}let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:r("rounded-lg border bg-card text-card-foreground shadow-sm",t),...l})});d.displayName="Card";let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:r("flex flex-col space-y-1.5 p-6",t),...l})});c.displayName="CardHeader";let o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("h3",{ref:s,className:r("text-2xl font-semibold leading-none tracking-tight",t),...l})});o.displayName="CardTitle";let u=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("p",{ref:s,className:r("text-sm text-muted-foreground",t),...l})});u.displayName="CardDescription";let m=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:r("p-6 pt-0",t),...l})});m.displayName="CardContent",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:r("flex items-center p-6 pt-0",t),...l})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=8885)}),_N_E=e.O()}]);