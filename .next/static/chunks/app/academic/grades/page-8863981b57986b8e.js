(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[112],{982:function(e,s,t){Promise.resolve().then(t.bind(t,422))},422:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return d}});var a=t(7437),r=t(2265),l=t(9197);function n(e){let{tenantId:s,onSaveGrades:t,onPublishGrades:n}=e,[d,i]=(0,r.useState)("Grade 11 - A"),[c,o]=(0,r.useState)(""),[x,m]=(0,r.useState)({}),[u,h]=(0,r.useState)("entry"),g=[{id:"1",title:"Mid-term Mathematics Exam",subject:"Mathematics",totalMarks:100,type:"exam",scheduledDate:"2024-02-15"},{id:"2",title:"Physics Quiz - Chapter 5",subject:"Physics",totalMarks:25,type:"quiz",scheduledDate:"2024-02-10"},{id:"3",title:"Chemistry Lab Report",subject:"Chemistry",totalMarks:50,type:"assignment",scheduledDate:"2024-02-12"}],j=e=>e>=90?"A+":e>=80?"A":e>=70?"B+":e>=60?"B":e>=50?"C+":e>=40?"C":e>=33?"D":"F",p=e=>{switch(e){case"A+":case"A":return"text-green-600";case"B+":case"B":return"text-blue-600";case"C+":case"C":return"text-yellow-600";case"D":return"text-orange-600";case"F":return"text-red-600";default:return"text-gray-600"}},f=(e,s)=>{let t=g.find(e=>e.id===c);if(!t)return;let a=s/t.totalMarks*100,r=j(a);m(l=>({...l,[e]:{studentId:e,assessmentId:c,marksObtained:s,totalMarks:t.totalMarks,percentage:a,grade:r,gradedBy:"Current Teacher",gradedAt:new Date().toISOString()}}))},b=(e,s)=>{m(t=>({...t,[e]:{...t[e],remarks:s}}))},y=g.find(e=>e.id===c),N=(()=>{let e=Object.values(x);if(0===e.length)return null;let s=e.length,t=e.reduce((e,s)=>e+s.marksObtained,0),a=e.reduce((e,s)=>e+s.percentage,0)/s,r=Math.max(...e.map(e=>e.marksObtained)),l=Math.min(...e.map(e=>e.marksObtained)),n=e.reduce((e,s)=>(e[s.grade]=(e[s.grade]||0)+1,e),{});return{totalStudents:s,averageMarks:(t/s).toFixed(1),averagePercentage:a.toFixed(1),highestMarks:r,lowestMarks:l,gradeDistribution:n}})();return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Grade Entry"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Record and manage student grades"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>h("entry"===u?"review":"entry"),className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"entry"===u?"Review Mode":"Entry Mode"}),c&&(0,a.jsx)("button",{onClick:()=>null==n?void 0:n(c),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Publish Grades"})]})]}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Class"}),(0,a.jsxs)("select",{value:d,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"Grade 11 - A",children:"Grade 11 - Division A"}),(0,a.jsx)("option",{value:"Grade 11 - B",children:"Grade 11 - Division B"}),(0,a.jsx)("option",{value:"Grade 10 - A",children:"Grade 10 - Division A"}),(0,a.jsx)("option",{value:"Grade 10 - B",children:"Grade 10 - Division B"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Assessment"}),(0,a.jsxs)("select",{value:c,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select Assessment"}),g.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.title," (",e.totalMarks," marks)"]},e.id))]})]})]})})}),y&&(0,a.jsx)(l.Zb,{children:(0,a.jsxs)(l.Ol,{children:[(0,a.jsx)(l.ll,{children:y.title}),(0,a.jsxs)(l.SZ,{children:[y.subject," • ",y.type.toUpperCase()," • Total Marks: ",y.totalMarks," • Date: ",y.scheduledDate]})]})}),N&&(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Class Statistics"})}),(0,a.jsxs)(l.aY,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:N.totalStudents}),(0,a.jsx)("div",{className:"text-gray-600",children:"Students"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:N.averageMarks}),(0,a.jsx)("div",{className:"text-gray-600",children:"Average Marks"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[N.averagePercentage,"%"]}),(0,a.jsx)("div",{className:"text-gray-600",children:"Average %"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:N.highestMarks}),(0,a.jsx)("div",{className:"text-gray-600",children:"Highest"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:N.lowestMarks}),(0,a.jsx)("div",{className:"text-gray-600",children:"Lowest"})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Grade Distribution"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:Object.entries(N.gradeDistribution).map(e=>{let[s,t]=e;return(0,a.jsxs)("span",{className:"px-2 py-1 text-sm rounded ".concat(p(s)," bg-gray-100"),children:[s,": ",t]},s)})})]})]})]}),y&&(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[(0,a.jsxs)(l.ll,{children:["Grade Entry - ",d]}),(0,a.jsx)(l.SZ,{children:"Enter marks for each student"})]}),(0,a.jsxs)(l.aY,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full table-auto",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Roll No."}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Student Name"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Marks Obtained"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Percentage"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Grade"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Remarks"})]})}),(0,a.jsx)("tbody",{children:[{id:"1",name:"John Smith",rollNumber:"11A001",email:"<EMAIL>"},{id:"2",name:"Sarah Johnson",rollNumber:"11A002",email:"<EMAIL>"},{id:"3",name:"Michael Brown",rollNumber:"11A003",email:"<EMAIL>"},{id:"4",name:"Emily Davis",rollNumber:"11A004",email:"<EMAIL>"},{id:"5",name:"David Wilson",rollNumber:"11A005",email:"<EMAIL>"}].map(e=>{let s=x[e.id];return(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"py-3 px-4 font-medium",children:e.rollNumber}),(0,a.jsx)("td",{className:"py-3 px-4",children:e.name}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"number",min:"0",max:y.totalMarks,value:(null==s?void 0:s.marksObtained)||"",onChange:s=>f(e.id,Number(s.target.value)),className:"w-20 px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0"}),(0,a.jsxs)("span",{className:"text-gray-500",children:["/ ",y.totalMarks]})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:s?(0,a.jsxs)("span",{className:"font-medium",children:[s.percentage.toFixed(1),"%"]}):(0,a.jsx)("span",{className:"text-gray-400",children:"-"})}),(0,a.jsx)("td",{className:"py-3 px-4",children:s?(0,a.jsx)("span",{className:"font-bold ".concat(p(s.grade)),children:s.grade}):(0,a.jsx)("span",{className:"text-gray-400",children:"-"})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("input",{type:"text",value:(null==s?void 0:s.remarks)||"",onChange:s=>b(e.id,s.target.value),className:"w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Optional remarks"})})]},e.id)})})]})}),(0,a.jsx)("div",{className:"mt-4 flex justify-end space-x-3",children:(0,a.jsx)("button",{onClick:()=>null==t?void 0:t(Object.values(x)),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",disabled:0===Object.keys(x).length,children:"Save Grades"})})]})]})]})}function d(){return(0,a.jsx)(n,{tenantId:"demo-tenant-uuid-1234567890",onSaveGrades:e=>{console.log("Save grades:",e)},onPublishGrades:e=>{console.log("Publish grades for assessment:",e)}})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return i},aY:function(){return m},SZ:function(){return x},Ol:function(){return c},ll:function(){return o}});var a=t(7437),r=t(2265),l=t(7042),n=t(4769);function d(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,l.W)(s))}let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});i.displayName="Card";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:d("flex flex-col space-y-1.5 p-6",t),...r})});c.displayName="CardHeader";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:d("text-2xl font-semibold leading-none tracking-tight",t),...r})});o.displayName="CardTitle";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:d("text-sm text-muted-foreground",t),...r})});x.displayName="CardDescription";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:d("p-6 pt-0",t),...r})});m.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:d("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=982)}),_N_E=e.O()}]);