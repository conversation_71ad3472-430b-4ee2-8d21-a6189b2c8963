(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7981],{2442:function(e,t,a){Promise.resolve().then(a.bind(a,3779))},3779:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return d}});var r=a(7437),s=a(2265),l=a(9197);function n(e){let{tenantId:t,onSaveAttendance:a,onGenerateReport:n}=e,[d,i]=(0,s.useState)("Grade 11 - A"),[c,o]=(0,s.useState)(new Date().toISOString().split("T")[0]),[x,m]=(0,s.useState)("all"),[u,h]=(0,s.useState)("daily"),[b,g]=(0,s.useState)({}),p=[{studentId:"1",date:"2024-01-20",status:"present",subject:"math",period:"1",markedBy:"Teacher",markedAt:"2024-01-20T08:00:00Z"},{studentId:"1",date:"2024-01-21",status:"absent",subject:"math",period:"1",markedBy:"Teacher",markedAt:"2024-01-21T08:00:00Z"},{studentId:"2",date:"2024-01-20",status:"present",subject:"math",period:"1",markedBy:"Teacher",markedAt:"2024-01-20T08:00:00Z"},{studentId:"2",date:"2024-01-21",status:"late",subject:"math",period:"1",markedBy:"Teacher",markedAt:"2024-01-21T08:00:00Z"}],j=e=>{switch(e){case"present":return"bg-green-100 text-green-800 border-green-200";case"absent":return"bg-red-100 text-red-800 border-red-200";case"late":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"excused":return"bg-blue-100 text-blue-800 border-blue-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},y=(e,t)=>{g(a=>({...a,[e]:{studentId:e,date:c,status:t,subject:"all"===x?void 0:x,markedBy:"Current Teacher",markedAt:new Date().toISOString()}}))},f=(e,t)=>{g(a=>({...a,[e]:{...a[e],remarks:t}}))},v=e=>p.filter(t=>t.studentId===e),N=e=>{let t=v(e);return 0===t.length?0:(t.filter(e=>"present"===e.status).length/t.length*100).toFixed(1)},w=(()=>{let e=Object.values(b);if(0===e.length)return null;let t=e.filter(e=>"present"===e.status).length,a=e.filter(e=>"absent"===e.status).length,r=e.filter(e=>"late"===e.status).length,s=e.filter(e=>"excused"===e.status).length,l=e.length;return{present:t,absent:a,late:r,excused:s,total:l,presentPercentage:l>0?(t/l*100).toFixed(1):"0"}})();return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Attendance Tracking"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Track and manage student attendance"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>null==n?void 0:n(d,{start:c,end:c}),className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Generate Report"}),(0,r.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Export Data"})]})]}),(0,r.jsx)(l.Zb,{children:(0,r.jsx)(l.aY,{className:"pt-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Class"}),(0,r.jsxs)("select",{value:d,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"Grade 11 - A",children:"Grade 11 - Division A"}),(0,r.jsx)("option",{value:"Grade 11 - B",children:"Grade 11 - Division B"}),(0,r.jsx)("option",{value:"Grade 10 - A",children:"Grade 10 - Division A"}),(0,r.jsx)("option",{value:"Grade 10 - B",children:"Grade 10 - Division B"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date"}),(0,r.jsx)("input",{type:"date",value:c,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject"}),(0,r.jsx)("select",{value:x,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{id:"all",name:"All Subjects"},{id:"math",name:"Mathematics"},{id:"physics",name:"Physics"},{id:"chemistry",name:"Chemistry"},{id:"english",name:"English"},{id:"history",name:"History"}].map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"View"}),(0,r.jsx)("div",{className:"flex border border-gray-300 rounded-md",children:["daily","weekly","monthly"].map(e=>(0,r.jsx)("button",{onClick:()=>h(e),className:"flex-1 px-3 py-2 text-sm capitalize ".concat(u===e?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:e},e))})]})]})})}),w&&(0,r.jsxs)(l.Zb,{children:[(0,r.jsx)(l.Ol,{children:(0,r.jsx)(l.ll,{children:"Today's Attendance Summary"})}),(0,r.jsx)(l.aY,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:w.present}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Present"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:w.absent}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Absent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:w.late}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Late"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:w.excused}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Excused"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[w.presentPercentage,"%"]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Attendance Rate"})]})]})})]}),"daily"===u&&(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsxs)(l.ll,{children:["Daily Attendance - ",c]}),(0,r.jsxs)(l.SZ,{children:["Mark attendance for ",d]})]}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full table-auto",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Roll No."}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Student Name"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Attendance"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Overall %"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Remarks"})]})}),(0,r.jsx)("tbody",{children:[{id:"1",name:"John Smith",rollNumber:"11A001",email:"<EMAIL>"},{id:"2",name:"Sarah Johnson",rollNumber:"11A002",email:"<EMAIL>"},{id:"3",name:"Michael Brown",rollNumber:"11A003",email:"<EMAIL>"},{id:"4",name:"Emily Davis",rollNumber:"11A004",email:"<EMAIL>"},{id:"5",name:"David Wilson",rollNumber:"11A005",email:"<EMAIL>"},{id:"6",name:"Lisa Garcia",rollNumber:"11A006",email:"<EMAIL>"}].map(e=>{let t=b[e.id],a=N(e.id);return(0,r.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"py-3 px-4 font-medium",children:e.rollNumber}),(0,r.jsx)("td",{className:"py-3 px-4",children:e.name}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsx)("div",{className:"flex space-x-2",children:["present","absent","late","excused"].map(a=>(0,r.jsx)("button",{onClick:()=>y(e.id,a),className:"px-3 py-1 text-xs rounded border transition-colors ".concat((null==t?void 0:t.status)===a?j(a):"bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100"),children:a.charAt(0).toUpperCase()+a.slice(1)},a))})}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsxs)("span",{className:"font-medium ".concat(Number(a)>=75?"text-green-600":Number(a)>=60?"text-yellow-600":"text-red-600"),children:[a,"%"]})}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsx)("input",{type:"text",value:(null==t?void 0:t.remarks)||"",onChange:t=>f(e.id,t.target.value),className:"w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Optional remarks"})})]},e.id)})})]})}),(0,r.jsx)("div",{className:"mt-4 flex justify-end space-x-3",children:(0,r.jsx)("button",{onClick:()=>null==a?void 0:a(Object.values(b)),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",disabled:0===Object.keys(b).length,children:"Save Attendance"})})]})]}),"weekly"===u&&(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsx)(l.ll,{children:"Weekly Attendance Summary"}),(0,r.jsx)(l.SZ,{children:"Attendance overview for the current week"})]}),(0,r.jsx)(l.aY,{children:(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Weekly view implementation - Shows attendance patterns for the week"})})]}),"monthly"===u&&(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsx)(l.ll,{children:"Monthly Attendance Report"}),(0,r.jsx)(l.SZ,{children:"Comprehensive attendance analysis for the month"})]}),(0,r.jsx)(l.aY,{children:(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Monthly view implementation - Shows detailed attendance analytics"})})]})]})}function d(){return(0,r.jsx)(n,{tenantId:"demo-tenant-uuid-1234567890",onSaveAttendance:e=>{console.log("Save attendance:",e)},onGenerateReport:(e,t)=>{console.log("Generate attendance report:",{classId:e,dateRange:t})}})}},9197:function(e,t,a){"use strict";a.d(t,{Zb:function(){return i},aY:function(){return m},SZ:function(){return x},Ol:function(){return c},ll:function(){return o}});var r=a(7437),s=a(2265),l=a(7042),n=a(4769);function d(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.m6)((0,l.W)(t))}let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",a),...s})});i.displayName="Card";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:d("flex flex-col space-y-1.5 p-6",a),...s})});c.displayName="CardHeader";let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h3",{ref:t,className:d("text-2xl font-semibold leading-none tracking-tight",a),...s})});o.displayName="CardTitle";let x=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("p",{ref:t,className:d("text-sm text-muted-foreground",a),...s})});x.displayName="CardDescription";let m=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:d("p-6 pt-0",a),...s})});m.displayName="CardContent",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:d("flex items-center p-6 pt-0",a),...s})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=2442)}),_N_E=e.O()}]);