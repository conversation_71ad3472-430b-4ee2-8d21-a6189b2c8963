(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3598],{8558:function(e,s,r){Promise.resolve().then(r.bind(r,9845))},9845:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return i}});var t=r(7437),a=r(2265),d=r(9197);function l(e){let{tenantId:s,onCreateSchedule:r,onEditSchedule:l}=e,[i,n]=(0,a.useState)("Grade 9"),[c,o]=(0,a.useState)("A"),[x,m]=(0,a.useState)("current"),[u,h]=(0,a.useState)("week"),b=[{id:"1",startTime:"08:00",endTime:"08:45",duration:45},{id:"2",startTime:"08:45",endTime:"09:30",duration:45},{id:"3",startTime:"09:45",endTime:"10:30",duration:45},{id:"4",startTime:"10:30",endTime:"11:15",duration:45},{id:"5",startTime:"11:30",endTime:"12:15",duration:45},{id:"6",startTime:"12:15",endTime:"13:00",duration:45},{id:"7",startTime:"14:00",endTime:"14:45",duration:45},{id:"8",startTime:"14:45",endTime:"15:30",duration:45}],j=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],g=[{id:"1",subject:"Mathematics",teacher:"Dr. John Smith",classroom:"Room 101",grade:"Grade 9",division:"A",timeSlot:"1",day:"Monday",type:"lecture"},{id:"2",subject:"Physics",teacher:"Dr. Sarah Wilson",classroom:"Lab 201",grade:"Grade 9",division:"A",timeSlot:"2",day:"Monday",type:"lab"},{id:"3",subject:"English",teacher:"Prof. Emily Davis",classroom:"Room 102",grade:"Grade 9",division:"A",timeSlot:"4",day:"Monday",type:"lecture"},{id:"4",subject:"Chemistry",teacher:"Dr. Michael Brown",classroom:"Lab 202",grade:"Grade 9",division:"A",timeSlot:"1",day:"Tuesday",type:"lab"},{id:"5",subject:"History",teacher:"Prof. Lisa Garcia",classroom:"Room 103",grade:"Grade 9",division:"A",timeSlot:"2",day:"Tuesday",type:"lecture"},{id:"6",subject:"Mathematics",teacher:"Dr. John Smith",classroom:"Room 101",grade:"Grade 9",division:"A",timeSlot:"3",day:"Wednesday",type:"tutorial"}],y=e=>{switch(e){case"lecture":return"bg-blue-100 text-blue-800 border-blue-200";case"lab":return"bg-green-100 text-green-800 border-green-200";case"tutorial":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"exam":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},f=(e,s)=>g.find(r=>r.day===e&&r.timeSlot===s&&r.grade===i&&r.division===c);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Class Scheduling"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Create and manage class timetables"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("button",{className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Schedule"}),(0,t.jsx)("button",{onClick:r,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Add Class"})]})]}),(0,t.jsx)(d.Zb,{children:(0,t.jsx)(d.aY,{className:"pt-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Grade"}),(0,t.jsxs)("select",{value:i,onChange:e=>n(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"Grade 9",children:"Grade 9"}),(0,t.jsx)("option",{value:"Grade 10",children:"Grade 10"}),(0,t.jsx)("option",{value:"Grade 11",children:"Grade 11"}),(0,t.jsx)("option",{value:"Grade 12",children:"Grade 12"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Division"}),(0,t.jsxs)("select",{value:c,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"A",children:"Division A"}),(0,t.jsx)("option",{value:"B",children:"Division B"}),(0,t.jsx)("option",{value:"C",children:"Division C"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Week"}),(0,t.jsxs)("select",{value:x,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"current",children:"Current Week"}),(0,t.jsx)("option",{value:"next",children:"Next Week"}),(0,t.jsx)("option",{value:"custom",children:"Custom Range"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"View"}),(0,t.jsxs)("div",{className:"flex border border-gray-300 rounded-md",children:[(0,t.jsx)("button",{onClick:()=>h("week"),className:"flex-1 px-3 py-2 text-sm ".concat("week"===u?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:"Week"}),(0,t.jsx)("button",{onClick:()=>h("day"),className:"flex-1 px-3 py-2 text-sm ".concat("day"===u?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:"Day"})]})]}),(0,t.jsx)("div",{className:"flex items-end",children:(0,t.jsx)("button",{className:"w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Publish Schedule"})})]})})}),(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsx)(d.ll,{className:"text-lg",children:"Class Types"})}),(0,t.jsx)(d.aY,{children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-blue-100 border border-blue-200 rounded"}),(0,t.jsx)("span",{className:"text-sm",children:"Lecture"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-green-100 border border-green-200 rounded"}),(0,t.jsx)("span",{className:"text-sm",children:"Laboratory"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-yellow-100 border border-yellow-200 rounded"}),(0,t.jsx)("span",{className:"text-sm",children:"Tutorial"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-red-100 border border-red-200 rounded"}),(0,t.jsx)("span",{className:"text-sm",children:"Examination"})]})]})})]}),(0,t.jsxs)(d.Zb,{children:[(0,t.jsxs)(d.Ol,{children:[(0,t.jsxs)(d.ll,{children:[i," - Division ",c," Schedule"]}),(0,t.jsxs)(d.SZ,{children:["week"===u?"Weekly":"Daily"," class schedule"]})]}),(0,t.jsx)(d.aY,{children:"week"===u?(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full border-collapse border border-gray-300",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"bg-gray-50",children:[(0,t.jsx)("th",{className:"border border-gray-300 p-3 text-left font-medium",children:"Time"}),j.map(e=>(0,t.jsx)("th",{className:"border border-gray-300 p-3 text-center font-medium min-w-[150px]",children:e},e))]})}),(0,t.jsx)("tbody",{children:b.map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsxs)("td",{className:"border border-gray-300 p-3 bg-gray-50 font-medium",children:[(0,t.jsxs)("div",{className:"text-sm",children:[e.startTime," - ",e.endTime]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[e.duration," min"]})]}),j.map(s=>{let a=f(s,e.id);return(0,t.jsx)("td",{className:"border border-gray-300 p-2 h-20 align-top",children:a?(0,t.jsxs)("div",{className:"p-2 rounded border ".concat(y(a.type)," h-full cursor-pointer hover:shadow-sm"),children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:a.subject}),(0,t.jsx)("div",{className:"text-xs",children:a.teacher}),(0,t.jsx)("div",{className:"text-xs",children:a.classroom}),(0,t.jsx)("div",{className:"text-xs capitalize",children:a.type})]}):(0,t.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,t.jsx)("button",{onClick:()=>null==r?void 0:r(),className:"text-gray-400 hover:text-gray-600 text-xs",children:"+ Add Class"})})},"".concat(s,"-").concat(e.id))})]},e.id))})]})}):(()=>{let e="Monday",s=g.filter(s=>s.day===e&&s.grade===i&&s.division===c);return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold",children:[e," Schedule"]}),(0,t.jsx)("div",{className:"space-y-3",children:b.map(e=>{let a=s.find(s=>s.timeSlot===e.id);return(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"w-24 text-sm font-medium",children:[e.startTime," - ",e.endTime]}),a?(0,t.jsx)("div",{className:"flex-1 p-3 rounded border ".concat(y(a.type)),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:a.subject}),(0,t.jsxs)("div",{className:"text-sm",children:[a.teacher," • ",a.classroom]}),(0,t.jsx)("div",{className:"text-xs capitalize",children:a.type})]}),(0,t.jsx)("button",{onClick:()=>null==l?void 0:l(a.id),className:"text-blue-600 hover:text-blue-800 text-sm",children:"Edit"})]})}):(0,t.jsx)("div",{className:"flex-1 p-3 border-2 border-dashed border-gray-300 rounded text-center",children:(0,t.jsx)("button",{onClick:()=>null==r?void 0:r(),className:"text-gray-500 hover:text-gray-700",children:"+ Add Class"})})]},e.id)})})]})})()})]})]})}function i(){return(0,t.jsx)(l,{tenantId:"demo-tenant-uuid-1234567890",onCreateSchedule:()=>{console.log("Create new schedule entry")},onEditSchedule:e=>{console.log("Edit schedule:",e)}})}},9197:function(e,s,r){"use strict";r.d(s,{Zb:function(){return n},aY:function(){return m},SZ:function(){return x},Ol:function(){return c},ll:function(){return o}});var t=r(7437),a=r(2265),d=r(7042),l=r(4769);function i(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,l.m6)((0,d.W)(s))}let n=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});n.displayName="Card";let c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",r),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let x=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",r),...a})});x.displayName="CardDescription";let m=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:i("p-6 pt-0",r),...a})});m.displayName="CardContent",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=8558)}),_N_E=e.O()}]);