(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[36],{5573:function(e,s,t){Promise.resolve().then(t.bind(t,1382))},1382:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return i}});var a=t(7437),n=t(2265),l=t(9197);function r(e){let{tenantId:s,onCreateSubject:t,onEditSubject:r}=e,[i,c]=(0,n.useState)("all"),[d,o]=(0,n.useState)("all"),[m,u]=(0,n.useState)(""),[x,h]=(0,n.useState)(null),[p,j]=(0,n.useState)(!1),f=[{id:"1",name:"Advanced Mathematics",code:"MATH-11-ADV",grade:"Grade 11",stream:"Science",credits:6,description:"Advanced mathematical concepts including calculus, algebra, and trigonometry",syllabus:[{unit:"Unit 1: Calculus",topics:["Limits","Derivatives","Integration","Applications"],duration:"8 weeks"},{unit:"Unit 2: Algebra",topics:["Complex Numbers","Matrices","Determinants"],duration:"6 weeks"},{unit:"Unit 3: Trigonometry",topics:["Trigonometric Functions","Identities","Equations"],duration:"4 weeks"}],learningObjectives:["Understand fundamental calculus concepts","Apply mathematical reasoning to solve problems","Develop analytical thinking skills"],assessmentMethods:["Written Exams","Problem Sets","Projects","Class Participation"],textbooks:[{title:"Advanced Mathematics for Grade 11",author:"Dr. John Smith",publisher:"Academic Press"}],status:"active"},{id:"2",name:"Physics",code:"PHYS-11-STD",grade:"Grade 11",stream:"Science",credits:6,description:"Fundamental physics concepts including mechanics, thermodynamics, and waves",syllabus:[{unit:"Unit 1: Mechanics",topics:["Motion","Forces","Energy","Momentum"],duration:"10 weeks"},{unit:"Unit 2: Thermodynamics",topics:["Heat","Temperature","Laws of Thermodynamics"],duration:"6 weeks"}],learningObjectives:["Understand basic physics principles","Apply physics concepts to real-world problems","Develop experimental skills"],assessmentMethods:["Written Exams","Lab Reports","Practical Exams"],textbooks:[{title:"Physics Fundamentals",author:"Dr. Sarah Wilson",publisher:"Science Publications"}],status:"active"},{id:"3",name:"English Literature",code:"ENG-10-LIT",grade:"Grade 10",stream:"Arts",credits:4,description:"Study of classic and contemporary literature with focus on analysis and interpretation",syllabus:[{unit:"Unit 1: Poetry",topics:["Romantic Poetry","Modern Poetry","Analysis Techniques"],duration:"6 weeks"},{unit:"Unit 2: Drama",topics:["Shakespearean Drama","Modern Drama","Character Analysis"],duration:"8 weeks"}],learningObjectives:["Analyze literary texts critically","Understand historical and cultural contexts","Develop writing and communication skills"],assessmentMethods:["Essays","Presentations","Group Discussions","Creative Writing"],textbooks:[{title:"English Literature Anthology",author:"Prof. Emily Davis",publisher:"Literary Press"}],status:"active"}].filter(e=>{let s="all"===i||e.grade===i,t="all"===d||e.stream===d,a=e.name.toLowerCase().includes(m.toLowerCase())||e.code.toLowerCase().includes(m.toLowerCase());return s&&t&&a}),b=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"draft":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Curriculum Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage subjects, syllabus, and learning objectives"})]}),(0,a.jsx)("button",{onClick:t,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"+ Add Subject"})]}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search subjects...",value:m,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Grade"}),(0,a.jsxs)("select",{value:i,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Grades"}),(0,a.jsx)("option",{value:"Grade 9",children:"Grade 9"}),(0,a.jsx)("option",{value:"Grade 10",children:"Grade 10"}),(0,a.jsx)("option",{value:"Grade 11",children:"Grade 11"}),(0,a.jsx)("option",{value:"Grade 12",children:"Grade 12"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Stream"}),(0,a.jsxs)("select",{value:d,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Streams"}),(0,a.jsx)("option",{value:"Science",children:"Science"}),(0,a.jsx)("option",{value:"Arts",children:"Arts"}),(0,a.jsx)("option",{value:"Commerce",children:"Commerce"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)("button",{className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200",children:"Export Curriculum"})})]})})}),(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:f.map(e=>(0,a.jsxs)(l.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsxs)(l.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(l.ll,{className:"text-lg",children:e.name}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(b(e.status)),children:e.status.toUpperCase()})]}),(0,a.jsx)(l.SZ,{children:e.code})]}),(0,a.jsxs)(l.aY,{children:[(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Grade:"}),(0,a.jsx)("span",{className:"font-medium",children:e.grade})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Stream:"}),(0,a.jsx)("span",{className:"font-medium",children:e.stream})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Credits:"}),(0,a.jsx)("span",{className:"font-medium",children:e.credits})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-3 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[e.syllabus.length," units • ",e.assessmentMethods.length," assessment methods"]}),(0,a.jsx)("button",{onClick:()=>h(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View Details"})]})]})]},e.id))}),x&&(0,a.jsx)(()=>x?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:x.name}),(0,a.jsx)("button",{onClick:()=>h(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Basic Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Code:"})," ",x.code]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Grade:"})," ",x.grade]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Stream:"})," ",x.stream]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Credits:"})," ",x.credits]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Status:"}),(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(b(x.status)),children:x.status.toUpperCase()})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("span",{className:"font-medium",children:"Description:"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:x.description})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Learning Objectives"}),(0,a.jsx)("ul",{className:"space-y-1 text-sm",children:x.learningObjectives.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-blue-500 mr-2",children:"•"}),(0,a.jsx)("span",{children:e})]},s))})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Syllabus"}),(0,a.jsx)("div",{className:"space-y-4",children:x.syllabus.map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h5",{className:"font-medium",children:e.unit}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:e.duration})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.topics.map((e,s)=>(0,a.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:e},s))})]},s))})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Assessment Methods"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:x.assessmentMethods.map((e,s)=>(0,a.jsx)("span",{className:"px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full",children:e},s))})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Textbooks"}),(0,a.jsx)("div",{className:"space-y-2",children:x.textbooks.map((e,s)=>(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"font-medium",children:e.title})," by ",e.author,(0,a.jsxs)("span",{className:"text-gray-500",children:[" (",e.publisher,")"]})]},s))})]}),(0,a.jsx)("div",{className:"flex justify-end space-x-3 mt-6",children:(0,a.jsx)("button",{onClick:()=>null==r?void 0:r(x.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Edit Subject"})})]})}):null,{})]})}function i(){return(0,a.jsx)(r,{tenantId:"demo-tenant-uuid-1234567890",onCreateSubject:()=>{console.log("Create new subject")},onEditSubject:e=>{console.log("Edit subject:",e)}})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return c},aY:function(){return u},SZ:function(){return m},Ol:function(){return d},ll:function(){return o}});var a=t(7437),n=t(2265),l=t(7042),r=t(4769);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,l.W)(s))}let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});c.displayName="Card";let d=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",t),...n})});d.displayName="CardHeader";let o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",t),...n})});o.displayName="CardTitle";let m=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",t),...n})});m.displayName="CardDescription";let u=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:i("p-6 pt-0",t),...n})});u.displayName="CardContent",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=5573)}),_N_E=e.O()}]);