(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[74],{86:function(e,s,t){Promise.resolve().then(t.bind(t,2414))},2414:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return i}});var r=t(7437),a=t(2265),n=t(9197);function l(e){let{tenantId:s,onGenerateReport:t,onScheduleParentMeeting:l}=e,[i,d]=(0,a.useState)("Grade 11 - A"),[c,o]=(0,a.useState)(""),[m,x]=(0,a.useState)("comprehensive"),[u,h]=(0,a.useState)(null),p={student:{id:"1",name:"<PERSON>",rollNumber:"11A001",grade:"Grade 11",division:"A"},overallGPA:3.7,overallAttendance:92,subjects:[{subject:"Mathematics",currentGrade:"A-",percentage:87,attendance:95,assignments:{completed:8,total:10},assessments:{average:85,highest:95,lowest:78},trend:"improving"},{subject:"Physics",currentGrade:"B+",percentage:82,attendance:90,assignments:{completed:7,total:8},assessments:{average:80,highest:88,lowest:72},trend:"stable"},{subject:"Chemistry",currentGrade:"B",percentage:78,attendance:88,assignments:{completed:6,total:9},assessments:{average:76,highest:85,lowest:65},trend:"declining"},{subject:"English",currentGrade:"A",percentage:90,attendance:96,assignments:{completed:9,total:10},assessments:{average:88,highest:95,lowest:82},trend:"improving"}],behavioralNotes:["Shows excellent leadership qualities in group projects","Actively participates in class discussions","Helps fellow students with difficult concepts"],achievements:["Winner of Mathematics Olympiad (School Level)","Best Student of the Month - January 2024","Science Fair Project - Second Place"],areasForImprovement:["Needs to improve attendance in Chemistry lab sessions","Should focus more on completing assignments on time","Could benefit from additional practice in organic chemistry"],parentMeetings:[{date:"2024-01-15",notes:"Discussed academic progress and career guidance. Parents are supportive of student's interest in engineering."},{date:"2023-12-10",notes:"Addressed concerns about chemistry performance. Recommended additional tutoring."}]},g=e=>{switch(e){case"improving":return"\uD83D\uDCC8";case"declining":return"\uD83D\uDCC9";default:return"➡️"}},j=e=>{switch(e){case"improving":return"text-green-600";case"declining":return"text-red-600";case"stable":return"text-blue-600";default:return"text-gray-600"}},f=e=>e>=90?"text-green-600":e>=80?"text-blue-600":e>=70?"text-yellow-600":e>=60?"text-orange-600":"text-red-600",b=e=>{h(p)};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Student Progress Reports"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Generate and view comprehensive student progress reports"})]}),(0,r.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Bulk Generate Reports"})]}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"pt-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Class"}),(0,r.jsxs)("select",{value:i,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"Grade 11 - A",children:"Grade 11 - Division A"}),(0,r.jsx)("option",{value:"Grade 11 - B",children:"Grade 11 - Division B"}),(0,r.jsx)("option",{value:"Grade 10 - A",children:"Grade 10 - Division A"}),(0,r.jsx)("option",{value:"Grade 10 - B",children:"Grade 10 - Division B"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Report Type"}),(0,r.jsxs)("select",{value:m,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"comprehensive",children:"Comprehensive Report"}),(0,r.jsx)("option",{value:"academic",children:"Academic Performance Only"}),(0,r.jsx)("option",{value:"attendance",children:"Attendance Report"}),(0,r.jsx)("option",{value:"behavioral",children:"Behavioral Assessment"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Time Period"}),(0,r.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"current_term",children:"Current Term"}),(0,r.jsx)("option",{value:"semester",children:"Full Semester"}),(0,r.jsx)("option",{value:"academic_year",children:"Academic Year"}),(0,r.jsx)("option",{value:"custom",children:"Custom Range"})]})]})]})})}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{children:[(0,r.jsxs)(n.ll,{children:["Students - ",i]}),(0,r.jsx)(n.SZ,{children:"Select a student to view detailed progress report"})]}),(0,r.jsx)(n.aY,{children:(0,r.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[{id:"1",name:"John Smith",rollNumber:"11A001",grade:"Grade 11",division:"A"},{id:"2",name:"Sarah Johnson",rollNumber:"11A002",grade:"Grade 11",division:"A"},{id:"3",name:"Michael Brown",rollNumber:"11A003",grade:"Grade 11",division:"A"},{id:"4",name:"Emily Davis",rollNumber:"11A004",grade:"Grade 11",division:"A"}].map(e=>(0,r.jsxs)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.rollNumber})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-blue-600",children:"3.7"}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"GPA"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm mb-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Attendance:"}),(0,r.jsx)("span",{className:"ml-1 font-medium text-green-600",children:"92%"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Rank:"}),(0,r.jsx)("span",{className:"ml-1 font-medium",children:"3rd"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>b(e.id),className:"flex-1 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700",children:"View Progress"}),(0,r.jsx)("button",{onClick:()=>null==t?void 0:t(e.id,m),className:"flex-1 bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200",children:"Generate Report"})]})]},e.id))})})]}),u&&(0,r.jsx)(()=>u?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold",children:["Progress Report - ",u.student.name]}),(0,r.jsx)("button",{onClick:()=>h(null),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsx)(n.ll,{className:"text-lg",children:"Overall GPA"})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600",children:u.overallGPA}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Out of 4.0"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsx)(n.ll,{className:"text-lg",children:"Attendance"})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsxs)("div",{className:"text-3xl font-bold text-green-600",children:[u.overallAttendance,"%"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Overall attendance rate"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{className:"pb-3",children:(0,r.jsx)(n.ll,{className:"text-lg",children:"Class Rank"})}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-purple-600",children:"3rd"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Out of 45 students"})]})]})]}),(0,r.jsxs)(n.Zb,{className:"mb-6",children:[(0,r.jsx)(n.Ol,{children:(0,r.jsx)(n.ll,{children:"Subject Performance"})}),(0,r.jsx)(n.aY,{children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full table-auto",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Subject"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Grade"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Percentage"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Attendance"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Assignments"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Trend"})]})}),(0,r.jsx)("tbody",{children:u.subjects.map((e,s)=>(0,r.jsxs)("tr",{className:"border-b border-gray-100",children:[(0,r.jsx)("td",{className:"py-3 px-4 font-medium",children:e.subject}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsx)("span",{className:"font-bold ".concat(f(e.percentage)),children:e.currentGrade})}),(0,r.jsxs)("td",{className:"py-3 px-4",children:[e.percentage,"%"]}),(0,r.jsxs)("td",{className:"py-3 px-4",children:[e.attendance,"%"]}),(0,r.jsxs)("td",{className:"py-3 px-4",children:[e.assignments.completed,"/",e.assignments.total]}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsxs)("span",{className:"flex items-center ".concat(j(e.trend)),children:[g(e.trend)," ",e.trend]})})]},s))})]})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{children:(0,r.jsx)(n.ll,{children:"Achievements"})}),(0,r.jsx)(n.aY,{children:(0,r.jsx)("ul",{className:"space-y-2",children:u.achievements.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-green-500 mr-2",children:"\uD83C\uDFC6"}),(0,r.jsx)("span",{className:"text-sm",children:e})]},s))})})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{children:(0,r.jsx)(n.ll,{children:"Areas for Improvement"})}),(0,r.jsx)(n.aY,{children:(0,r.jsx)("ul",{className:"space-y-2",children:u.areasForImprovement.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-orange-500 mr-2",children:"\uD83D\uDCDD"}),(0,r.jsx)("span",{className:"text-sm",children:e})]},s))})})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:()=>null==l?void 0:l(u.student.id),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Schedule Parent Meeting"}),(0,r.jsx)("button",{onClick:()=>null==t?void 0:t(u.student.id,m),className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",children:"Generate Report"})]})]})}):null,{})]})}function i(){return(0,r.jsx)(l,{tenantId:"demo-tenant-uuid-1234567890",onGenerateReport:(e,s)=>{console.log("Generate progress report:",{studentId:e,reportType:s})},onScheduleParentMeeting:e=>{console.log("Schedule parent meeting for student:",e)}})}},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return d},aY:function(){return x},SZ:function(){return m},Ol:function(){return c},ll:function(){return o}});var r=t(7437),a=t(2265),n=t(7042),l=t(4769);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,l.m6)((0,n.W)(s))}let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:i("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});d.displayName="Card";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:i("flex flex-col space-y-1.5 p-6",t),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:i("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let m=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:i("text-sm text-muted-foreground",t),...a})});m.displayName="CardDescription";let x=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:i("p-6 pt-0",t),...a})});x.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:i("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"}},function(e){e.O(0,[7895,2971,4938,1744],function(){return e(e.s=86)}),_N_E=e.O()}]);