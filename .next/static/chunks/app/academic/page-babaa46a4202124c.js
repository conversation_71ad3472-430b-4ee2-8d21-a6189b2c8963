(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7525],{7914:function(e,s,t){Promise.resolve().then(t.bind(t,1514))},9197:function(e,s,t){"use strict";t.d(s,{Zb:function(){return d},aY:function(){return h},SZ:function(){return m},Ol:function(){return i},ll:function(){return x}});var a=t(7437),r=t(2265),l=t(7042),c=t(4769);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,c.m6)((0,l.W)(s))}let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:n("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});d.displayName="Card";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:n("flex flex-col space-y-1.5 p-6",t),...r})});i.displayName="CardHeader";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:n("text-2xl font-semibold leading-none tracking-tight",t),...r})});x.displayName="CardTitle";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:n("text-sm text-muted-foreground",t),...r})});m.displayName="CardDescription";let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:n("p-6 pt-0",t),...r})});h.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:n("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},1514:function(e,s,t){"use strict";t.r(s),t.d(s,{AcademicDashboard:function(){return n}});var a=t(7437);t(2265);var r=t(1396),l=t.n(r),c=t(9197);function n(e){let{tenantId:s}=e,t={totalStudents:1250,totalTeachers:85,totalClasses:42,totalSubjects:28,averageAttendance:92.5,upcomingAssessments:15,activeAcademicYear:"2024-25"};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Academic Dashboard"}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["Academic Year ",t.activeAcademicYear," - Overview and Management"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90",children:"Generate Reports"}),(0,a.jsx)("button",{className:"bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90",children:"New Assessment"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-6",children:[(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(c.ll,{className:"text-sm font-medium",children:"Total Students"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC65"})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.totalStudents}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active enrollment"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(c.ll,{className:"text-sm font-medium",children:"Teachers"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC68‍\uD83C\uDFEB"})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.totalTeachers}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active faculty"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(c.ll,{className:"text-sm font-medium",children:"Classes"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFEB"})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.totalClasses}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active classes"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(c.ll,{className:"text-sm font-medium",children:"Subjects"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDA"})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.totalSubjects}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Curriculum subjects"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(c.ll,{className:"text-sm font-medium",children:"Attendance"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.averageAttendance,"%"]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Average this month"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(c.ll,{className:"text-sm font-medium",children:"Assessments"}),(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.upcomingAssessments}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Upcoming this week"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsxs)(c.Zb,{className:"lg:col-span-2",children:[(0,a.jsxs)(c.Ol,{children:[(0,a.jsx)(c.ll,{children:"Recent Activities"}),(0,a.jsx)(c.SZ,{children:"Latest academic activities and updates"})]}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{type:"assessment",title:"Math Quiz - Grade 9A",teacher:"Ms. Johnson",date:"2024-01-15",status:"completed"},{type:"lesson",title:"Introduction to Physics",teacher:"Mr. Smith",date:"2024-01-15",status:"published"},{type:"attendance",title:"Daily Attendance - Grade 10B",teacher:"Mrs. Brown",date:"2024-01-15",status:"marked"},{type:"grade",title:"English Essay Grades",teacher:"Ms. Davis",date:"2024-01-14",status:"published"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-sm",children:["assessment"===e.type&&"\uD83D\uDCDD","lesson"===e.type&&"\uD83D\uDCDA","attendance"===e.type&&"✅","grade"===e.type&&"\uD83D\uDCCA"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.teacher})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm font-medium capitalize",children:e.status}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.date})]})]},s))})})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{children:[(0,a.jsx)(c.ll,{children:"Upcoming Assessments"}),(0,a.jsx)(c.SZ,{children:"Scheduled tests and assignments"})]}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{subject:"Mathematics",class:"Grade 9A",type:"Test",date:"2024-01-18",teacher:"Ms. Johnson"},{subject:"Science",class:"Grade 8B",type:"Quiz",date:"2024-01-19",teacher:"Mr. Wilson"},{subject:"English",class:"Grade 10A",type:"Assignment",date:"2024-01-20",teacher:"Ms. Davis"},{subject:"History",class:"Grade 11B",type:"Project",date:"2024-01-22",teacher:"Mr. Thompson"}].map((e,s)=>(0,a.jsxs)("div",{className:"p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"font-medium text-sm",children:e.subject}),(0,a.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded",children:e.type})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.class}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.teacher}),(0,a.jsx)("p",{className:"text-xs text-primary font-medium mt-1",children:e.date})]},s))})})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{children:[(0,a.jsx)(c.ll,{children:"Class Performance Overview"}),(0,a.jsx)(c.SZ,{children:"Academic performance and attendance by class"})]}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full table-auto",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Class"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Students"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Avg Grade"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Attendance"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:[{class:"Grade 9A",students:30,avgGrade:85.2,attendance:94.5},{class:"Grade 9B",students:28,avgGrade:82.7,attendance:91.2},{class:"Grade 10A",students:32,avgGrade:88.1,attendance:96.3},{class:"Grade 10B",students:29,avgGrade:84.9,attendance:93.8}].map((e,s)=>(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"font-medium text-primary",children:e.class})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"text-gray-900",children:e.students})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"text-gray-900",children:[e.avgGrade,"%"]}),(0,a.jsx)("div",{className:"w-16 h-2 bg-gray-200 rounded-full",children:(0,a.jsx)("div",{className:"h-2 bg-green-500 rounded-full",style:{width:"".concat(e.avgGrade/100*100,"%")}})})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"text-gray-900",children:[e.attendance,"%"]}),(0,a.jsx)("div",{className:"w-16 h-2 bg-gray-200 rounded-full",children:(0,a.jsx)("div",{className:"h-2 bg-blue-500 rounded-full",style:{width:"".concat(e.attendance,"%")}})})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"text-primary hover:text-primary/80 text-sm font-medium",children:"View Details"}),(0,a.jsx)("button",{className:"text-gray-600 hover:text-gray-800 text-sm font-medium",children:"Reports"})]})})]},s))})]})})})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(c.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(c.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDA"}),(0,a.jsx)(c.ll,{className:"text-lg",children:"Curriculum"})]})}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage curriculum and syllabus"}),(0,a.jsx)("button",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Curriculum →"})]})]}),(0,a.jsxs)(c.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(c.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"}),(0,a.jsx)(c.ll,{className:"text-lg",children:"Assessments"})]})}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Create and manage assessments"}),(0,a.jsx)("button",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Assessments →"})]})]}),(0,a.jsxs)(c.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(c.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,a.jsx)(c.ll,{className:"text-lg",children:"Attendance"})]})}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Track student attendance"}),(0,a.jsx)("button",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Mark Attendance →"})]})]}),(0,a.jsxs)(c.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(c.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCB"}),(0,a.jsx)(c.ll,{className:"text-lg",children:"Reports"})]})}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Generate academic reports"}),(0,a.jsx)("button",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Reports →"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6",children:[(0,a.jsx)(l(),{href:"/academic/curriculum",children:(0,a.jsxs)(c.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(c.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDA"}),(0,a.jsx)(c.ll,{className:"text-lg",children:"Curriculum"})]})}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Manage subjects & syllabus"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Curriculum →"})]})]})}),(0,a.jsx)(l(),{href:"/academic/schedule",children:(0,a.jsxs)(c.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(c.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC5"}),(0,a.jsx)(c.ll,{className:"text-lg",children:"Schedule"})]})}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Class timetables"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Manage Schedule →"})]})]})}),(0,a.jsx)(l(),{href:"/academic/assessments",children:(0,a.jsxs)(c.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(c.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"}),(0,a.jsx)(c.ll,{className:"text-lg",children:"Assessments"})]})}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Tests & assignments"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Create Assessments →"})]})]})}),(0,a.jsx)(l(),{href:"/academic/grades",children:(0,a.jsxs)(c.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(c.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,a.jsx)(c.ll,{className:"text-lg",children:"Grades"})]})}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Grade entry & management"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Enter Grades →"})]})]})}),(0,a.jsx)(l(),{href:"/academic/attendance",children:(0,a.jsxs)(c.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(c.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"✅"}),(0,a.jsx)(c.ll,{className:"text-lg",children:"Attendance"})]})}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Track student attendance"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"Track Attendance →"})]})]})}),(0,a.jsx)(l(),{href:"/academic/progress",children:(0,a.jsxs)(c.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:[(0,a.jsx)(c.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC8"}),(0,a.jsx)(c.ll,{className:"text-lg",children:"Progress Reports"})]})}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Student progress analytics"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary hover:text-primary/80",children:"View Progress →"})]})]})})]})]})}},1396:function(e,s,t){e.exports=t(5250)}},function(e){e.O(0,[7895,5250,2971,4938,1744],function(){return e(e.s=7914)}),_N_E=e.O()}]);