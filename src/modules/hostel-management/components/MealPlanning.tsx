'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface MealPlan {
  id: string
  date: string
  dayOfWeek: string
  meals: {
    breakfast: {
      items: string[]
      time: string
      calories: number
      cost: number
    }
    lunch: {
      items: string[]
      time: string
      calories: number
      cost: number
    }
    dinner: {
      items: string[]
      time: string
      calories: number
      cost: number
    }
    snacks?: {
      items: string[]
      time: string
      calories: number
      cost: number
    }
  }
  specialDiet: {
    vegetarian: string[]
    vegan: string[]
    glutenFree: string[]
    diabetic: string[]
  }
  nutritionInfo: {
    totalCalories: number
    protein: number
    carbs: number
    fat: number
    fiber: number
  }
  status: 'planned' | 'approved' | 'served' | 'cancelled'
  createdBy: string
  approvedBy?: string
  notes?: string
}

interface MealSubscription {
  id: string
  studentId: string
  studentName: string
  planType: 'full' | 'breakfast_only' | 'lunch_dinner' | 'custom'
  mealPreferences: {
    vegetarian: boolean
    vegan: boolean
    glutenFree: boolean
    diabetic: boolean
    allergies: string[]
  }
  startDate: string
  endDate?: string
  monthlyFee: number
  status: 'active' | 'paused' | 'cancelled'
  paymentStatus: 'paid' | 'pending' | 'overdue'
  lastPayment: string
  nextDue: string
}

interface MealPlanningProps {
  tenantId: string
  onCreateMealPlan?: () => void
  onApproveMealPlan?: (planId: string) => void
  onUpdateSubscription?: (subscriptionId: string) => void
  onGenerateMenu?: (date: string) => void
}

export function MealPlanning({ tenantId, onCreateMealPlan, onApproveMealPlan, onUpdateSubscription, onGenerateMenu }: MealPlanningProps) {
  const [activeTab, setActiveTab] = useState('plans')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedWeek, setSelectedWeek] = useState('current')
  const [selectedPlanType, setSelectedPlanType] = useState('all')
  const [selectedMealPlan, setSelectedMealPlan] = useState<MealPlan | null>(null)

  // Mock data for meal plans
  const mealPlans: MealPlan[] = [
    {
      id: '1',
      date: '2024-02-05',
      dayOfWeek: 'Monday',
      meals: {
        breakfast: {
          items: ['Poha', 'Sambar', 'Coconut Chutney', 'Tea/Coffee'],
          time: '07:30 - 09:00',
          calories: 350,
          cost: 25
        },
        lunch: {
          items: ['Rice', 'Dal', 'Mixed Vegetable Curry', 'Pickle', 'Curd'],
          time: '12:00 - 14:00',
          calories: 550,
          cost: 45
        },
        dinner: {
          items: ['Chapati', 'Paneer Butter Masala', 'Jeera Rice', 'Salad'],
          time: '19:00 - 21:00',
          calories: 480,
          cost: 50
        },
        snacks: {
          items: ['Samosa', 'Green Tea'],
          time: '16:00 - 17:00',
          calories: 180,
          cost: 15
        }
      },
      specialDiet: {
        vegetarian: ['All items are vegetarian'],
        vegan: ['Poha (without ghee)', 'Dal', 'Mixed Vegetables'],
        glutenFree: ['Rice', 'Dal', 'Vegetables', 'Curd'],
        diabetic: ['Reduced rice portion', 'Extra vegetables', 'No sugar in tea']
      },
      nutritionInfo: {
        totalCalories: 1560,
        protein: 45,
        carbs: 220,
        fat: 35,
        fiber: 25
      },
      status: 'approved',
      createdBy: 'Chef Manager',
      approvedBy: 'Hostel Warden',
      notes: 'Popular Monday menu with balanced nutrition'
    },
    {
      id: '2',
      date: '2024-02-06',
      dayOfWeek: 'Tuesday',
      meals: {
        breakfast: {
          items: ['Idli', 'Sambar', 'Tomato Chutney', 'Filter Coffee'],
          time: '07:30 - 09:00',
          calories: 320,
          cost: 25
        },
        lunch: {
          items: ['Biryani', 'Raita', 'Boiled Egg', 'Pickle', 'Papad'],
          time: '12:00 - 14:00',
          calories: 650,
          cost: 55
        },
        dinner: {
          items: ['Roti', 'Rajma', 'Jeera Rice', 'Mixed Salad'],
          time: '19:00 - 21:00',
          calories: 520,
          cost: 50
        }
      },
      specialDiet: {
        vegetarian: ['Vegetable Biryani instead of regular'],
        vegan: ['Idli (without ghee)', 'Sambar', 'Rajma'],
        glutenFree: ['Rice items', 'Raita', 'Salad'],
        diabetic: ['Brown rice biryani', 'Extra vegetables']
      },
      nutritionInfo: {
        totalCalories: 1490,
        protein: 42,
        carbs: 210,
        fat: 38,
        fiber: 22
      },
      status: 'planned',
      createdBy: 'Chef Manager',
      notes: 'Special Tuesday biryani day'
    }
  ]

  // Mock data for meal subscriptions
  const mealSubscriptions: MealSubscription[] = [
    {
      id: '1',
      studentId: 'STU-2024-001',
      studentName: 'Rahul Sharma',
      planType: 'full',
      mealPreferences: {
        vegetarian: true,
        vegan: false,
        glutenFree: false,
        diabetic: false,
        allergies: ['Peanuts']
      },
      startDate: '2024-01-15',
      monthlyFee: 4500,
      status: 'active',
      paymentStatus: 'paid',
      lastPayment: '2024-01-15',
      nextDue: '2024-02-15'
    },
    {
      id: '2',
      studentId: 'STU-2024-002',
      studentName: 'Priya Singh',
      planType: 'lunch_dinner',
      mealPreferences: {
        vegetarian: true,
        vegan: true,
        glutenFree: false,
        diabetic: false,
        allergies: []
      },
      startDate: '2024-01-20',
      monthlyFee: 3200,
      status: 'active',
      paymentStatus: 'pending',
      lastPayment: '2024-01-20',
      nextDue: '2024-02-20'
    },
    {
      id: '3',
      studentId: 'STU-2024-003',
      studentName: 'Amit Kumar',
      planType: 'breakfast_only',
      mealPreferences: {
        vegetarian: false,
        vegan: false,
        glutenFree: true,
        diabetic: false,
        allergies: ['Dairy']
      },
      startDate: '2024-01-15',
      monthlyFee: 1500,
      status: 'active',
      paymentStatus: 'overdue',
      lastPayment: '2024-01-15',
      nextDue: '2024-02-15'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned':
        return 'bg-yellow-100 text-yellow-800'
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'served':
        return 'bg-blue-100 text-blue-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPlanTypeColor = (type: string) => {
    switch (type) {
      case 'full':
        return 'bg-green-100 text-green-800'
      case 'breakfast_only':
        return 'bg-yellow-100 text-yellow-800'
      case 'lunch_dinner':
        return 'bg-blue-100 text-blue-800'
      case 'custom':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredMealPlans = mealPlans.filter(plan => {
    const matchesSearch = plan.dayOfWeek.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         plan.meals.breakfast.items.some(item => item.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         plan.meals.lunch.items.some(item => item.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         plan.meals.dinner.items.some(item => item.toLowerCase().includes(searchTerm.toLowerCase()))
    return matchesSearch
  })

  const filteredSubscriptions = mealSubscriptions.filter(subscription => {
    const matchesSearch = subscription.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         subscription.studentId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesPlanType = selectedPlanType === 'all' || subscription.planType === selectedPlanType
    return matchesSearch && matchesPlanType
  })

  const MealPlanDetailModal = () => {
    if (!selectedMealPlan) return null

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold">{selectedMealPlan.dayOfWeek} - {selectedMealPlan.date}</h3>
            <button
              onClick={() => setSelectedMealPlan(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Meal Schedule</h4>
              <div className="space-y-4">
                <div className="border rounded-lg p-3">
                  <h5 className="font-medium text-green-600">Breakfast ({selectedMealPlan.meals.breakfast.time})</h5>
                  <ul className="text-sm text-gray-600 mt-1">
                    {selectedMealPlan.meals.breakfast.items.map((item, index) => (
                      <li key={index}>• {item}</li>
                    ))}
                  </ul>
                  <div className="text-xs text-gray-500 mt-2">
                    {selectedMealPlan.meals.breakfast.calories} cal • ₹{selectedMealPlan.meals.breakfast.cost}
                  </div>
                </div>
                <div className="border rounded-lg p-3">
                  <h5 className="font-medium text-blue-600">Lunch ({selectedMealPlan.meals.lunch.time})</h5>
                  <ul className="text-sm text-gray-600 mt-1">
                    {selectedMealPlan.meals.lunch.items.map((item, index) => (
                      <li key={index}>• {item}</li>
                    ))}
                  </ul>
                  <div className="text-xs text-gray-500 mt-2">
                    {selectedMealPlan.meals.lunch.calories} cal • ₹{selectedMealPlan.meals.lunch.cost}
                  </div>
                </div>
                <div className="border rounded-lg p-3">
                  <h5 className="font-medium text-purple-600">Dinner ({selectedMealPlan.meals.dinner.time})</h5>
                  <ul className="text-sm text-gray-600 mt-1">
                    {selectedMealPlan.meals.dinner.items.map((item, index) => (
                      <li key={index}>• {item}</li>
                    ))}
                  </ul>
                  <div className="text-xs text-gray-500 mt-2">
                    {selectedMealPlan.meals.dinner.calories} cal • ₹{selectedMealPlan.meals.dinner.cost}
                  </div>
                </div>
                {selectedMealPlan.meals.snacks && (
                  <div className="border rounded-lg p-3">
                    <h5 className="font-medium text-orange-600">Snacks ({selectedMealPlan.meals.snacks.time})</h5>
                    <ul className="text-sm text-gray-600 mt-1">
                      {selectedMealPlan.meals.snacks.items.map((item, index) => (
                        <li key={index}>• {item}</li>
                      ))}
                    </ul>
                    <div className="text-xs text-gray-500 mt-2">
                      {selectedMealPlan.meals.snacks.calories} cal • ₹{selectedMealPlan.meals.snacks.cost}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Nutrition Information</h4>
              <div className="space-y-2 text-sm bg-gray-50 p-3 rounded">
                <div><span className="font-medium">Total Calories:</span> {selectedMealPlan.nutritionInfo.totalCalories}</div>
                <div><span className="font-medium">Protein:</span> {selectedMealPlan.nutritionInfo.protein}g</div>
                <div><span className="font-medium">Carbohydrates:</span> {selectedMealPlan.nutritionInfo.carbs}g</div>
                <div><span className="font-medium">Fat:</span> {selectedMealPlan.nutritionInfo.fat}g</div>
                <div><span className="font-medium">Fiber:</span> {selectedMealPlan.nutritionInfo.fiber}g</div>
              </div>

              <h4 className="font-medium mb-3 mt-6">Special Diet Options</h4>
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-sm text-green-600">Vegetarian</h5>
                  <ul className="text-xs text-gray-600">
                    {selectedMealPlan.specialDiet.vegetarian.map((item, index) => (
                      <li key={index}>• {item}</li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-sm text-blue-600">Vegan</h5>
                  <ul className="text-xs text-gray-600">
                    {selectedMealPlan.specialDiet.vegan.map((item, index) => (
                      <li key={index}>• {item}</li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-sm text-purple-600">Gluten-Free</h5>
                  <ul className="text-xs text-gray-600">
                    {selectedMealPlan.specialDiet.glutenFree.map((item, index) => (
                      <li key={index}>• {item}</li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-sm text-orange-600">Diabetic</h5>
                  <ul className="text-xs text-gray-600">
                    {selectedMealPlan.specialDiet.diabetic.map((item, index) => (
                      <li key={index}>• {item}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h4 className="font-medium mb-3">Plan Details</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><span className="font-medium">Status:</span> 
                <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getStatusColor(selectedMealPlan.status)}`}>
                  {selectedMealPlan.status.toUpperCase()}
                </span>
              </div>
              <div><span className="font-medium">Created by:</span> {selectedMealPlan.createdBy}</div>
              {selectedMealPlan.approvedBy && (
                <div><span className="font-medium">Approved by:</span> {selectedMealPlan.approvedBy}</div>
              )}
              <div><span className="font-medium">Total Cost:</span> ₹{
                selectedMealPlan.meals.breakfast.cost + 
                selectedMealPlan.meals.lunch.cost + 
                selectedMealPlan.meals.dinner.cost + 
                (selectedMealPlan.meals.snacks?.cost || 0)
              }</div>
            </div>
            {selectedMealPlan.notes && (
              <div className="mt-3">
                <span className="font-medium">Notes:</span>
                <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded mt-1">{selectedMealPlan.notes}</p>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3">
            {selectedMealPlan.status === 'planned' && (
              <button
                onClick={() => onApproveMealPlan?.(selectedMealPlan.id)}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
              >
                Approve Plan
              </button>
            )}
            <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
              Edit Plan
            </button>
            <button className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
              Duplicate Plan
            </button>
          </div>
        </div>
      </div>
    )
  }
}
