'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'

interface HostelDashboardProps {
  tenantId: string
}

export function HostelDashboard({ tenantId }: HostelDashboardProps) {
  const stats = {
    totalRooms: 150,
    occupiedRooms: 142,
    totalBeds: 300,
    occupiedBeds: 285,
    occupancyRate: 95,
    maintenanceRequests: 8
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Hostel Management</h1>
          <p className="text-muted-foreground">Manage hostel operations and resident services</p>
        </div>
        <div className="flex items-center space-x-2">
          <button className="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90">
            Generate Report
          </button>
          <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90">
            Room Allocation
          </button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Rooms</CardTitle>
            <span className="text-2xl">🏠</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalRooms}</div>
            <p className="text-xs text-muted-foreground">Available rooms</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Occupied</CardTitle>
            <span className="text-2xl">🔑</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.occupiedRooms}</div>
            <p className="text-xs text-muted-foreground">Rooms in use</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Beds</CardTitle>
            <span className="text-2xl">🛏️</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalBeds}</div>
            <p className="text-xs text-muted-foreground">Bed capacity</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Occupied Beds</CardTitle>
            <span className="text-2xl">👥</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.occupiedBeds}</div>
            <p className="text-xs text-muted-foreground">Current residents</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Occupancy Rate</CardTitle>
            <span className="text-2xl">📊</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.occupancyRate}%</div>
            <p className="text-xs text-muted-foreground">Current occupancy</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maintenance</CardTitle>
            <span className="text-2xl">🔧</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.maintenanceRequests}</div>
            <p className="text-xs text-muted-foreground">Pending requests</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">🏠</span>
              <CardTitle className="text-lg">Room Management</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">Manage room allocation and availability</p>
            <button className="text-sm font-medium text-primary hover:text-primary/80">
              Manage Rooms →
            </button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">👥</span>
              <CardTitle className="text-lg">Residents</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">Manage resident information</p>
            <button className="text-sm font-medium text-primary hover:text-primary/80">
              View Residents →
            </button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">🍽️</span>
              <CardTitle className="text-lg">Meal Plans</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">Manage dining and meal services</p>
            <button className="text-sm font-medium text-primary hover:text-primary/80">
              Manage Meals →
            </button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">🔧</span>
              <CardTitle className="text-lg">Maintenance</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">Track maintenance requests</p>
            <button className="text-sm font-medium text-primary hover:text-primary/80">
              View Requests →
            </button>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
        <Link href="/hostel/rooms">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-2">
                <span className="text-2xl">🏠</span>
                <CardTitle className="text-lg">Room Allocation</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-3">Manage room assignments</p>
              <span className="text-sm font-medium text-primary hover:text-primary/80">
                Manage Rooms →
              </span>
            </CardContent>
          </Card>
        </Link>

        <Link href="/hostel/residents">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-2">
                <span className="text-2xl">👥</span>
                <CardTitle className="text-lg">Residents</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-3">Student resident profiles</p>
              <span className="text-sm font-medium text-primary hover:text-primary/80">
                View Residents →
              </span>
            </CardContent>
          </Card>
        </Link>

        <Link href="/hostel/maintenance">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-2">
                <span className="text-2xl">🔧</span>
                <CardTitle className="text-lg">Maintenance</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-3">Repair & maintenance</p>
              <span className="text-sm font-medium text-primary hover:text-primary/80">
                View Requests →
              </span>
            </CardContent>
          </Card>
        </Link>

        <Link href="/hostel/visitors">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-2">
                <span className="text-2xl">🚪</span>
                <CardTitle className="text-lg">Visitors</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-3">Guest access control</p>
              <span className="text-sm font-medium text-primary hover:text-primary/80">
                Manage Visitors →
              </span>
            </CardContent>
          </Card>
        </Link>

        <Link href="/hostel/facilities">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-2">
                <span className="text-2xl">🏢</span>
                <CardTitle className="text-lg">Facilities</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-3">Common area management</p>
              <span className="text-sm font-medium text-primary hover:text-primary/80">
                View Facilities →
              </span>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  )
}
