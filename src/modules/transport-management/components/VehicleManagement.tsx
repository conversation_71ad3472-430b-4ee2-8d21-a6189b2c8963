'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface Vehicle {
  id: string
  vehicleId: string
  registrationNumber: string
  vehicleType: 'bus' | 'van' | 'car' | 'mini_bus'
  make: string
  model: string
  year: number
  capacity: number
  currentOccupancy: number
  fuelType: 'diesel' | 'petrol' | 'cng' | 'electric'
  engineNumber: string
  chassisNumber: string
  color: string
  purchaseDate: string
  purchasePrice: number
  currentValue: number
  insurance: {
    provider: string
    policyNumber: string
    startDate: string
    endDate: string
    premium: number
    coverage: string[]
    status: 'active' | 'expired' | 'pending_renewal'
  }
  permits: {
    type: string
    number: string
    issuedBy: string
    issueDate: string
    expiryDate: string
    status: 'valid' | 'expired' | 'pending_renewal'
  }[]
  maintenance: {
    lastService: string
    nextService: string
    serviceInterval: number
    totalMileage: number
    fuelEfficiency: number
    maintenanceCost: number
    warrantyStatus: 'active' | 'expired'
    warrantyEndDate?: string
  }
  safety: {
    lastInspection: string
    nextInspection: string
    safetyRating: number
    fitnessStatus: 'fit' | 'unfit' | 'pending_inspection'
    emergencyEquipment: string[]
    gpsInstalled: boolean
    cameraInstalled: boolean
    firstAidKit: boolean
  }
  assignment: {
    routeId?: string
    routeName?: string
    driverId?: string
    driverName?: string
    conductorId?: string
    conductorName?: string
    shift: 'morning' | 'afternoon' | 'both' | 'unassigned'
  }
  status: 'active' | 'maintenance' | 'out_of_service' | 'retired'
  documents: {
    type: string
    fileName: string
    uploadDate: string
    expiryDate?: string
    verified: boolean
  }[]
  fuelRecords: {
    date: string
    quantity: number
    cost: number
    mileage: number
    fuelStation: string
  }[]
  incidentHistory: {
    date: string
    type: 'accident' | 'breakdown' | 'violation' | 'complaint'
    description: string
    severity: 'low' | 'medium' | 'high'
    resolved: boolean
  }[]
  lastUpdated: string
  createdDate: string
}

interface VehicleManagementProps {
  tenantId: string
  onAddVehicle?: () => void
  onEditVehicle?: (vehicleId: string) => void
  onScheduleMaintenance?: (vehicleId: string) => void
  onUpdateStatus?: (vehicleId: string, status: string) => void
}

export function VehicleManagement({ tenantId, onAddVehicle, onEditVehicle, onScheduleMaintenance, onUpdateStatus }: VehicleManagementProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedFuelType, setSelectedFuelType] = useState('all')
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null)

  // Mock data for vehicles
  const vehicles: Vehicle[] = [
    {
      id: '1',
      vehicleId: 'VEH-2024-001',
      registrationNumber: 'DL-01-AB-1234',
      vehicleType: 'bus',
      make: 'Tata',
      model: 'Starbus',
      year: 2022,
      capacity: 45,
      currentOccupancy: 38,
      fuelType: 'diesel',
      engineNumber: 'ENG123456789',
      chassisNumber: 'CHS987654321',
      color: 'Yellow',
      purchaseDate: '2022-03-15',
      purchasePrice: 2500000,
      currentValue: 2200000,
      insurance: {
        provider: 'National Insurance Company',
        policyNumber: 'POL-2024-001',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        premium: 45000,
        coverage: ['Third Party', 'Comprehensive', 'Passenger Coverage'],
        status: 'active'
      },
      permits: [
        {
          type: 'School Bus Permit',
          number: 'SBP-2024-001',
          issuedBy: 'Transport Department',
          issueDate: '2024-01-15',
          expiryDate: '2025-01-14',
          status: 'valid'
        },
        {
          type: 'Pollution Certificate',
          number: 'PUC-2024-001',
          issuedBy: 'Pollution Control Board',
          issueDate: '2024-02-01',
          expiryDate: '2024-08-01',
          status: 'valid'
        }
      ],
      maintenance: {
        lastService: '2024-01-15',
        nextService: '2024-04-15',
        serviceInterval: 5000,
        totalMileage: 45000,
        fuelEfficiency: 8.5,
        maintenanceCost: 125000,
        warrantyStatus: 'active',
        warrantyEndDate: '2025-03-15'
      },
      safety: {
        lastInspection: '2024-01-10',
        nextInspection: '2024-07-10',
        safetyRating: 4.5,
        fitnessStatus: 'fit',
        emergencyEquipment: ['Fire Extinguisher', 'First Aid Kit', 'Emergency Exit Hammer'],
        gpsInstalled: true,
        cameraInstalled: true,
        firstAidKit: true
      },
      assignment: {
        routeId: 'RT-001',
        routeName: 'Route A - Central Delhi',
        driverId: 'DRV-001',
        driverName: 'Rajesh Kumar',
        conductorId: 'CON-001',
        conductorName: 'Suresh Singh',
        shift: 'both'
      },
      status: 'active',
      documents: [
        {
          type: 'Registration Certificate',
          fileName: 'rc_dl01ab1234.pdf',
          uploadDate: '2022-03-15',
          verified: true
        },
        {
          type: 'Insurance Policy',
          fileName: 'insurance_2024.pdf',
          uploadDate: '2024-01-01',
          expiryDate: '2024-12-31',
          verified: true
        }
      ],
      fuelRecords: [
        {
          date: '2024-02-01',
          quantity: 50,
          cost: 4500,
          mileage: 44500,
          fuelStation: 'HP Petrol Pump'
        },
        {
          date: '2024-01-25',
          quantity: 45,
          cost: 4050,
          mileage: 44100,
          fuelStation: 'Indian Oil Station'
        }
      ],
      incidentHistory: [
        {
          date: '2024-01-20',
          type: 'breakdown',
          description: 'Engine overheating issue resolved',
          severity: 'medium',
          resolved: true
        }
      ],
      lastUpdated: '2024-02-05',
      createdDate: '2022-03-15'
    },
    {
      id: '2',
      vehicleId: 'VEH-2024-002',
      registrationNumber: 'DL-02-CD-5678',
      vehicleType: 'van',
      make: 'Mahindra',
      model: 'Bolero',
      year: 2023,
      capacity: 12,
      currentOccupancy: 10,
      fuelType: 'diesel',
      engineNumber: 'ENG987654321',
      chassisNumber: 'CHS123456789',
      color: 'White',
      purchaseDate: '2023-06-10',
      purchasePrice: 1200000,
      currentValue: 1100000,
      insurance: {
        provider: 'ICICI Lombard',
        policyNumber: 'POL-2024-002',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        premium: 25000,
        coverage: ['Third Party', 'Comprehensive'],
        status: 'active'
      },
      permits: [
        {
          type: 'Commercial Vehicle Permit',
          number: 'CVP-2024-002',
          issuedBy: 'Transport Department',
          issueDate: '2024-01-15',
          expiryDate: '2025-01-14',
          status: 'valid'
        }
      ],
      maintenance: {
        lastService: '2024-01-20',
        nextService: '2024-04-20',
        serviceInterval: 10000,
        totalMileage: 25000,
        fuelEfficiency: 12.0,
        maintenanceCost: 45000,
        warrantyStatus: 'active',
        warrantyEndDate: '2026-06-10'
      },
      safety: {
        lastInspection: '2024-01-15',
        nextInspection: '2024-07-15',
        safetyRating: 4.2,
        fitnessStatus: 'fit',
        emergencyEquipment: ['Fire Extinguisher', 'First Aid Kit'],
        gpsInstalled: true,
        cameraInstalled: false,
        firstAidKit: true
      },
      assignment: {
        routeId: 'RT-002',
        routeName: 'Route B - South Delhi',
        driverId: 'DRV-002',
        driverName: 'Amit Sharma',
        shift: 'morning'
      },
      status: 'active',
      documents: [
        {
          type: 'Registration Certificate',
          fileName: 'rc_dl02cd5678.pdf',
          uploadDate: '2023-06-10',
          verified: true
        }
      ],
      fuelRecords: [
        {
          date: '2024-02-02',
          quantity: 30,
          cost: 2700,
          mileage: 24800,
          fuelStation: 'Bharat Petroleum'
        }
      ],
      incidentHistory: [],
      lastUpdated: '2024-02-03',
      createdDate: '2023-06-10'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800'
      case 'out_of_service':
        return 'bg-red-100 text-red-800'
      case 'retired':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getVehicleTypeColor = (type: string) => {
    switch (type) {
      case 'bus':
        return 'bg-blue-100 text-blue-800'
      case 'van':
        return 'bg-green-100 text-green-800'
      case 'car':
        return 'bg-purple-100 text-purple-800'
      case 'mini_bus':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getFuelTypeColor = (type: string) => {
    switch (type) {
      case 'diesel':
        return 'bg-yellow-100 text-yellow-800'
      case 'petrol':
        return 'bg-red-100 text-red-800'
      case 'cng':
        return 'bg-green-100 text-green-800'
      case 'electric':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getInsuranceStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'expired':
        return 'bg-red-100 text-red-800'
      case 'pending_renewal':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getFitnessStatusColor = (status: string) => {
    switch (status) {
      case 'fit':
        return 'bg-green-100 text-green-800'
      case 'unfit':
        return 'bg-red-100 text-red-800'
      case 'pending_inspection':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredVehicles = vehicles.filter(vehicle => {
    const matchesSearch = vehicle.registrationNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.vehicleId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.assignment.routeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.assignment.driverName?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || vehicle.vehicleType === selectedType
    const matchesStatus = selectedStatus === 'all' || vehicle.status === selectedStatus
    const matchesFuelType = selectedFuelType === 'all' || vehicle.fuelType === selectedFuelType
    return matchesSearch && matchesType && matchesStatus && matchesFuelType
  })

  const VehicleDetailModal = () => {
    if (!selectedVehicle) return null

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold">{selectedVehicle.registrationNumber}</h3>
            <button
              onClick={() => setSelectedVehicle(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Vehicle Information</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Vehicle ID:</span> {selectedVehicle.vehicleId}</div>
                <div><span className="font-medium">Registration:</span> {selectedVehicle.registrationNumber}</div>
                <div><span className="font-medium">Make & Model:</span> {selectedVehicle.make} {selectedVehicle.model}</div>
                <div><span className="font-medium">Year:</span> {selectedVehicle.year}</div>
                <div><span className="font-medium">Type:</span> 
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getVehicleTypeColor(selectedVehicle.vehicleType)}`}>
                    {selectedVehicle.vehicleType.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div><span className="font-medium">Fuel Type:</span> 
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getFuelTypeColor(selectedVehicle.fuelType)}`}>
                    {selectedVehicle.fuelType.toUpperCase()}
                  </span>
                </div>
                <div><span className="font-medium">Capacity:</span> {selectedVehicle.capacity} passengers</div>
                <div><span className="font-medium">Current Occupancy:</span> {selectedVehicle.currentOccupancy}</div>
                <div><span className="font-medium">Color:</span> {selectedVehicle.color}</div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Technical Details</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Engine Number:</span> {selectedVehicle.engineNumber}</div>
                <div><span className="font-medium">Chassis Number:</span> {selectedVehicle.chassisNumber}</div>
                <div><span className="font-medium">Purchase Date:</span> {selectedVehicle.purchaseDate}</div>
                <div><span className="font-medium">Purchase Price:</span> ₹{selectedVehicle.purchasePrice.toLocaleString()}</div>
                <div><span className="font-medium">Current Value:</span> ₹{selectedVehicle.currentValue.toLocaleString()}</div>
                <div><span className="font-medium">Total Mileage:</span> {selectedVehicle.maintenance.totalMileage.toLocaleString()} km</div>
                <div><span className="font-medium">Fuel Efficiency:</span> {selectedVehicle.maintenance.fuelEfficiency} km/l</div>
                <div><span className="font-medium">Status:</span> 
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getStatusColor(selectedVehicle.status)}`}>
                    {selectedVehicle.status.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Current Assignment</h4>
              <div className="space-y-2 text-sm">
                {selectedVehicle.assignment.routeName ? (
                  <>
                    <div><span className="font-medium">Route:</span> {selectedVehicle.assignment.routeName}</div>
                    <div><span className="font-medium">Driver:</span> {selectedVehicle.assignment.driverName}</div>
                    {selectedVehicle.assignment.conductorName && (
                      <div><span className="font-medium">Conductor:</span> {selectedVehicle.assignment.conductorName}</div>
                    )}
                    <div><span className="font-medium">Shift:</span> {selectedVehicle.assignment.shift.replace('_', ' ').toUpperCase()}</div>
                  </>
                ) : (
                  <div className="text-gray-500">No current assignment</div>
                )}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Insurance Details</h4>
              <div className="space-y-2 text-sm bg-gray-50 p-3 rounded">
                <div><span className="font-medium">Provider:</span> {selectedVehicle.insurance.provider}</div>
                <div><span className="font-medium">Policy Number:</span> {selectedVehicle.insurance.policyNumber}</div>
                <div><span className="font-medium">Period:</span> {selectedVehicle.insurance.startDate} to {selectedVehicle.insurance.endDate}</div>
                <div><span className="font-medium">Premium:</span> ₹{selectedVehicle.insurance.premium.toLocaleString()}</div>
                <div><span className="font-medium">Status:</span> 
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getInsuranceStatusColor(selectedVehicle.insurance.status)}`}>
                    {selectedVehicle.insurance.status.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Coverage:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {selectedVehicle.insurance.coverage.map((coverage, index) => (
                      <span key={index} className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                        {coverage}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Safety & Fitness</h4>
              <div className="space-y-2 text-sm bg-gray-50 p-3 rounded">
                <div><span className="font-medium">Last Inspection:</span> {selectedVehicle.safety.lastInspection}</div>
                <div><span className="font-medium">Next Inspection:</span> {selectedVehicle.safety.nextInspection}</div>
                <div><span className="font-medium">Safety Rating:</span> ⭐ {selectedVehicle.safety.safetyRating}/5</div>
                <div><span className="font-medium">Fitness Status:</span> 
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getFitnessStatusColor(selectedVehicle.safety.fitnessStatus)}`}>
                    {selectedVehicle.safety.fitnessStatus.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div><span className="font-medium">GPS Installed:</span> {selectedVehicle.safety.gpsInstalled ? '✅' : '❌'}</div>
                <div><span className="font-medium">Camera Installed:</span> {selectedVehicle.safety.cameraInstalled ? '✅' : '❌'}</div>
                <div><span className="font-medium">First Aid Kit:</span> {selectedVehicle.safety.firstAidKit ? '✅' : '❌'}</div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Permits & Licenses</h4>
              <div className="space-y-2">
                {selectedVehicle.permits.map((permit, index) => (
                  <div key={index} className="border rounded p-2">
                    <div className="font-medium text-sm">{permit.type}</div>
                    <div className="text-sm text-gray-600">Number: {permit.number}</div>
                    <div className="text-sm text-gray-600">Issued by: {permit.issuedBy}</div>
                    <div className="text-sm text-gray-600">Valid until: {permit.expiryDate}</div>
                    <span className={`px-2 py-1 text-xs rounded-full ${permit.status === 'valid' ? 'bg-green-100 text-green-800' : permit.status === 'expired' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`}>
                      {permit.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Maintenance Summary</h4>
              <div className="space-y-2 text-sm bg-gray-50 p-3 rounded">
                <div><span className="font-medium">Last Service:</span> {selectedVehicle.maintenance.lastService}</div>
                <div><span className="font-medium">Next Service:</span> {selectedVehicle.maintenance.nextService}</div>
                <div><span className="font-medium">Service Interval:</span> {selectedVehicle.maintenance.serviceInterval.toLocaleString()} km</div>
                <div><span className="font-medium">Maintenance Cost:</span> ₹{selectedVehicle.maintenance.maintenanceCost.toLocaleString()}</div>
                <div><span className="font-medium">Warranty Status:</span> 
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${selectedVehicle.maintenance.warrantyStatus === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {selectedVehicle.maintenance.warrantyStatus.toUpperCase()}
                  </span>
                </div>
                {selectedVehicle.maintenance.warrantyEndDate && (
                  <div><span className="font-medium">Warranty Until:</span> {selectedVehicle.maintenance.warrantyEndDate}</div>
                )}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Emergency Equipment</h4>
              <div className="flex flex-wrap gap-2">
                {selectedVehicle.safety.emergencyEquipment.map((equipment, index) => (
                  <span key={index} className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                    {equipment}
                  </span>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Recent Fuel Records</h4>
              <div className="space-y-2">
                {selectedVehicle.fuelRecords.slice(0, 3).map((record, index) => (
                  <div key={index} className="text-sm border rounded p-2">
                    <div className="flex justify-between">
                      <span>{record.date}</span>
                      <span className="font-medium">₹{record.cost}</span>
                    </div>
                    <div className="text-gray-600">{record.quantity}L at {record.fuelStation}</div>
                    <div className="text-gray-600">Mileage: {record.mileage.toLocaleString()} km</div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {selectedVehicle.incidentHistory.length > 0 && (
            <div className="mb-6">
              <h4 className="font-medium mb-3">Incident History</h4>
              <div className="space-y-2">
                {selectedVehicle.incidentHistory.map((incident, index) => (
                  <div key={index} className="border rounded p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-sm">{incident.type.replace('_', ' ').toUpperCase()}</span>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          incident.severity === 'high' ? 'bg-red-100 text-red-800' :
                          incident.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {incident.severity.toUpperCase()}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${incident.resolved ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {incident.resolved ? 'RESOLVED' : 'PENDING'}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">{incident.description}</p>
                    <p className="text-xs text-gray-500 mt-1">Date: {incident.date}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => onEditVehicle?.(selectedVehicle.id)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Edit Vehicle
            </button>
            <button
              onClick={() => onScheduleMaintenance?.(selectedVehicle.id)}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
            >
              Schedule Maintenance
            </button>
            <button className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
              Generate Report
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Vehicle Management</h1>
          <p className="text-gray-600">Manage your fleet vehicles and their maintenance</p>
        </div>
        <button
          type="button"
          onClick={() => onAddVehicle?.()}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          + Add Vehicle
        </button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <input
                type="text"
                placeholder="Search vehicles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <select
                aria-label="Filter by vehicle type"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="bus">Bus</option>
                <option value="van">Van</option>
                <option value="car">Car</option>
                <option value="mini_bus">Mini Bus</option>
              </select>
            </div>
            <div>
              <select
                aria-label="Filter by status"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="maintenance">Maintenance</option>
                <option value="out_of_service">Out of Service</option>
                <option value="retired">Retired</option>
              </select>
            </div>
            <div>
              <select
                aria-label="Filter by fuel type"
                value={selectedFuelType}
                onChange={(e) => setSelectedFuelType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Fuel Types</option>
                <option value="diesel">Diesel</option>
                <option value="petrol">Petrol</option>
                <option value="cng">CNG</option>
                <option value="electric">Electric</option>
              </select>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">
                {filteredVehicles.length} vehicle{filteredVehicles.length !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredVehicles.map((vehicle) => (
          <Card key={vehicle.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{vehicle.registrationNumber}</CardTitle>
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(vehicle.status)}`}>
                  {vehicle.status.replace('_', ' ').toUpperCase()}
                </span>
              </div>
              <CardDescription>
                {vehicle.make} {vehicle.model} ({vehicle.year})
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Type:</span>
                  <span className="font-medium">{vehicle.vehicleType.replace('_', ' ').toUpperCase()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Capacity:</span>
                  <span className="font-medium">{vehicle.capacity} passengers</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Current Occupancy:</span>
                  <span className="font-medium">{vehicle.currentOccupancy}/{vehicle.capacity}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Fuel Type:</span>
                  <span className="font-medium">{vehicle.fuelType.toUpperCase()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Route:</span>
                  <span className="font-medium">{vehicle.assignment.routeName || 'Unassigned'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Driver:</span>
                  <span className="font-medium">{vehicle.assignment.driverName || 'Unassigned'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Next Service:</span>
                  <span className="font-medium">{vehicle.maintenance.nextService}</span>
                </div>
              </div>

              <div className="mt-4 flex items-center justify-between">
                <div className="flex space-x-2">
                  {vehicle.safety.gpsInstalled && (
                    <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                      GPS
                    </span>
                  )}
                  {vehicle.safety.cameraInstalled && (
                    <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                      Camera
                    </span>
                  )}
                  {vehicle.insurance.status === 'active' && (
                    <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                      Insured
                    </span>
                  )}
                </div>
                <button
                  type="button"
                  onClick={() => setSelectedVehicle(vehicle)}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  View Details
                </button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Vehicle Detail Modal */}
      {selectedVehicle && <VehicleDetailModal />}
    </div>
  )
}