'use client'

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface StudentTransport {
  id: string
  studentId: string
  studentName: string
  class: string
  section: string
  rollNumber: string
  parentName: string
  parentPhone: string
  address: {
    pickup: string
    drop: string
    coordinates: {
      latitude: number
      longitude: number
    }
  }
  transportDetails: {
    routeId: string
    routeName: string
    vehicleId: string
    vehicleNumber: string
    driverId: string
    driverName: string
    conductorId?: string
    conductorName?: string
    stopId: string
    stopName: string
    pickupTime: string
    dropTime: string
    distance: number
  }
  subscription: {
    planType: 'monthly' | 'quarterly' | 'annual'
    startDate: string
    endDate: string
    fee: number
    paymentStatus: 'paid' | 'pending' | 'overdue'
    lastPayment: string
    nextDue: string
  }
  attendance: {
    totalDays: number
    presentDays: number
    absentDays: number
    attendancePercentage: number
    lastPresent: string
  }
  emergencyContacts: {
    name: string
    relationship: string
    phone: string
    priority: number
  }[]
  medicalInfo: {
    bloodGroup: string
    allergies: string[]
    medications: string[]
    specialNeeds?: string
    emergencyMedicalContact: string
  }
  preferences: {
    seatPreference?: string
    specialInstructions?: string
    pickupInstructions?: string
    dropInstructions?: string
  }
  status: 'active' | 'inactive' | 'suspended' | 'graduated'
  incidents: {
    date: string
    type: 'late_pickup' | 'missed_drop' | 'behavior' | 'medical' | 'other'
    description: string
    severity: 'low' | 'medium' | 'high'
    resolved: boolean
    actionTaken?: string
  }[]
  feedback: {
    driverRating: number
    routeRating: number
    overallSatisfaction: number
    comments: string[]
    lastFeedbackDate: string
  }
  lastUpdated: string
  createdDate: string
}

interface StudentTransportationProps {
  tenantId: string
  onAssignTransport?: (studentId: string) => void
  onUpdateSubscription?: (studentId: string) => void
  onMarkAttendance?: (studentId: string, status: 'present' | 'absent') => void
  onReportIncident?: (studentId: string) => void
}

export function StudentTransportation({ tenantId, onAssignTransport, onUpdateSubscription, onMarkAttendance, onReportIncident }: StudentTransportationProps): React.JSX.Element {
  const [activeTab, setActiveTab] = useState('students')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedClass, setSelectedClass] = useState('all')
  const [selectedRoute, setSelectedRoute] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedStudent, setSelectedStudent] = useState<StudentTransport | null>(null)

  // Mock data for student transportation
  const studentTransports: StudentTransport[] = [
    {
      id: '1',
      studentId: 'STU-2024-001',
      studentName: 'Aarav Sharma',
      class: 'Grade 10',
      section: 'A',
      rollNumber: '10A001',
      parentName: 'Rajesh Sharma',
      parentPhone: '+91-9876543210',
      address: {
        pickup: '123 Rajouri Garden, Delhi',
        drop: '123 Rajouri Garden, Delhi',
        coordinates: { latitude: 28.6467, longitude: 77.1200 }
      },
      transportDetails: {
        routeId: 'RT-001',
        routeName: 'Route A - Central Delhi',
        vehicleId: 'VEH-2024-001',
        vehicleNumber: 'DL-01-AB-1234',
        driverId: 'DRV-001',
        driverName: 'Rajesh Kumar',
        conductorId: 'CON-001',
        conductorName: 'Suresh Singh',
        stopId: 'S1',
        stopName: 'Rajouri Garden Metro',
        pickupTime: '07:15',
        dropTime: '15:45',
        distance: 8.5
      },
      subscription: {
        planType: 'monthly',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        fee: 2500,
        paymentStatus: 'paid',
        lastPayment: '2024-02-01',
        nextDue: '2024-03-01'
      },
      attendance: {
        totalDays: 40,
        presentDays: 38,
        absentDays: 2,
        attendancePercentage: 95,
        lastPresent: '2024-02-05'
      },
      emergencyContacts: [
        {
          name: 'Sunita Sharma',
          relationship: 'Mother',
          phone: '+91-**********',
          priority: 1
        },
        {
          name: 'Vikram Sharma',
          relationship: 'Uncle',
          phone: '+91-**********',
          priority: 2
        }
      ],
      medicalInfo: {
        bloodGroup: 'B+',
        allergies: ['Peanuts'],
        medications: [],
        emergencyMedicalContact: '+91-**********'
      },
      preferences: {
        seatPreference: 'Window seat',
        pickupInstructions: 'Wait near metro station gate',
        dropInstructions: 'Drop at building gate'
      },
      status: 'active',
      incidents: [
        {
          date: '2024-01-25',
          type: 'late_pickup',
          description: 'Bus was 10 minutes late due to traffic',
          severity: 'low',
          resolved: true,
          actionTaken: 'Route timing adjusted'
        }
      ],
      feedback: {
        driverRating: 4.5,
        routeRating: 4.2,
        overallSatisfaction: 4.3,
        comments: ['Driver is very careful', 'Bus is clean and comfortable'],
        lastFeedbackDate: '2024-01-30'
      },
      lastUpdated: '2024-02-05',
      createdDate: '2024-01-01'
    },
    {
      id: '2',
      studentId: 'STU-2024-002',
      studentName: 'Priya Singh',
      class: 'Grade 8',
      section: 'B',
      rollNumber: '8B015',
      parentName: 'Amit Singh',
      parentPhone: '+91-9876543220',
      address: {
        pickup: '456 Lajpat Nagar, Delhi',
        drop: '456 Lajpat Nagar, Delhi',
        coordinates: { latitude: 28.5678, longitude: 77.2434 }
      },
      transportDetails: {
        routeId: 'RT-002',
        routeName: 'Route B - South Delhi',
        vehicleId: 'VEH-2024-002',
        vehicleNumber: 'DL-02-CD-5678',
        driverId: 'DRV-002',
        driverName: 'Amit Sharma',
        stopId: 'S5',
        stopName: 'Lajpat Nagar',
        pickupTime: '07:20',
        dropTime: '15:20',
        distance: 6.2
      },
      subscription: {
        planType: 'quarterly',
        startDate: '2024-01-01',
        endDate: '2024-03-31',
        fee: 7000,
        paymentStatus: 'paid',
        lastPayment: '2024-01-01',
        nextDue: '2024-04-01'
      },
      attendance: {
        totalDays: 40,
        presentDays: 40,
        absentDays: 0,
        attendancePercentage: 100,
        lastPresent: '2024-02-05'
      },
      emergencyContacts: [
        {
          name: 'Neha Singh',
          relationship: 'Mother',
          phone: '+91-**********',
          priority: 1
        }
      ],
      medicalInfo: {
        bloodGroup: 'A+',
        allergies: [],
        medications: [],
        emergencyMedicalContact: '+91-**********'
      },
      preferences: {
        dropInstructions: 'Drop at main gate'
      },
      status: 'active',
      incidents: [],
      feedback: {
        driverRating: 4.8,
        routeRating: 4.5,
        overallSatisfaction: 4.6,
        comments: ['Excellent service', 'Always on time'],
        lastFeedbackDate: '2024-02-01'
      },
      lastUpdated: '2024-02-03',
      createdDate: '2024-01-01'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-800'
      case 'suspended':
        return 'bg-red-100 text-red-800'
      case 'graduated':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPlanTypeColor = (type: string) => {
    switch (type) {
      case 'monthly':
        return 'bg-blue-100 text-blue-800'
      case 'quarterly':
        return 'bg-green-100 text-green-800'
      case 'annual':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getIncidentSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'high':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredStudents = studentTransports.filter(student => {
    const matchesSearch = student.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.rollNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.parentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.transportDetails.routeName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesClass = selectedClass === 'all' || student.class === selectedClass
    const matchesRoute = selectedRoute === 'all' || student.transportDetails.routeId === selectedRoute
    const matchesStatus = selectedStatus === 'all' || student.status === selectedStatus
    return matchesSearch && matchesClass && matchesRoute && matchesStatus
  })

  const StudentDetailModal = () => {
    if (!selectedStudent) return null

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-5xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold">{selectedStudent.studentName}</h3>
            <button
              onClick={() => setSelectedStudent(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Student Information</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Student ID:</span> {selectedStudent.studentId}</div>
                <div><span className="font-medium">Class:</span> {selectedStudent.class} - {selectedStudent.section}</div>
                <div><span className="font-medium">Roll Number:</span> {selectedStudent.rollNumber}</div>
                <div><span className="font-medium">Parent:</span> {selectedStudent.parentName}</div>
                <div><span className="font-medium">Phone:</span> {selectedStudent.parentPhone}</div>
                <div><span className="font-medium">Status:</span> 
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getStatusColor(selectedStudent.status)}`}>
                    {selectedStudent.status.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Transport Details</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Route:</span> {selectedStudent.transportDetails.routeName}</div>
                <div><span className="font-medium">Vehicle:</span> {selectedStudent.transportDetails.vehicleNumber}</div>
                <div><span className="font-medium">Driver:</span> {selectedStudent.transportDetails.driverName}</div>
                {selectedStudent.transportDetails.conductorName && (
                  <div><span className="font-medium">Conductor:</span> {selectedStudent.transportDetails.conductorName}</div>
                )}
                <div><span className="font-medium">Stop:</span> {selectedStudent.transportDetails.stopName}</div>
                <div><span className="font-medium">Pickup Time:</span> {selectedStudent.transportDetails.pickupTime}</div>
                <div><span className="font-medium">Drop Time:</span> {selectedStudent.transportDetails.dropTime}</div>
                <div><span className="font-medium">Distance:</span> {selectedStudent.transportDetails.distance} km</div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Subscription</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Plan:</span> 
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getPlanTypeColor(selectedStudent.subscription.planType)}`}>
                    {selectedStudent.subscription.planType.toUpperCase()}
                  </span>
                </div>
                <div><span className="font-medium">Period:</span> {selectedStudent.subscription.startDate} to {selectedStudent.subscription.endDate}</div>
                <div><span className="font-medium">Fee:</span> ₹{selectedStudent.subscription.fee.toLocaleString()}</div>
                <div><span className="font-medium">Payment Status:</span> 
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getPaymentStatusColor(selectedStudent.subscription.paymentStatus)}`}>
                    {selectedStudent.subscription.paymentStatus.toUpperCase()}
                  </span>
                </div>
                <div><span className="font-medium">Last Payment:</span> {selectedStudent.subscription.lastPayment}</div>
                <div><span className="font-medium">Next Due:</span> {selectedStudent.subscription.nextDue}</div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Address</h4>
              <div className="space-y-2 text-sm bg-gray-50 p-3 rounded">
                <div><span className="font-medium">Pickup:</span> {selectedStudent.address.pickup}</div>
                <div><span className="font-medium">Drop:</span> {selectedStudent.address.drop}</div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Attendance Summary</h4>
              <div className="space-y-2 text-sm bg-gray-50 p-3 rounded">
                <div><span className="font-medium">Total Days:</span> {selectedStudent.attendance.totalDays}</div>
                <div><span className="font-medium">Present:</span> {selectedStudent.attendance.presentDays}</div>
                <div><span className="font-medium">Absent:</span> {selectedStudent.attendance.absentDays}</div>
                <div><span className="font-medium">Attendance:</span> {selectedStudent.attendance.attendancePercentage}%</div>
                <div><span className="font-medium">Last Present:</span> {selectedStudent.attendance.lastPresent}</div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Emergency Contacts</h4>
              <div className="space-y-2">
                {selectedStudent.emergencyContacts.map((contact, index) => (
                  <div key={index} className="border rounded p-2">
                    <div className="font-medium text-sm">{contact.name}</div>
                    <div className="text-sm text-gray-600">{contact.relationship}</div>
                    <div className="text-sm text-gray-600">{contact.phone}</div>
                    <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                      Priority {contact.priority}
                    </span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Medical Information</h4>
              <div className="space-y-2 text-sm bg-gray-50 p-3 rounded">
                <div><span className="font-medium">Blood Group:</span> {selectedStudent.medicalInfo.bloodGroup}</div>
                <div><span className="font-medium">Emergency Contact:</span> {selectedStudent.medicalInfo.emergencyMedicalContact}</div>
                {selectedStudent.medicalInfo.allergies.length > 0 && (
                  <div>
                    <span className="font-medium">Allergies:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {selectedStudent.medicalInfo.allergies.map((allergy, index) => (
                        <span key={index} className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                          {allergy}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                {selectedStudent.medicalInfo.medications.length > 0 && (
                  <div>
                    <span className="font-medium">Medications:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {selectedStudent.medicalInfo.medications.map((medication, index) => (
                        <span key={index} className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                          {medication}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                {selectedStudent.medicalInfo.specialNeeds && (
                  <div><span className="font-medium">Special Needs:</span> {selectedStudent.medicalInfo.specialNeeds}</div>
                )}
              </div>
            </div>
          </div>

          {selectedStudent.preferences && (
            <div className="mb-6">
              <h4 className="font-medium mb-3">Preferences & Instructions</h4>
              <div className="space-y-2 text-sm bg-gray-50 p-3 rounded">
                {selectedStudent.preferences.seatPreference && (
                  <div><span className="font-medium">Seat Preference:</span> {selectedStudent.preferences.seatPreference}</div>
                )}
                {selectedStudent.preferences.pickupInstructions && (
                  <div><span className="font-medium">Pickup Instructions:</span> {selectedStudent.preferences.pickupInstructions}</div>
                )}
                {selectedStudent.preferences.dropInstructions && (
                  <div><span className="font-medium">Drop Instructions:</span> {selectedStudent.preferences.dropInstructions}</div>
                )}
                {selectedStudent.preferences.specialInstructions && (
                  <div><span className="font-medium">Special Instructions:</span> {selectedStudent.preferences.specialInstructions}</div>
                )}
              </div>
            </div>
          )}

          {selectedStudent.incidents.length > 0 && (
            <div className="mb-6">
              <h4 className="font-medium mb-3">Recent Incidents</h4>
              <div className="space-y-2">
                {selectedStudent.incidents.map((incident, index) => (
                  <div key={index} className="border rounded p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-sm">{incident.type.replace('_', ' ').toUpperCase()}</span>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 text-xs rounded-full ${getIncidentSeverityColor(incident.severity)}`}>
                          {incident.severity.toUpperCase()}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${incident.resolved ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {incident.resolved ? 'RESOLVED' : 'PENDING'}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-1">{incident.description}</p>
                    {incident.actionTaken && (
                      <p className="text-sm text-blue-600">Action: {incident.actionTaken}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">Date: {incident.date}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="mb-6">
            <h4 className="font-medium mb-3">Feedback & Ratings</h4>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="bg-blue-50 p-3 rounded">
                <div className="font-medium text-blue-800">Driver Rating</div>
                <div className="text-lg font-bold text-blue-600">⭐ {selectedStudent.feedback.driverRating}/5</div>
              </div>
              <div className="bg-green-50 p-3 rounded">
                <div className="font-medium text-green-800">Route Rating</div>
                <div className="text-lg font-bold text-green-600">⭐ {selectedStudent.feedback.routeRating}/5</div>
              </div>
              <div className="bg-purple-50 p-3 rounded">
                <div className="font-medium text-purple-800">Overall Satisfaction</div>
                <div className="text-lg font-bold text-purple-600">⭐ {selectedStudent.feedback.overallSatisfaction}/5</div>
              </div>
            </div>
            {selectedStudent.feedback.comments.length > 0 && (
              <div className="mt-3">
                <span className="font-medium text-sm">Recent Comments:</span>
                <ul className="text-sm text-gray-600 mt-1">
                  {selectedStudent.feedback.comments.map((comment, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>{comment}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => onMarkAttendance?.(selectedStudent.id, 'present')}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
            >
              Mark Present
            </button>
            <button
              onClick={() => onUpdateSubscription?.(selectedStudent.id)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Update Subscription
            </button>
            <button
              onClick={() => onReportIncident?.(selectedStudent.id)}
              className="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700"
            >
              Report Incident
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Student Transportation</h1>
        <p className="text-gray-600">Manage student transport assignments, subscriptions, and attendance</p>
      </div>

      {/* Filters */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <input
            type="text"
            placeholder="Search students..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label htmlFor="class-select" className="sr-only">
            Filter by class
          </label>
          <select
            id="class-select"
            aria-label="Filter by class"
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Classes</option>
            <option value="Grade 8">Grade 8</option>
            <option value="Grade 9">Grade 9</option>
            <option value="Grade 10">Grade 10</option>
            <option value="Grade 11">Grade 11</option>
            <option value="Grade 12">Grade 12</option>
          </select>
        </div>
        <div>
          <label htmlFor="route-select" className="sr-only">
            Filter by route
          </label>
          <select
            id="route-select"
            aria-label="Filter by route"
            value={selectedRoute}
            onChange={(e) => setSelectedRoute(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Routes</option>
            <option value="RT-001">Route A - Central Delhi</option>
            <option value="RT-002">Route B - South Delhi</option>
          </select>
        </div>
        <div>
          <label htmlFor="status-select" className="sr-only">
            Filter by status
          </label>
          <select
            id="status-select"
            aria-label="Filter by status"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="suspended">Suspended</option>
          </select>
        </div>
      </div>

      {/* Student List */}
      <div className="grid grid-cols-1 gap-4">
        {filteredStudents.map((student) => (
          <Card key={student.id} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4 items-center">
                <div>
                  <h3 className="font-semibold text-gray-900">{student.studentName}</h3>
                  <p className="text-sm text-gray-600">{student.studentId}</p>
                  <p className="text-sm text-gray-600">{student.class} - {student.section}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Route</p>
                  <p className="text-sm text-gray-600">{student.transportDetails.routeName}</p>
                  <p className="text-sm text-gray-600">{student.transportDetails.vehicleNumber}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Pickup Time</p>
                  <p className="text-sm text-gray-600">{student.transportDetails.pickupTime}</p>
                  <p className="text-sm text-gray-600">{student.transportDetails.stopName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Subscription</p>
                  <span className={`inline-block px-2 py-1 text-xs rounded-full ${getPlanTypeColor(student.subscription.planType)}`}>
                    {student.subscription.planType.toUpperCase()}
                  </span>
                  <span className={`inline-block ml-2 px-2 py-1 text-xs rounded-full ${getPaymentStatusColor(student.subscription.paymentStatus)}`}>
                    {student.subscription.paymentStatus.toUpperCase()}
                  </span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Attendance</p>
                  <p className="text-sm text-gray-600">{student.attendance.attendancePercentage}%</p>
                  <p className="text-sm text-gray-600">{student.attendance.presentDays}/{student.attendance.totalDays} days</p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setSelectedStudent(student)}
                    className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
                  >
                    View Details
                  </button>
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(student.status)}`}>
                    {student.status.toUpperCase()}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredStudents.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No students found matching the current filters.</p>
        </div>
      )}

      {/* Modal */}
      <StudentDetailModal />
    </div>
  )
}