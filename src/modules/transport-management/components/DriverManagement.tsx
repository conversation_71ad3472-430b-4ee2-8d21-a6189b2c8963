'use client'

import React, { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface Driver {
  id: string
  driverId: string
  personalInfo: {
    firstName: string
    lastName: string
    fullName: string
    dateOfBirth: string
    age: number
    gender: 'male' | 'female' | 'other'
    phone: string
    alternatePhone?: string
    email?: string
    address: {
      street: string
      city: string
      state: string
      pincode: string
    }
    emergencyContact: {
      name: string
      relationship: string
      phone: string
    }
  }
  employment: {
    employeeId: string
    joiningDate: string
    employmentType: 'full_time' | 'part_time' | 'contract'
    salary: number
    workingHours: number
    shift: 'morning' | 'afternoon' | 'both' | 'flexible'
    status: 'active' | 'inactive' | 'on_leave' | 'suspended' | 'terminated'
  }
  licensing: {
    licenseNumber: string
    licenseType: string[]
    issueDate: string
    expiryDate: string
    issuingAuthority: string
    endorsements: string[]
    restrictions?: string[]
    status: 'valid' | 'expired' | 'suspended' | 'pending_renewal'
  }
  experience: {
    totalYears: number
    schoolBusExperience: number
    previousEmployers: {
      company: string
      position: string
      duration: string
      reason: string
    }[]
    trainingCertificates: {
      name: string
      issuedBy: string
      issueDate: string
      expiryDate?: string
      verified: boolean
    }[]
  }
  assignment: {
    vehicleId?: string
    vehicleNumber?: string
    routeId?: string
    routeName?: string
    conductorId?: string
    conductorName?: string
    workingDays: string[]
    startTime: string
    endTime: string
  }
  performance: {
    safetyRating: number
    punctualityRating: number
    studentFeedbackRating: number
    supervisorRating: number
    overallRating: number
    totalTrips: number
    onTimeTrips: number
    incidentCount: number
    complaintCount: number
    commendationCount: number
  }
  health: {
    medicalCertificate: {
      issueDate: string
      expiryDate: string
      issuedBy: string
      status: 'valid' | 'expired' | 'pending'
    }
    eyeTestDate: string
    nextEyeTest: string
    bloodGroup: string
    allergies?: string[]
    medications?: string[]
    fitnessStatus: 'fit' | 'unfit' | 'conditional'
  }
  documents: {
    type: string
    fileName: string
    uploadDate: string
    expiryDate?: string
    verified: boolean
  }[]
  violations: {
    date: string
    type: 'traffic' | 'safety' | 'conduct' | 'attendance'
    description: string
    severity: 'minor' | 'major' | 'critical'
    action: string
    resolved: boolean
  }[]
  lastUpdated: string
  createdDate: string
}

interface DriverManagementProps {
  tenantId: string
  onAddDriver?: () => void
  onEditDriver?: (driverId: string) => void
  onAssignRoute?: (driverId: string, routeId: string) => void
  onUpdateStatus?: (driverId: string, status: string) => void
}

export function DriverManagement({ tenantId, onAddDriver, onEditDriver, onAssignRoute, onUpdateStatus }: DriverManagementProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedShift, setSelectedShift] = useState('all')
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null)

  // Mock data for drivers
  const drivers: Driver[] = [
    {
      id: '1',
      driverId: 'DRV-001',
      personalInfo: {
        firstName: 'Rajesh',
        lastName: 'Kumar',
        fullName: 'Rajesh Kumar',
        dateOfBirth: '1980-05-15',
        age: 44,
        gender: 'male',
        phone: '+91-9876543210',
        alternatePhone: '+91-9876543211',
        email: '<EMAIL>',
        address: {
          street: '123 Driver Colony, Sector 10',
          city: 'Delhi',
          state: 'Delhi',
          pincode: '110001'
        },
        emergencyContact: {
          name: 'Sunita Kumar',
          relationship: 'Wife',
          phone: '+91-**********'
        }
      },
      employment: {
        employeeId: 'EMP-DRV-001',
        joiningDate: '2020-03-15',
        employmentType: 'full_time',
        salary: 28000,
        workingHours: 8,
        shift: 'both',
        status: 'active'
      },
      licensing: {
        licenseNumber: 'DL-01-20200315-001',
        licenseType: ['Heavy Vehicle', 'Public Service Vehicle'],
        issueDate: '2018-03-15',
        expiryDate: '2025-03-14',
        issuingAuthority: 'Delhi Transport Department',
        endorsements: ['School Bus', 'Passenger Transport'],
        status: 'valid'
      },
      experience: {
        totalYears: 12,
        schoolBusExperience: 4,
        previousEmployers: [
          {
            company: 'City Transport Services',
            position: 'Bus Driver',
            duration: '2015-2020',
            reason: 'Career Growth'
          },
          {
            company: 'Metro Bus Corporation',
            position: 'Driver',
            duration: '2012-2015',
            reason: 'Better Opportunity'
          }
        ],
        trainingCertificates: [
          {
            name: 'Defensive Driving Course',
            issuedBy: 'Road Safety Institute',
            issueDate: '2023-01-15',
            expiryDate: '2026-01-15',
            verified: true
          },
          {
            name: 'First Aid Training',
            issuedBy: 'Red Cross Society',
            issueDate: '2023-06-10',
            expiryDate: '2025-06-10',
            verified: true
          }
        ]
      },
      assignment: {
        vehicleId: 'VEH-2024-001',
        vehicleNumber: 'DL-01-AB-1234',
        routeId: 'RT-001',
        routeName: 'Route A - Central Delhi',
        conductorId: 'CON-001',
        conductorName: 'Suresh Singh',
        workingDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
        startTime: '06:30',
        endTime: '16:00'
      },
      performance: {
        safetyRating: 4.8,
        punctualityRating: 4.6,
        studentFeedbackRating: 4.5,
        supervisorRating: 4.7,
        overallRating: 4.6,
        totalTrips: 1250,
        onTimeTrips: 1180,
        incidentCount: 1,
        complaintCount: 2,
        commendationCount: 5
      },
      health: {
        medicalCertificate: {
          issueDate: '2024-01-15',
          expiryDate: '2025-01-15',
          issuedBy: 'Dr. Sharma, City Hospital',
          status: 'valid'
        },
        eyeTestDate: '2024-01-15',
        nextEyeTest: '2025-01-15',
        bloodGroup: 'B+',
        fitnessStatus: 'fit'
      },
      documents: [
        {
          type: 'Driving License',
          fileName: 'license_rajesh_kumar.pdf',
          uploadDate: '2020-03-15',
          expiryDate: '2025-03-14',
          verified: true
        },
        {
          type: 'Medical Certificate',
          fileName: 'medical_cert_2024.pdf',
          uploadDate: '2024-01-15',
          expiryDate: '2025-01-15',
          verified: true
        }
      ],
      violations: [
        {
          date: '2023-11-20',
          type: 'traffic',
          description: 'Minor speeding violation - resolved with warning',
          severity: 'minor',
          action: 'Verbal Warning',
          resolved: true
        }
      ],
      lastUpdated: '2024-02-05',
      createdDate: '2020-03-15'
    },
    {
      id: '2',
      driverId: 'DRV-002',
      personalInfo: {
        firstName: 'Amit',
        lastName: 'Sharma',
        fullName: 'Amit Sharma',
        dateOfBirth: '1985-08-22',
        age: 39,
        gender: 'male',
        phone: '+91-**********',
        email: '<EMAIL>',
        address: {
          street: '456 Transport Nagar',
          city: 'Delhi',
          state: 'Delhi',
          pincode: '110002'
        },
        emergencyContact: {
          name: 'Priya Sharma',
          relationship: 'Wife',
          phone: '+91-**********'
        }
      },
      employment: {
        employeeId: 'EMP-DRV-002',
        joiningDate: '2021-06-10',
        employmentType: 'full_time',
        salary: 25000,
        workingHours: 6,
        shift: 'morning',
        status: 'active'
      },
      licensing: {
        licenseNumber: 'DL-02-20210610-002',
        licenseType: ['Light Vehicle', 'Heavy Vehicle'],
        issueDate: '2019-06-10',
        expiryDate: '2026-06-09',
        issuingAuthority: 'Delhi Transport Department',
        endorsements: ['Commercial Vehicle'],
        status: 'valid'
      },
      experience: {
        totalYears: 8,
        schoolBusExperience: 3,
        previousEmployers: [
          {
            company: 'Private Transport Company',
            position: 'Driver',
            duration: '2018-2021',
            reason: 'Better Benefits'
          }
        ],
        trainingCertificates: [
          {
            name: 'Safe Driving Course',
            issuedBy: 'Transport Training Institute',
            issueDate: '2023-03-20',
            expiryDate: '2026-03-20',
            verified: true
          }
        ]
      },
      assignment: {
        vehicleId: 'VEH-2024-002',
        vehicleNumber: 'DL-02-CD-5678',
        routeId: 'RT-002',
        routeName: 'Route B - South Delhi',
        workingDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        startTime: '07:00',
        endTime: '13:00'
      },
      performance: {
        safetyRating: 4.5,
        punctualityRating: 4.8,
        studentFeedbackRating: 4.3,
        supervisorRating: 4.4,
        overallRating: 4.5,
        totalTrips: 850,
        onTimeTrips: 820,
        incidentCount: 0,
        complaintCount: 1,
        commendationCount: 3
      },
      health: {
        medicalCertificate: {
          issueDate: '2024-01-20',
          expiryDate: '2025-01-20',
          issuedBy: 'Dr. Patel, Medical Center',
          status: 'valid'
        },
        eyeTestDate: '2024-01-20',
        nextEyeTest: '2025-01-20',
        bloodGroup: 'A+',
        fitnessStatus: 'fit'
      },
      documents: [
        {
          type: 'Driving License',
          fileName: 'license_amit_sharma.pdf',
          uploadDate: '2021-06-10',
          expiryDate: '2026-06-09',
          verified: true
        }
      ],
      violations: [],
      lastUpdated: '2024-02-03',
      createdDate: '2021-06-10'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-800'
      case 'on_leave':
        return 'bg-yellow-100 text-yellow-800'
      case 'suspended':
        return 'bg-red-100 text-red-800'
      case 'terminated':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getShiftColor = (shift: string) => {
    switch (shift) {
      case 'morning':
        return 'bg-blue-100 text-blue-800'
      case 'afternoon':
        return 'bg-orange-100 text-orange-800'
      case 'both':
        return 'bg-purple-100 text-purple-800'
      case 'flexible':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getLicenseStatusColor = (status: string) => {
    switch (status) {
      case 'valid':
        return 'bg-green-100 text-green-800'
      case 'expired':
        return 'bg-red-100 text-red-800'
      case 'suspended':
        return 'bg-red-100 text-red-800'
      case 'pending_renewal':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getFitnessStatusColor = (status: string) => {
    switch (status) {
      case 'fit':
        return 'bg-green-100 text-green-800'
      case 'unfit':
        return 'bg-red-100 text-red-800'
      case 'conditional':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getViolationSeverityColor = (severity: string) => {
    switch (severity) {
      case 'minor':
        return 'bg-yellow-100 text-yellow-800'
      case 'major':
        return 'bg-orange-100 text-orange-800'
      case 'critical':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredDrivers = drivers.filter(driver => {
    const matchesSearch = driver.personalInfo.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         driver.driverId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         driver.personalInfo.phone.includes(searchTerm) ||
                         driver.assignment.vehicleNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         driver.assignment.routeName?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || driver.employment.status === selectedStatus
    const matchesShift = selectedShift === 'all' || driver.employment.shift === selectedShift
    return matchesSearch && matchesStatus && matchesShift
  })

  const DriverDetailModal = () => {
    if (!selectedDriver) return null

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold">{selectedDriver.personalInfo.fullName}</h3>
            <button
              type="button"
              onClick={() => setSelectedDriver(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Personal Information</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Driver ID:</span> {selectedDriver.driverId}</div>
                <div><span className="font-medium">Email:</span> {selectedDriver.personalInfo.email}</div>
                <div><span className="font-medium">Phone:</span> {selectedDriver.personalInfo.phone}</div>
                <div><span className="font-medium">Date of Birth:</span> {selectedDriver.personalInfo.dateOfBirth}</div>
                <div><span className="font-medium">Age:</span> {selectedDriver.personalInfo.age}</div>
                <div><span className="font-medium">Gender:</span> {selectedDriver.personalInfo.gender}</div>
                <div><span className="font-medium">Status:</span>
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getStatusColor(selectedDriver.employment.status)}`}>
                    {selectedDriver.employment.status.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Employment Information</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Employee ID:</span> {selectedDriver.employment.employeeId}</div>
                <div><span className="font-medium">Joining Date:</span> {selectedDriver.employment.joiningDate}</div>
                <div><span className="font-medium">Employment Type:</span> {selectedDriver.employment.employmentType}</div>
                <div><span className="font-medium">Salary:</span> ₹{selectedDriver.employment.salary.toLocaleString()}</div>
                <div><span className="font-medium">Working Hours:</span> {selectedDriver.employment.workingHours} hours</div>
                <div><span className="font-medium">Shift:</span> {selectedDriver.employment.shift}</div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Assignment Details</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Vehicle:</span> {selectedDriver.assignment.vehicleNumber}</div>
                <div><span className="font-medium">Route:</span> {selectedDriver.assignment.routeName}</div>
                <div><span className="font-medium">Conductor:</span> {selectedDriver.assignment.conductorName}</div>
                <div><span className="font-medium">Working Days:</span> {selectedDriver.assignment.workingDays.join(', ')}</div>
                <div><span className="font-medium">Shift Time:</span> {selectedDriver.assignment.startTime} - {selectedDriver.assignment.endTime}</div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Performance Metrics</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Overall Rating:</span> {selectedDriver.performance.overallRating}/5</div>
                <div><span className="font-medium">Safety Rating:</span> {selectedDriver.performance.safetyRating}/5</div>
                <div><span className="font-medium">Punctuality:</span> {selectedDriver.performance.punctualityRating}/5</div>
                <div><span className="font-medium">Total Trips:</span> {selectedDriver.performance.totalTrips}</div>
                <div><span className="font-medium">On-time Trips:</span> {selectedDriver.performance.onTimeTrips}</div>
                <div><span className="font-medium">Incidents:</span> {selectedDriver.performance.incidentCount}</div>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => onEditDriver?.(selectedDriver.id)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Edit Driver
            </button>
            <button
              type="button"
              onClick={() => onAssignRoute?.(selectedDriver.id, 'new-route')}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
            >
              Assign Route
            </button>
            <button
              type="button"
              onClick={() => onUpdateStatus?.(selectedDriver.id, 'active')}
              className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
            >
              Update Status
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Driver Management</h1>
          <p className="text-gray-600">Manage drivers and their assignments</p>
        </div>
        <button
          type="button"
          onClick={() => onAddDriver?.()}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          + Add Driver
        </button>
      </div>

      {/* Summary Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Drivers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{drivers.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Active Drivers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {drivers.filter(d => d.employment.status === 'active').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {(drivers.reduce((sum, d) => sum + d.performance.overallRating, 0) / drivers.length).toFixed(1)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Incidents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {drivers.reduce((sum, d) => sum + d.performance.incidentCount, 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
              <input
                type="text"
                placeholder="Search by name, ID, phone, vehicle, or route"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label htmlFor="status-select" className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                id="status-select"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="on_leave">On Leave</option>
                <option value="suspended">Suspended</option>
                <option value="terminated">Terminated</option>
              </select>
            </div>
            <div>
              <label htmlFor="shift-select" className="block text-sm font-medium text-gray-700 mb-2">Shift</label>
              <select
                id="shift-select"
                value={selectedShift}
                onChange={(e) => setSelectedShift(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Shifts</option>
                <option value="morning">Morning</option>
                <option value="afternoon">Afternoon</option>
                <option value="both">Both</option>
                <option value="flexible">Flexible</option>
              </select>
            </div>
            <div className="flex items-end">
              <button type="button" className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200">
                Export Drivers
              </button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Drivers Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredDrivers.map((driver) => (
          <Card key={driver.id} className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{driver.personalInfo.fullName}</CardTitle>
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(driver.employment.status)}`}>
                  {driver.employment.status.toUpperCase()}
                </span>
              </div>
              <CardDescription>{driver.driverId} • {driver.employment.shift} shift</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Vehicle:</span>
                  <span className="font-medium">{driver.assignment.vehicleNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Route:</span>
                  <span className="font-medium">{driver.assignment.routeName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Rating:</span>
                  <span className="font-medium">{driver.performance.overallRating}/5</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Trips:</span>
                  <span className="font-medium">{driver.performance.totalTrips}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Phone:</span>
                  <span className="font-medium">{driver.personalInfo.phone}</span>
                </div>
              </div>

              <div className="mt-4 flex items-center justify-between">
                <div className="flex space-x-2">
                  {driver.performance.incidentCount > 0 && (
                    <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                      {driver.performance.incidentCount} Incidents
                    </span>
                  )}
                  {driver.violations.some(v => !v.resolved) && (
                    <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">
                      Violations
                    </span>
                  )}
                </div>
                <button
                  type="button"
                  onClick={() => setSelectedDriver(driver)}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  View Details
                </button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Driver Detail Modal */}
      {selectedDriver && <DriverDetailModal />}
    </div>
  )
}