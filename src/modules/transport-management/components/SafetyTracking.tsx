'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface SafetyIncident {
  id: string
  incidentId: string
  date: string
  time: string
  type: 'accident' | 'breakdown' | 'medical_emergency' | 'behavioral' | 'safety_violation' | 'route_deviation'
  severity: 'low' | 'medium' | 'high' | 'critical'
  vehicleId: string
  vehicleNumber: string
  routeId: string
  routeName: string
  driverId: string
  driverName: string
  location: {
    address: string
    coordinates: {
      latitude: number
      longitude: number
    }
    landmark?: string
  }
  description: string
  studentsInvolved: {
    studentId: string
    studentName: string
    class: string
    injuryStatus: 'none' | 'minor' | 'major'
    medicalAttention: boolean
  }[]
  witnesses: {
    name: string
    contact: string
    statement: string
  }[]
  emergencyServices: {
    called: boolean
    services: string[]
    arrivalTime?: string
    reportNumber?: string
  }
  parentNotification: {
    notified: boolean
    notificationTime?: string
    method: 'phone' | 'sms' | 'email' | 'in_person'
  }
  actionTaken: string[]
  followUpRequired: boolean
  followUpActions: string[]
  status: 'reported' | 'investigating' | 'resolved' | 'closed'
  reportedBy: string
  investigatedBy?: string
  resolution?: string
  documents: {
    type: string
    fileName: string
    uploadDate: string
  }[]
  lastUpdated: string
}

interface VehicleTracking {
  id: string
  vehicleId: string
  vehicleNumber: string
  routeId: string
  routeName: string
  driverId: string
  driverName: string
  currentLocation: {
    latitude: number
    longitude: number
    address: string
    timestamp: string
  }
  status: 'on_route' | 'at_stop' | 'delayed' | 'breakdown' | 'emergency' | 'off_duty'
  speed: number
  direction: string
  nextStop: {
    stopId: string
    stopName: string
    estimatedArrival: string
    distance: number
  }
  studentsOnBoard: number
  maxCapacity: number
  route: {
    completedStops: number
    totalStops: number
    progressPercentage: number
  }
  alerts: {
    type: 'speeding' | 'route_deviation' | 'emergency_button' | 'breakdown' | 'delay'
    message: string
    timestamp: string
    severity: 'low' | 'medium' | 'high'
  }[]
  lastUpdate: string
}

interface SafetyMetrics {
  totalIncidents: number
  incidentsByType: {
    [key: string]: number
  }
  incidentsBySeverity: {
    [key: string]: number
  }
  safetyScore: number
  averageResponseTime: number
  vehiclesSafetyCompliant: number
  totalVehicles: number
  driversWithViolations: number
  totalDrivers: number
  monthlyTrend: {
    month: string
    incidents: number
    safetyScore: number
  }[]
}

interface SafetyTrackingProps {
  tenantId: string
  onReportIncident?: () => void
  onInvestigateIncident?: (incidentId: string) => void
  onSendAlert?: (vehicleId: string, message: string) => void
  onEmergencyResponse?: (vehicleId: string) => void
}

export function SafetyTracking({ tenantId, onReportIncident, onInvestigateIncident, onSendAlert, onEmergencyResponse }: SafetyTrackingProps) {
  const [activeTab, setActiveTab] = useState('tracking')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedSeverity, setSelectedSeverity] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedIncident, setSelectedIncident] = useState<SafetyIncident | null>(null)

  // Mock data for safety incidents
  const safetyIncidents: SafetyIncident[] = [
    {
      id: '1',
      incidentId: 'INC-2024-001',
      date: '2024-02-05',
      time: '08:15',
      type: 'breakdown',
      severity: 'medium',
      vehicleId: 'VEH-2024-001',
      vehicleNumber: 'DL-01-AB-1234',
      routeId: 'RT-001',
      routeName: 'Route A - Central Delhi',
      driverId: 'DRV-001',
      driverName: 'Rajesh Kumar',
      location: {
        address: 'Karol Bagh Market, Delhi',
        coordinates: { latitude: 28.6519, longitude: 77.1909 },
        landmark: 'Near Metro Station'
      },
      description: 'Engine overheating caused the bus to stop. All students were safely evacuated and alternative transport arranged.',
      studentsInvolved: [],
      witnesses: [
        {
          name: 'Suresh Singh',
          contact: '+91-9876543211',
          statement: 'Driver handled the situation professionally and ensured student safety.'
        }
      ],
      emergencyServices: {
        called: true,
        services: ['Mechanic', 'Backup Vehicle'],
        arrivalTime: '08:30',
        reportNumber: 'MECH-2024-001'
      },
      parentNotification: {
        notified: true,
        notificationTime: '08:20',
        method: 'sms'
      },
      actionTaken: [
        'Students evacuated safely',
        'Backup vehicle arranged',
        'Parents notified',
        'Vehicle towed for repair'
      ],
      followUpRequired: true,
      followUpActions: [
        'Complete engine inspection',
        'Update maintenance schedule',
        'Driver counseling on emergency procedures'
      ],
      status: 'resolved',
      reportedBy: 'Rajesh Kumar (Driver)',
      investigatedBy: 'Transport Manager',
      resolution: 'Engine cooling system repaired. Vehicle back in service after safety inspection.',
      documents: [
        {
          type: 'Incident Report',
          fileName: 'incident_report_001.pdf',
          uploadDate: '2024-02-05'
        },
        {
          type: 'Repair Invoice',
          fileName: 'repair_invoice_001.pdf',
          uploadDate: '2024-02-06'
        }
      ],
      lastUpdated: '2024-02-06'
    },
    {
      id: '2',
      incidentId: 'INC-2024-002',
      date: '2024-02-03',
      time: '15:45',
      type: 'medical_emergency',
      severity: 'high',
      vehicleId: 'VEH-2024-002',
      vehicleNumber: 'DL-02-CD-5678',
      routeId: 'RT-002',
      routeName: 'Route B - South Delhi',
      driverId: 'DRV-002',
      driverName: 'Amit Sharma',
      location: {
        address: 'Defence Colony Market, Delhi',
        coordinates: { latitude: 28.5729, longitude: 77.2295 },
        landmark: 'Market Area'
      },
      description: 'Student experienced allergic reaction during journey. Immediate medical attention provided.',
      studentsInvolved: [
        {
          studentId: 'STU-2024-015',
          studentName: 'Arjun Patel',
          class: 'Grade 7',
          injuryStatus: 'minor',
          medicalAttention: true
        }
      ],
      witnesses: [
        {
          name: 'Priya Singh',
          contact: '+91-**********',
          statement: 'Student suddenly felt unwell and had difficulty breathing.'
        }
      ],
      emergencyServices: {
        called: true,
        services: ['Ambulance', 'School Nurse'],
        arrivalTime: '15:55',
        reportNumber: 'AMB-2024-002'
      },
      parentNotification: {
        notified: true,
        notificationTime: '15:50',
        method: 'phone'
      },
      actionTaken: [
        'First aid administered',
        'Ambulance called',
        'Parents contacted immediately',
        'Student taken to hospital',
        'Other students reassured'
      ],
      followUpRequired: true,
      followUpActions: [
        'Update student medical records',
        'Review emergency procedures',
        'Staff first aid training'
      ],
      status: 'investigating',
      reportedBy: 'Amit Sharma (Driver)',
      investigatedBy: 'Safety Officer',
      documents: [
        {
          type: 'Medical Report',
          fileName: 'medical_report_002.pdf',
          uploadDate: '2024-02-03'
        }
      ],
      lastUpdated: '2024-02-04'
    }
  ]

  // Mock data for vehicle tracking
  const vehicleTracking: VehicleTracking[] = [
    {
      id: '1',
      vehicleId: 'VEH-2024-001',
      vehicleNumber: 'DL-01-AB-1234',
      routeId: 'RT-001',
      routeName: 'Route A - Central Delhi',
      driverId: 'DRV-001',
      driverName: 'Rajesh Kumar',
      currentLocation: {
        latitude: 28.6519,
        longitude: 77.1909,
        address: 'Karol Bagh Market, Delhi',
        timestamp: '2024-02-05 14:30:00'
      },
      status: 'on_route',
      speed: 25,
      direction: 'Southeast',
      nextStop: {
        stopId: 'S3',
        stopName: 'Paharganj',
        estimatedArrival: '14:35',
        distance: 1.2
      },
      studentsOnBoard: 35,
      maxCapacity: 45,
      route: {
        completedStops: 2,
        totalStops: 4,
        progressPercentage: 50
      },
      alerts: [],
      lastUpdate: '2024-02-05 14:30:00'
    },
    {
      id: '2',
      vehicleId: 'VEH-2024-002',
      vehicleNumber: 'DL-02-CD-5678',
      routeId: 'RT-002',
      routeName: 'Route B - South Delhi',
      driverId: 'DRV-002',
      driverName: 'Amit Sharma',
      currentLocation: {
        latitude: 28.5678,
        longitude: 77.2434,
        address: 'Lajpat Nagar Central Market, Delhi',
        timestamp: '2024-02-05 14:25:00'
      },
      status: 'delayed',
      speed: 0,
      direction: 'Stationary',
      nextStop: {
        stopId: 'S6',
        stopName: 'Defence Colony',
        estimatedArrival: '14:40',
        distance: 2.1
      },
      studentsOnBoard: 8,
      maxCapacity: 12,
      route: {
        completedStops: 1,
        totalStops: 3,
        progressPercentage: 33
      },
      alerts: [
        {
          type: 'delay',
          message: 'Vehicle delayed due to traffic congestion',
          timestamp: '2024-02-05 14:25:00',
          severity: 'medium'
        }
      ],
      lastUpdate: '2024-02-05 14:25:00'
    }
  ]

  // Mock safety metrics
  const safetyMetrics: SafetyMetrics = {
    totalIncidents: 12,
    incidentsByType: {
      breakdown: 5,
      medical_emergency: 2,
      accident: 1,
      behavioral: 3,
      safety_violation: 1
    },
    incidentsBySeverity: {
      low: 6,
      medium: 4,
      high: 2,
      critical: 0
    },
    safetyScore: 87,
    averageResponseTime: 8.5,
    vehiclesSafetyCompliant: 18,
    totalVehicles: 20,
    driversWithViolations: 2,
    totalDrivers: 15,
    monthlyTrend: [
      { month: 'Oct', incidents: 8, safetyScore: 85 },
      { month: 'Nov', incidents: 6, safetyScore: 88 },
      { month: 'Dec', incidents: 4, safetyScore: 90 },
      { month: 'Jan', incidents: 7, safetyScore: 86 },
      { month: 'Feb', incidents: 5, safetyScore: 87 }
    ]
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on_route':
        return 'bg-green-100 text-green-800'
      case 'at_stop':
        return 'bg-blue-100 text-blue-800'
      case 'delayed':
        return 'bg-yellow-100 text-yellow-800'
      case 'breakdown':
        return 'bg-red-100 text-red-800'
      case 'emergency':
        return 'bg-red-100 text-red-800'
      case 'off_duty':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'critical':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getIncidentStatusColor = (status: string) => {
    switch (status) {
      case 'reported':
        return 'bg-blue-100 text-blue-800'
      case 'investigating':
        return 'bg-yellow-100 text-yellow-800'
      case 'resolved':
        return 'bg-green-100 text-green-800'
      case 'closed':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getAlertSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-blue-100 text-blue-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'high':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredIncidents = safetyIncidents.filter(incident => {
    const matchesSearch = incident.incidentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         incident.vehicleNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         incident.routeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         incident.driverName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         incident.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesSeverity = selectedSeverity === 'all' || incident.severity === selectedSeverity
    const matchesStatus = selectedStatus === 'all' || incident.status === selectedStatus
    return matchesSearch && matchesSeverity && matchesStatus
  })

  const IncidentDetailModal = () => {
    if (!selectedIncident) return null

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-5xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold">Incident {selectedIncident.incidentId}</h3>
            <button
              type="button"
              onClick={() => setSelectedIncident(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Incident Details</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Incident ID:</span> {selectedIncident.incidentId}</div>
                <div><span className="font-medium">Date & Time:</span> {selectedIncident.date} at {selectedIncident.time}</div>
                <div><span className="font-medium">Type:</span> {selectedIncident.type.replace('_', ' ').toUpperCase()}</div>
                <div><span className="font-medium">Severity:</span> 
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getSeverityColor(selectedIncident.severity)}`}>
                    {selectedIncident.severity.toUpperCase()}
                  </span>
                </div>
                <div><span className="font-medium">Status:</span> 
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getIncidentStatusColor(selectedIncident.status)}`}>
                    {selectedIncident.status.toUpperCase()}
                  </span>
                </div>
                <div><span className="font-medium">Reported By:</span> {selectedIncident.reportedBy}</div>
                {selectedIncident.investigatedBy && (
                  <div><span className="font-medium">Investigated By:</span> {selectedIncident.investigatedBy}</div>
                )}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Vehicle & Route</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Vehicle:</span> {selectedIncident.vehicleNumber}</div>
                <div><span className="font-medium">Route:</span> {selectedIncident.routeName}</div>
                <div><span className="font-medium">Driver:</span> {selectedIncident.driverName}</div>
                <div><span className="font-medium">Location:</span> {selectedIncident.location.address}</div>
                {selectedIncident.location.landmark && (
                  <div><span className="font-medium">Landmark:</span> {selectedIncident.location.landmark}</div>
                )}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Emergency Response</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Emergency Services:</span> {selectedIncident.emergencyServices.called ? '✅ Called' : '❌ Not Called'}</div>
                {selectedIncident.emergencyServices.called && (
                  <>
                    <div><span className="font-medium">Services:</span> {selectedIncident.emergencyServices.services.join(', ')}</div>
                    {selectedIncident.emergencyServices.arrivalTime && (
                      <div><span className="font-medium">Arrival Time:</span> {selectedIncident.emergencyServices.arrivalTime}</div>
                    )}
                    {selectedIncident.emergencyServices.reportNumber && (
                      <div><span className="font-medium">Report Number:</span> {selectedIncident.emergencyServices.reportNumber}</div>
                    )}
                  </>
                )}
                <div><span className="font-medium">Parents Notified:</span> {selectedIncident.parentNotification.notified ? '✅ Yes' : '❌ No'}</div>
                {selectedIncident.parentNotification.notified && (
                  <>
                    <div><span className="font-medium">Notification Time:</span> {selectedIncident.parentNotification.notificationTime}</div>
                    <div><span className="font-medium">Method:</span> {selectedIncident.parentNotification.method.toUpperCase()}</div>
                  </>
                )}
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h4 className="font-medium mb-3">Description</h4>
            <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">{selectedIncident.description}</p>
          </div>

          {selectedIncident.studentsInvolved.length > 0 && (
            <div className="mb-6">
              <h4 className="font-medium mb-3">Students Involved</h4>
              <div className="space-y-2">
                {selectedIncident.studentsInvolved.map((student, index) => (
                  <div key={index} className="border rounded p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{student.studentName}</div>
                        <div className="text-sm text-gray-600">{student.class} - {student.studentId}</div>
                      </div>
                      <div className="text-right">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          student.injuryStatus === 'none' ? 'bg-green-100 text-green-800' :
                          student.injuryStatus === 'minor' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {student.injuryStatus.toUpperCase()}
                        </span>
                        <div className="text-xs text-gray-500 mt-1">
                          Medical Attention: {student.medicalAttention ? 'Yes' : 'No'}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Action Taken</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                {selectedIncident.actionTaken.map((action, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>{action}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-3">Follow-up Actions</h4>
              {selectedIncident.followUpRequired ? (
                <ul className="text-sm text-gray-600 space-y-1">
                  {selectedIncident.followUpActions.map((action, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>{action}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-500">No follow-up required</p>
              )}
            </div>
          </div>

          {selectedIncident.witnesses.length > 0 && (
            <div className="mb-6">
              <h4 className="font-medium mb-3">Witnesses</h4>
              <div className="space-y-2">
                {selectedIncident.witnesses.map((witness, index) => (
                  <div key={index} className="border rounded p-3">
                    <div className="font-medium text-sm">{witness.name}</div>
                    <div className="text-sm text-gray-600">Contact: {witness.contact}</div>
                    <div className="text-sm text-gray-600 mt-1">Statement: {witness.statement}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {selectedIncident.resolution && (
            <div className="mb-6">
              <h4 className="font-medium mb-3">Resolution</h4>
              <p className="text-sm text-gray-600 bg-green-50 p-3 rounded">{selectedIncident.resolution}</p>
            </div>
          )}

          {selectedIncident.documents.length > 0 && (
            <div className="mb-6">
              <h4 className="font-medium mb-3">Documents</h4>
              <div className="space-y-2">
                {selectedIncident.documents.map((doc, index) => (
                  <div key={index} className="flex items-center justify-between border rounded p-2">
                    <div>
                      <span className="font-medium text-sm">{doc.type}</span>
                      <div className="text-xs text-gray-500">Uploaded: {doc.uploadDate}</div>
                    </div>
                    <button type="button" className="text-blue-600 hover:text-blue-800 text-sm">
                      Download
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3">
            {selectedIncident.status !== 'closed' && (
              <button
                type="button"
                onClick={() => onInvestigateIncident?.(selectedIncident.id)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Update Investigation
              </button>
            )}
            <button type="button" className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
              Generate Report
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Safety Tracking</h1>
        <button
          type="button"
          onClick={onReportIncident}
          className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
        >
          Report Incident
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            type="button"
            onClick={() => setActiveTab('tracking')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'tracking'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Live Tracking
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('incidents')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'incidents'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Incidents
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('metrics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'metrics'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Safety Metrics
          </button>
        </nav>
      </div>

      {/* Live Tracking Tab */}
      {activeTab === 'tracking' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {vehicleTracking.map((vehicle) => (
              <Card key={vehicle.id}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{vehicle.vehicleNumber}</CardTitle>
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(vehicle.status)}`}>
                      {vehicle.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                  <CardDescription>{vehicle.routeName}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Driver:</span>
                      <span className="font-medium">{vehicle.driverName}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Students:</span>
                      <span className="font-medium">{vehicle.studentsOnBoard}/{vehicle.maxCapacity}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Speed:</span>
                      <span className="font-medium">{vehicle.speed} km/h</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Next Stop:</span>
                      <span className="font-medium">{vehicle.nextStop.stopName}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">ETA:</span>
                      <span className="font-medium">{vehicle.nextStop.estimatedArrival}</span>
                    </div>

                    {vehicle.alerts.length > 0 && (
                      <div className="mt-3 pt-3 border-t">
                        <div className="text-sm font-medium text-red-600 mb-2">Active Alerts:</div>
                        {vehicle.alerts.map((alert, index) => (
                          <div key={index} className="text-xs text-red-600 bg-red-50 p-2 rounded">
                            {alert.message}
                          </div>
                        ))}
                      </div>
                    )}

                    <div className="flex space-x-2 mt-4">
                      <button
                        type="button"
                        onClick={() => onSendAlert?.(vehicle.vehicleId, 'Safety check required')}
                        className="flex-1 bg-yellow-600 text-white px-3 py-1 text-xs rounded hover:bg-yellow-700"
                      >
                        Send Alert
                      </button>
                      {vehicle.status === 'emergency' && (
                        <button
                          type="button"
                          onClick={() => onEmergencyResponse?.(vehicle.vehicleId)}
                          className="flex-1 bg-red-600 text-white px-3 py-1 text-xs rounded hover:bg-red-700"
                        >
                          Emergency
                        </button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Incidents Tab */}
      {activeTab === 'incidents' && (
        <div className="space-y-6">
          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <input
                type="text"
                placeholder="Search incidents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <select
              aria-label="Filter by severity"
              value={selectedSeverity}
              onChange={(e) => setSelectedSeverity(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Severities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
            <select
              aria-label="Filter by status"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Statuses</option>
              <option value="reported">Reported</option>
              <option value="investigating">Investigating</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
            </select>
          </div>

          {/* Incidents List */}
          <div className="space-y-4">
            {filteredIncidents.map((incident) => (
              <Card key={incident.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <h3 className="font-semibold">{incident.incidentId}</h3>
                      <span className={`px-2 py-1 text-xs rounded-full ${getSeverityColor(incident.severity)}`}>
                        {incident.severity.toUpperCase()}
                      </span>
                      <span className="text-sm text-gray-600">
                        {incident.type.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">
                      {incident.date} {incident.time}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                    <div>
                      <div className="text-sm text-gray-600">Vehicle</div>
                      <div className="font-medium">{incident.vehicleNumber}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Route</div>
                      <div className="font-medium">{incident.routeName}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Driver</div>
                      <div className="font-medium">{incident.driverName}</div>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-3">{incident.description}</p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="text-gray-600">
                        Status: <span className="font-medium">{incident.status.replace('_', ' ').toUpperCase()}</span>
                      </span>
                      {incident.studentsInvolved.length > 0 && (
                        <span className="text-orange-600">
                          {incident.studentsInvolved.length} student(s) involved
                        </span>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => setSelectedIncident(incident)}
                      className="bg-blue-600 text-white px-3 py-1 text-sm rounded hover:bg-blue-700"
                    >
                      View Details
                    </button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Safety Metrics Tab */}
      {activeTab === 'metrics' && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Total Incidents</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{safetyMetrics.totalIncidents}</div>
                <p className="text-xs text-gray-500">This month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Safety Score</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{safetyMetrics.safetyScore}%</div>
                <p className="text-xs text-gray-500">Fleet average</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Response Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{safetyMetrics.averageResponseTime} min</div>
                <p className="text-xs text-gray-500">Average</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Compliance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {safetyMetrics.vehiclesSafetyCompliant}/{safetyMetrics.totalVehicles}
                </div>
                <p className="text-xs text-gray-500">Vehicles compliant</p>
              </CardContent>
            </Card>
          </div>

          {/* Incident Breakdown */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Incidents by Type</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(safetyMetrics.incidentsByType).map(([type, count]) => (
                    <div key={type} className="flex items-center justify-between">
                      <span className="text-sm capitalize">{type.replace('_', ' ')}</span>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Incidents by Severity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(safetyMetrics.incidentsBySeverity).map(([severity, count]) => (
                    <div key={severity} className="flex items-center justify-between">
                      <span className={`text-sm px-2 py-1 rounded-full ${getSeverityColor(severity)}`}>
                        {severity.toUpperCase()}
                      </span>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Incident Detail Modal */}
      <IncidentDetailModal />
    </div>
  )
}