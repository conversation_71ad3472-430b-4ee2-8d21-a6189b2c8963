'use client'

import React, { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface Route {
  id: string
  routeId: string
  routeName: string
  description: string
  area: string
  startLocation: {
    name: string
    address: string
    coordinates: {
      latitude: number
      longitude: number
    }
  }
  endLocation: {
    name: string
    address: string
    coordinates: {
      latitude: number
      longitude: number
    }
  }
  stops: {
    id: string
    name: string
    address: string
    coordinates: {
      latitude: number
      longitude: number
    }
    estimatedTime: string
    studentsCount: number
    landmark?: string
    stopType: 'pickup' | 'drop' | 'both'
    sequence: number
  }[]
  schedule: {
    morningPickup: {
      startTime: string
      endTime: string
      estimatedDuration: number
    }
    afternoonDrop: {
      startTime: string
      endTime: string
      estimatedDuration: number
    }
  }
  distance: {
    totalKm: number
    estimatedTime: number
    fuelConsumption: number
  }
  vehicleAssignment: {
    vehicleId?: string
    vehicleNumber?: string
    driverId?: string
    driverName?: string
    conductorId?: string
    conductorName?: string
  }
  capacity: {
    maxStudents: number
    currentStudents: number
    availableSeats: number
  }
  operatingDays: string[]
  status: 'active' | 'inactive' | 'under_review' | 'suspended'
  safetyFeatures: {
    schoolZones: number
    trafficSignals: number
    speedBreakers: number
    riskLevel: 'low' | 'medium' | 'high'
    emergencyContacts: string[]
  }
  performance: {
    onTimePercentage: number
    averageDelay: number
    studentSatisfaction: number
    fuelEfficiency: number
    incidentCount: number
  }
  costs: {
    dailyOperatingCost: number
    monthlyFuelCost: number
    maintenanceCost: number
    driverSalary: number
    totalMonthlyCost: number
  }
  lastUpdated: string
  createdDate: string
}

interface RoutePlanningProps {
  tenantId: string
  onCreateRoute?: () => void
  onEditRoute?: (routeId: string) => void
  onOptimizeRoute?: (routeId: string) => void
  onAssignVehicle?: (routeId: string, vehicleId: string) => void
}

export function RoutePlanning({ tenantId, onCreateRoute, onEditRoute, onOptimizeRoute, onAssignVehicle }: RoutePlanningProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedArea, setSelectedArea] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedRoute, setSelectedRoute] = useState<Route | null>(null)

  // Mock data for routes
  const routes: Route[] = [
    {
      id: '1',
      routeId: 'RT-001',
      routeName: 'Route A - Central Delhi',
      description: 'Main route covering central Delhi residential areas',
      area: 'Central Delhi',
      startLocation: {
        name: 'School Campus',
        address: 'ABC School, Connaught Place, New Delhi',
        coordinates: { latitude: 28.6315, longitude: 77.2167 }
      },
      endLocation: {
        name: 'School Campus',
        address: 'ABC School, Connaught Place, New Delhi',
        coordinates: { latitude: 28.6315, longitude: 77.2167 }
      },
      stops: [
        {
          id: 'S1',
          name: 'Rajouri Garden Metro',
          address: 'Rajouri Garden Metro Station, Delhi',
          coordinates: { latitude: 28.6467, longitude: 77.1200 },
          estimatedTime: '07:15',
          studentsCount: 8,
          landmark: 'Near Metro Station',
          stopType: 'both',
          sequence: 1
        },
        {
          id: 'S2',
          name: 'Karol Bagh Market',
          address: 'Karol Bagh Market, Delhi',
          coordinates: { latitude: 28.6519, longitude: 77.1909 },
          estimatedTime: '07:25',
          studentsCount: 12,
          landmark: 'Main Market Area',
          stopType: 'both',
          sequence: 2
        },
        {
          id: 'S3',
          name: 'Paharganj',
          address: 'Paharganj Main Road, Delhi',
          coordinates: { latitude: 28.6414, longitude: 77.2085 },
          estimatedTime: '07:35',
          studentsCount: 6,
          landmark: 'Near Railway Station',
          stopType: 'both',
          sequence: 3
        },
        {
          id: 'S4',
          name: 'Connaught Place',
          address: 'Connaught Place, Central Delhi',
          coordinates: { latitude: 28.6304, longitude: 77.2177 },
          estimatedTime: '07:45',
          studentsCount: 12,
          landmark: 'Central Park',
          stopType: 'both',
          sequence: 4
        }
      ],
      schedule: {
        morningPickup: {
          startTime: '07:00',
          endTime: '08:00',
          estimatedDuration: 60
        },
        afternoonDrop: {
          startTime: '14:30',
          endTime: '15:30',
          estimatedDuration: 60
        }
      },
      distance: {
        totalKm: 25,
        estimatedTime: 60,
        fuelConsumption: 3.0
      },
      vehicleAssignment: {
        vehicleId: 'VEH-2024-001',
        vehicleNumber: 'DL-01-AB-1234',
        driverId: 'DRV-001',
        driverName: 'Rajesh Kumar',
        conductorId: 'CON-001',
        conductorName: 'Suresh Singh'
      },
      capacity: {
        maxStudents: 45,
        currentStudents: 38,
        availableSeats: 7
      },
      operatingDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
      status: 'active',
      safetyFeatures: {
        schoolZones: 3,
        trafficSignals: 8,
        speedBreakers: 12,
        riskLevel: 'medium',
        emergencyContacts: ['+91-9876543210', '+91-9876543211']
      },
      performance: {
        onTimePercentage: 92,
        averageDelay: 3.5,
        studentSatisfaction: 4.2,
        fuelEfficiency: 8.5,
        incidentCount: 1
      },
      costs: {
        dailyOperatingCost: 1200,
        monthlyFuelCost: 18000,
        maintenanceCost: 5000,
        driverSalary: 25000,
        totalMonthlyCost: 48000
      },
      lastUpdated: '2024-02-05',
      createdDate: '2023-06-15'
    },
    {
      id: '2',
      routeId: 'RT-002',
      routeName: 'Route B - South Delhi',
      description: 'Route covering South Delhi residential areas',
      area: 'South Delhi',
      startLocation: {
        name: 'School Campus',
        address: 'ABC School, Connaught Place, New Delhi',
        coordinates: { latitude: 28.6315, longitude: 77.2167 }
      },
      endLocation: {
        name: 'School Campus',
        address: 'ABC School, Connaught Place, New Delhi',
        coordinates: { latitude: 28.6315, longitude: 77.2167 }
      },
      stops: [
        {
          id: 'S5',
          name: 'Lajpat Nagar',
          address: 'Lajpat Nagar Central Market, Delhi',
          coordinates: { latitude: 28.5678, longitude: 77.2434 },
          estimatedTime: '07:20',
          studentsCount: 5,
          landmark: 'Central Market',
          stopType: 'both',
          sequence: 1
        },
        {
          id: 'S6',
          name: 'Defence Colony',
          address: 'Defence Colony Market, Delhi',
          coordinates: { latitude: 28.5729, longitude: 77.2295 },
          estimatedTime: '07:30',
          studentsCount: 3,
          landmark: 'Market Area',
          stopType: 'both',
          sequence: 2
        },
        {
          id: 'S7',
          name: 'Khan Market',
          address: 'Khan Market, Delhi',
          coordinates: { latitude: 28.5984, longitude: 77.2295 },
          estimatedTime: '07:40',
          studentsCount: 2,
          landmark: 'Main Market',
          stopType: 'both',
          sequence: 3
        }
      ],
      schedule: {
        morningPickup: {
          startTime: '07:15',
          endTime: '08:00',
          estimatedDuration: 45
        },
        afternoonDrop: {
          startTime: '14:30',
          endTime: '15:15',
          estimatedDuration: 45
        }
      },
      distance: {
        totalKm: 18,
        estimatedTime: 45,
        fuelConsumption: 1.5
      },
      vehicleAssignment: {
        vehicleId: 'VEH-2024-002',
        vehicleNumber: 'DL-02-CD-5678',
        driverId: 'DRV-002',
        driverName: 'Amit Sharma'
      },
      capacity: {
        maxStudents: 12,
        currentStudents: 10,
        availableSeats: 2
      },
      operatingDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      status: 'active',
      safetyFeatures: {
        schoolZones: 2,
        trafficSignals: 6,
        speedBreakers: 8,
        riskLevel: 'low',
        emergencyContacts: ['+91-9876543220']
      },
      performance: {
        onTimePercentage: 95,
        averageDelay: 2.0,
        studentSatisfaction: 4.5,
        fuelEfficiency: 12.0,
        incidentCount: 0
      },
      costs: {
        dailyOperatingCost: 800,
        monthlyFuelCost: 12000,
        maintenanceCost: 3000,
        driverSalary: 22000,
        totalMonthlyCost: 37000
      },
      lastUpdated: '2024-02-03',
      createdDate: '2023-08-20'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-800'
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800'
      case 'suspended':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'high':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStopTypeColor = (type: string) => {
    switch (type) {
      case 'pickup':
        return 'bg-blue-100 text-blue-800'
      case 'drop':
        return 'bg-purple-100 text-purple-800'
      case 'both':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredRoutes = routes.filter(route => {
    const matchesSearch = route.routeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         route.routeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         route.area.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         route.vehicleAssignment.vehicleNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         route.vehicleAssignment.driverName?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesArea = selectedArea === 'all' || route.area === selectedArea
    const matchesStatus = selectedStatus === 'all' || route.status === selectedStatus
    return matchesSearch && matchesArea && matchesStatus
  })

  const RouteDetailModal = () => {
    if (!selectedRoute) return null

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold">{selectedRoute.routeName}</h3>
            <button
              onClick={() => setSelectedRoute(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Route Information</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Route ID:</span> {selectedRoute.routeId}</div>
                <div><span className="font-medium">Area:</span> {selectedRoute.area}</div>
                <div><span className="font-medium">Total Distance:</span> {selectedRoute.distance.totalKm} km</div>
                <div><span className="font-medium">Estimated Time:</span> {selectedRoute.distance.estimatedTime} minutes</div>
                <div><span className="font-medium">Fuel Consumption:</span> {selectedRoute.distance.fuelConsumption} L/trip</div>
                <div><span className="font-medium">Status:</span>
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getStatusColor(selectedRoute.status)}`}>
                    {selectedRoute.status.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div><span className="font-medium">Operating Days:</span> {selectedRoute.operatingDays.length} days/week</div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Capacity & Assignment</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Max Capacity:</span> {selectedRoute.capacity.maxStudents}</div>
                <div><span className="font-medium">Current Students:</span> {selectedRoute.capacity.currentStudents}</div>
                <div><span className="font-medium">Available Seats:</span> {selectedRoute.capacity.availableSeats}</div>
                <div><span className="font-medium">Occupancy Rate:</span> {Math.round((selectedRoute.capacity.currentStudents / selectedRoute.capacity.maxStudents) * 100)}%</div>
                {selectedRoute.vehicleAssignment.vehicleNumber && (
                  <>
                    <div><span className="font-medium">Vehicle:</span> {selectedRoute.vehicleAssignment.vehicleNumber}</div>
                    <div><span className="font-medium">Driver:</span> {selectedRoute.vehicleAssignment.driverName}</div>
                    {selectedRoute.vehicleAssignment.conductorName && (
                      <div><span className="font-medium">Conductor:</span> {selectedRoute.vehicleAssignment.conductorName}</div>
                    )}
                  </>
                )}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Schedule</h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Morning Pickup:</span>
                  <div className="text-gray-600 ml-2">
                    {selectedRoute.schedule.morningPickup.startTime} - {selectedRoute.schedule.morningPickup.endTime}
                  </div>
                  <div className="text-gray-600 ml-2">
                    Duration: {selectedRoute.schedule.morningPickup.estimatedDuration} minutes
                  </div>
                </div>
                <div>
                  <span className="font-medium">Afternoon Drop:</span>
                  <div className="text-gray-600 ml-2">
                    {selectedRoute.schedule.afternoonDrop.startTime} - {selectedRoute.schedule.afternoonDrop.endTime}
                  </div>
                  <div className="text-gray-600 ml-2">
                    Duration: {selectedRoute.schedule.afternoonDrop.estimatedDuration} minutes
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h4 className="font-medium mb-3">Description</h4>
            <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">{selectedRoute.description}</p>
          </div>

          <div className="mb-6">
            <h4 className="font-medium mb-3">Route Stops ({selectedRoute.stops.length})</h4>
            <div className="space-y-3">
              {selectedRoute.stops.map((stop, index) => (
                <div key={stop.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                        Stop {stop.sequence}
                      </span>
                      <h5 className="font-medium">{stop.name}</h5>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStopTypeColor(stop.stopType)}`}>
                        {stop.stopType.toUpperCase()}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {stop.studentsCount} students • {stop.estimatedTime}
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    <div>{stop.address}</div>
                    {stop.landmark && <div className="text-xs text-gray-500">Landmark: {stop.landmark}</div>}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Safety Features</h4>
              <div className="space-y-2 text-sm bg-gray-50 p-3 rounded">
                <div><span className="font-medium">School Zones:</span> {selectedRoute.safetyFeatures.schoolZones}</div>
                <div><span className="font-medium">Traffic Signals:</span> {selectedRoute.safetyFeatures.trafficSignals}</div>
                <div><span className="font-medium">Speed Breakers:</span> {selectedRoute.safetyFeatures.speedBreakers}</div>
                <div><span className="font-medium">Risk Level:</span>
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getRiskLevelColor(selectedRoute.safetyFeatures.riskLevel)}`}>
                    {selectedRoute.safetyFeatures.riskLevel.toUpperCase()}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Emergency Contacts:</span>
                  <div className="mt-1">
                    {selectedRoute.safetyFeatures.emergencyContacts.map((contact, index) => (
                      <div key={index} className="text-gray-600">{contact}</div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Performance Metrics</h4>
              <div className="space-y-2 text-sm bg-gray-50 p-3 rounded">
                <div><span className="font-medium">On-Time Performance:</span> {selectedRoute.performance.onTimePercentage}%</div>
                <div><span className="font-medium">Average Delay:</span> {selectedRoute.performance.averageDelay} minutes</div>
                <div><span className="font-medium">Student Satisfaction:</span> ⭐ {selectedRoute.performance.studentSatisfaction}/5</div>
                <div><span className="font-medium">Fuel Efficiency:</span> {selectedRoute.performance.fuelEfficiency} km/l</div>
                <div><span className="font-medium">Incident Count:</span> {selectedRoute.performance.incidentCount} (this month)</div>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h4 className="font-medium mb-3">Cost Analysis</h4>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
              <div className="bg-blue-50 p-3 rounded">
                <div className="font-medium text-blue-800">Daily Operating</div>
                <div className="text-lg font-bold text-blue-600">₹{selectedRoute.costs.dailyOperatingCost}</div>
              </div>
              <div className="bg-green-50 p-3 rounded">
                <div className="font-medium text-green-800">Monthly Fuel</div>
                <div className="text-lg font-bold text-green-600">₹{selectedRoute.costs.monthlyFuelCost.toLocaleString()}</div>
              </div>
              <div className="bg-yellow-50 p-3 rounded">
                <div className="font-medium text-yellow-800">Maintenance</div>
                <div className="text-lg font-bold text-yellow-600">₹{selectedRoute.costs.maintenanceCost.toLocaleString()}</div>
              </div>
              <div className="bg-purple-50 p-3 rounded">
                <div className="font-medium text-purple-800">Driver Salary</div>
                <div className="text-lg font-bold text-purple-600">₹{selectedRoute.costs.driverSalary.toLocaleString()}</div>
              </div>
              <div className="bg-red-50 p-3 rounded">
                <div className="font-medium text-red-800">Total Monthly</div>
                <div className="text-lg font-bold text-red-600">₹{selectedRoute.costs.totalMonthlyCost.toLocaleString()}</div>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h4 className="font-medium mb-3">Operating Days</h4>
            <div className="flex flex-wrap gap-2">
              {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                <span
                  key={day}
                  className={`px-3 py-1 text-sm rounded-full ${
                    selectedRoute.operatingDays.includes(day)
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-500'
                  }`}
                >
                  {day}
                </span>
              ))}
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => onOptimizeRoute?.(selectedRoute.id)}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
            >
              Optimize Route
            </button>
            <button
              onClick={() => onEditRoute?.(selectedRoute.id)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Edit Route
            </button>
            <button className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
              Generate Report
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Route Planning & Management</h1>
          <p className="text-gray-600">Manage and optimize school transport routes</p>
        </div>
        <button
          onClick={() => onCreateRoute?.()}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
        >
          <span>+</span>
          <span>Create New Route</span>
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search Routes</label>
            <input
              type="text"
              placeholder="Search by route name, ID, area, vehicle..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label htmlFor="area-select" className="block text-sm font-medium text-gray-700 mb-1">Area</label>
            <select
              id="area-select"
              value={selectedArea}
              onChange={(e) => setSelectedArea(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Areas</option>
              <option value="Central Delhi">Central Delhi</option>
              <option value="South Delhi">South Delhi</option>
              <option value="North Delhi">North Delhi</option>
              <option value="East Delhi">East Delhi</option>
              <option value="West Delhi">West Delhi</option>
            </select>
          </div>
          <div>
            <label htmlFor="status-select" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              id="status-select"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="under_review">Under Review</option>
              <option value="suspended">Suspended</option>
            </select>
          </div>
          <div className="flex items-end">
            <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200">
              Export Routes
            </button>
          </div>
        </div>
      </div>

      {/* Routes Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredRoutes.map((route) => (
          <Card key={route.id} className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">{route.routeName}</CardTitle>
                  <CardDescription className="text-sm">{route.routeId} • {route.area}</CardDescription>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(route.status)}`}>
                  {route.status.replace('_', ' ').toUpperCase()}
                </span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Distance:</span>
                    <div className="text-gray-600">{route.distance.totalKm} km</div>
                  </div>
                  <div>
                    <span className="font-medium">Duration:</span>
                    <div className="text-gray-600">{route.distance.estimatedTime} min</div>
                  </div>
                  <div>
                    <span className="font-medium">Students:</span>
                    <div className="text-gray-600">{route.capacity.currentStudents}/{route.capacity.maxStudents}</div>
                  </div>
                  <div>
                    <span className="font-medium">Stops:</span>
                    <div className="text-gray-600">{route.stops.length}</div>
                  </div>
                </div>

                <div className="border-t pt-3">
                  <div className="text-sm">
                    <span className="font-medium">Vehicle:</span>
                    <div className="text-gray-600">{route.vehicleAssignment.vehicleNumber || 'Not assigned'}</div>
                  </div>
                  <div className="text-sm mt-1">
                    <span className="font-medium">Driver:</span>
                    <div className="text-gray-600">{route.vehicleAssignment.driverName || 'Not assigned'}</div>
                  </div>
                </div>

                <div className="border-t pt-3">
                  <div className="flex items-center justify-between text-sm">
                    <div>
                      <span className="font-medium">Performance:</span>
                      <div className="text-gray-600">{route.performance.onTimePercentage}% on-time</div>
                    </div>
                    <div>
                      <span className="font-medium">Risk:</span>
                      <span className={`ml-1 px-2 py-1 text-xs rounded-full ${getRiskLevelColor(route.safetyFeatures.riskLevel)}`}>
                        {route.safetyFeatures.riskLevel.toUpperCase()}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="border-t pt-3 flex space-x-2">
                  <button
                    onClick={() => setSelectedRoute(route)}
                    className="flex-1 bg-blue-50 text-blue-700 px-3 py-2 rounded text-sm hover:bg-blue-100"
                  >
                    View Details
                  </button>
                  <button
                    onClick={() => onOptimizeRoute?.(route.id)}
                    className="bg-green-50 text-green-700 px-3 py-2 rounded text-sm hover:bg-green-100"
                  >
                    Optimize
                  </button>
                  <button
                    onClick={() => onEditRoute?.(route.id)}
                    className="bg-gray-50 text-gray-700 px-3 py-2 rounded text-sm hover:bg-gray-100"
                  >
                    Edit
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredRoutes.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">No routes found matching your criteria</div>
          <button
            onClick={() => onCreateRoute?.()}
            className="mt-4 bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
          >
            Create Your First Route
          </button>
        </div>
      )}

      <RouteDetailModal />
    </div>
  )
}