import Link from 'next/link'

export default function Home() {
  const modules = [
    {
      title: 'Student Admissions',
      description: 'Manage student applications, enrollment, and admissions workflow',
      href: '/admissions',
      icon: '👥',
      color: 'bg-blue-500'
    },
    {
      title: 'Academic Management',
      description: 'Curriculum, assessments, grading, and student progress tracking',
      href: '/academic',
      icon: '📚',
      color: 'bg-green-500'
    },
    {
      title: 'Student Financials',
      description: 'Fee management, billing, payments, and financial reporting',
      href: '/financials',
      icon: '💰',
      color: 'bg-yellow-500'
    },
    {
      title: 'Library Management',
      description: 'Digital and physical library with circulation and inventory',
      href: '/library',
      icon: '📖',
      color: 'bg-purple-500'
    },
    {
      title: 'Alumni Engagement',
      description: 'Alumni directory, events, job board, and community management',
      href: '/alumni',
      icon: '🎓',
      color: 'bg-indigo-500'
    },
    {
      title: 'Hostel Management',
      description: 'Room allocation, resident tracking, and hostel operations',
      href: '/hostel',
      icon: '🏠',
      color: 'bg-red-500'
    },
    {
      title: 'Teacher Management',
      description: 'Teacher profiles, evaluations, and professional development',
      href: '/teachers',
      icon: '👨‍🏫',
      color: 'bg-teal-500'
    }
  ]

  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">EMS</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Employee Management System</h1>
                <p className="text-sm text-gray-600">Multi-Tenant Modular Architecture</p>
              </div>
            </div>
            <Link
              href="/dashboard"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Go to Dashboard
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Complete School Management Solution
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive multi-tenant platform with 7 integrated modules for complete educational institution management
          </p>
        </div>

        {/* Modules Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
          {modules.map((module, index) => (
            <Link
              key={index}
              href={module.href}
              className="group bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
            >
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className={`w-12 h-12 ${module.color} rounded-lg flex items-center justify-center text-white text-2xl mr-4`}>
                    {module.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {module.title}
                  </h3>
                </div>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {module.description}
                </p>
                <div className="mt-4 flex items-center text-blue-600 text-sm font-medium">
                  <span>Explore Module</span>
                  <svg className="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Features Section */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Key Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🏢</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Multi-Tenant</h4>
              <p className="text-sm text-gray-600">Complete data isolation between institutions</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🔧</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Modular</h4>
              <p className="text-sm text-gray-600">Enable/disable modules per tenant needs</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🔒</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Secure</h4>
              <p className="text-sm text-gray-600">Role-based access control and permissions</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📱</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Responsive</h4>
              <p className="text-sm text-gray-600">Works seamlessly on all devices</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
