import Link from 'next/link'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-6xl font-bold text-gray-900 mb-8">
            EMS Platform
          </h1>
          <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">
            Employee Management System - Multi-Tenant Modular Architecture
          </p>

          <div className="space-y-4 mb-12">
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded max-w-md mx-auto">
              ✅ Deployment Successful
            </div>
            <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded max-w-md mx-auto">
              ✅ Next.js 14 App Router
            </div>
            <div className="bg-purple-100 border border-purple-400 text-purple-700 px-4 py-3 rounded max-w-md mx-auto">
              ✅ TypeScript & Tailwind CSS
            </div>
          </div>

          <div className="space-x-4">
            <Link
              href="/dashboard"
              className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-block"
            >
              Go to Dashboard
            </Link>
            <Link
              href="/test"
              className="bg-gray-600 text-white px-8 py-3 rounded-lg hover:bg-gray-700 transition-colors inline-block"
            >
              Test Page
            </Link>
          </div>

          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-2">🏫 Multi-Tenant</h3>
              <p className="text-gray-600">Complete data isolation between institutions</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-2">🔧 Modular</h3>
              <p className="text-gray-600">Enable/disable modules per tenant needs</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-2">🔒 Secure</h3>
              <p className="text-gray-600">Role-based access control and permissions</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
