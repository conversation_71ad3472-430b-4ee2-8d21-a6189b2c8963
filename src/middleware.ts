import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const url = req.nextUrl.clone()

  // Skip middleware for static files, API routes, and health checks
  if (
    url.pathname.startsWith('/_next') ||
    url.pathname.startsWith('/api') ||
    url.pathname.includes('.') ||
    url.pathname === '/favicon.ico'
  ) {
    return res
  }

  // Get the hostname to determine tenant
  const hostname = req.headers.get('host') || ''

  // Skip tenant resolution for main domain and localhost
  if (
    hostname.includes('localhost') ||
    hostname === 'ems-platform.vercel.app' ||
    hostname.includes('vercel.app') ||
    url.pathname.startsWith('/auth') ||
    url.pathname === '/' ||
    url.pathname === '/test'
  ) {
    // For public routes, just continue without auth check
    if (url.pathname === '/' || url.pathname === '/test' || url.pathname.startsWith('/auth')) {
      return res
    }
  }

  // Try to get session, but don't fail if Supabase is not configured
  let session = null
  try {
    const supabase = createMiddlewareClient({ req, res })
    const { data } = await supabase.auth.getSession()
    session = data.session
  } catch (error) {
    console.log('Supabase middleware error:', error)
    // Continue without session for development
  }

  // Protected routes that require authentication
  const protectedPaths = [
    '/dashboard',
    '/admissions',
    '/academic',
    '/financials',
    '/library',
    '/alumni',
    '/hostel',
    '/teachers',
    '/transport',
    '/settings',
    '/profile'
  ]

  const isProtectedPath = protectedPaths.some(path =>
    url.pathname.startsWith(path)
  )

  // For development, allow access to protected routes without authentication
  if (process.env.NODE_ENV === 'development') {
    return res
  }

  // Redirect to login if accessing protected route without session
  if (isProtectedPath && !session) {
    const redirectUrl = new URL('/auth/login', req.url)
    redirectUrl.searchParams.set('redirectTo', url.pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // Redirect to dashboard if accessing auth routes with active session
  if (url.pathname.startsWith('/auth') && session) {
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
