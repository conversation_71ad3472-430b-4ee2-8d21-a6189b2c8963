# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://vurkebfevfaerzbjrsac.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ1cmtlYmZldmZhZXJ6Ympyc2FjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMjE0OTgsImV4cCI6MjA2NzY5NzQ5OH0._id645i-Tutxxut-75Rb8b14annuv31J4WxFZIneA5U
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Database Configuration
DATABASE_URL=your_database_connection_string

# Authentication
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="EMS - Employee Management System"

# Multi-tenancy
NEXT_PUBLIC_ENABLE_MULTI_TENANCY=true
NEXT_PUBLIC_DEFAULT_TENANT=demo

# AI Configuration (for future AI features)
OPENAI_API_KEY=your_openai_api_key

# Email Configuration
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=<EMAIL>

# File Storage
NEXT_PUBLIC_STORAGE_BUCKET=ems-files

# Payment Gateway (for future payment features)
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# Analytics (optional)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_ga_id
