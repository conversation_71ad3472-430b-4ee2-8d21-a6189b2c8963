{"name": "ems-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 -p 3001", "dev:network": "next dev --hostname 0.0.0.0 --port 3000", "dev:all": "HOSTNAME=0.0.0.0 PORT=3000 next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@supabase/supabase-js": "^2.38.5", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "lucide-react": "^0.294.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "date-fns": "^2.30.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "zustand": "^4.4.7", "react-query": "^3.39.3", "@tanstack/react-query": "^5.8.4", "recharts": "^2.8.0", "react-hot-toast": "^2.4.1"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.0.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7"}}