/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['localhost', 'https://5be29360dae9.ngrok-free.app'],
  },
  env: {
    CUSTOM_KEY: 'my-value',
  },
  distDir: 'dist',
  // Allow access from any host
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
        ],
      },
    ]
  },
}

module.exports = nextConfig
